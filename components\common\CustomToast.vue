<template>

    <view v-if="visible" class="toast-overlay" @click="handleOverlayClick">
        <!-- 确认对话框样式 -->
        <view v-if="type === 'confirm'" class="confirm-dialog" :class="animationClass" @click.stop>
            <!-- 标题 -->
            <view class="dialog-header" v-if="title">
                <text class="dialog-title">{{ title }}</text>
            </view>

            <!-- 内容 -->
            <view class="dialog-content">
                <text class="dialog-message">{{ message }}</text>
            </view>

            <!-- 按钮组 -->
            <view class="dialog-buttons">
                <button class="dialog-btn cancel-btn" @click="handleCancel">
                    {{ cancelText || '取消' }}
                </button>
                <button class="dialog-btn confirm-btn" @click="handleConfirm">
                    {{ confirmText || '确定' }}
                </button>
            </view>
        </view>

        <!-- 普通Toast样式 -->
        <view v-else class="toast-container" :class="[`toast-${type}`, animationClass]" @click.stop>
            <!-- 图标 -->
            <view class="toast-icon" v-if="showIcon">
                <text class="iconfont" :class="iconClass"></text>
            </view>

            <!-- 内容 -->
            <view class="toast-content">
                <text class="toast-title" v-if="title">{{ title }}</text>
                <text class="toast-message">{{ message }}</text>
            </view>

            <!-- 关闭按钮 -->
            <view class="toast-close" v-if="showClose" @click="hide">
                <text class="iconfont icon-close"></text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'

// Props
const props = defineProps({
    // 是否显示
    visible: {
        type: Boolean,
        default: false
    },
    // 消息内容
    message: {
        type: String,
        required: true
    },
    // 标题
    title: {
        type: String,
        default: ''
    },
    // 类型：success, error, warning, info, confirm
    type: {
        type: String,
        default: 'info',
        validator: (value) => ['success', 'error', 'warning', 'info', 'confirm'].includes(value)
    },
    // 持续时间（毫秒），0表示不自动关闭
    duration: {
        type: Number,
        default: 3000
    },
    // 是否显示图标
    showIcon: {
        type: Boolean,
        default: true
    },
    // 是否显示关闭按钮
    showClose: {
        type: Boolean,
        default: false
    },
    // 位置：top, center, bottom
    position: {
        type: String,
        default: 'center',
        validator: (value) => ['top', 'center', 'bottom'].includes(value)
    },
    // 确认按钮文字
    confirmText: {
        type: String,
        default: '确定'
    },
    // 取消按钮文字
    cancelText: {
        type: String,
        default: '取消'
    },
    // 点击遮罩是否关闭
    closeOnClickOverlay: {
        type: Boolean,
        default: true
    }
})

// Emits
const emit = defineEmits(['update:visible', 'close', 'confirm', 'cancel'])

// 响应式数据
const animationClass = ref('')
let timer = null

// 计算属性
const iconClass = computed(() => {
    const iconMap = {
        success: 'icon-check',
        error: 'icon-close',
        warning: 'icon-warning',
        info: 'icon-info'
    }
    return iconMap[props.type] || 'icon-info'
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
    if (newVal) {
        show()
    } else {
        animationClass.value = 'toast-fade-out'
    }
})

// 显示Toast
const show = () => {
    animationClass.value = 'toast-fade-in'

    // 确认对话框不自动关闭
    if (props.type !== 'confirm' && props.duration > 0) {
        clearTimeout(timer)
        timer = setTimeout(() => {
            hide()
        }, props.duration)
    }
}

// 隐藏Toast
const hide = () => {
    animationClass.value = 'toast-fade-out'
    clearTimeout(timer)

    setTimeout(() => {
        emit('update:visible', false)
        emit('close')
    }, 300) // 等待动画完成
}

// 处理遮罩点击
const handleOverlayClick = () => {
    if (props.closeOnClickOverlay && props.type !== 'confirm') {
        hide()
    }
}

// 处理确认按钮点击
const handleConfirm = () => {
    emit('confirm')
    hide()
}

// 处理取消按钮点击
const handleCancel = () => {
    emit('cancel')
    hide()
}

// 组件销毁时清理定时器
onUnmounted(() => {
    clearTimeout(timer)
})
</script>

<style lang="less" scoped>
@import '@/static/iconfont/iconfont.css';

.toast-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
}

/* 确认对话框样式 */
.confirm-dialog {
    width: 560rpx;
    background: #fff;
    border-radius: 24rpx;
    margin: 0 32rpx;
    overflow: hidden;
    box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.2);
}

.dialog-header {
    padding: 48rpx 32rpx 24rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;
}

.dialog-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
}

.dialog-content {
    padding: 32rpx;
    text-align: center;
}

.dialog-message {
    font-size: 30rpx;
    color: #666;
    line-height: 1.6;
}

.dialog-buttons {
    display: flex;
    padding: 0 16rpx 16rpx 16rpx;
}

.dialog-btn {
    flex: 1;
    height: 88rpx;
    border: none;
    font-size: 32rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.cancel-btn {
    color: #333;
    background-color: #E5E5E5;
    border-radius: 12rpx;
    margin: 16rpx 8rpx 16rpx 16rpx;

    &:active {
        background-color: #D0D0D0;
    }
}

.confirm-btn {
    color: #fff;
    background-color: #FF4D4F;
    font-weight: 600;
    border-radius: 12rpx;
    margin: 16rpx 16rpx 16rpx 8rpx;

    &:active {
        background-color: #E53E3E;
    }
}

.toast-container {
    max-width: 600rpx;
    min-width: 300rpx;
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin: 0 32rpx;
    display: flex;
    align-items: flex-start;
    gap: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    position: relative;
}

.toast-icon {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    
    .iconfont {
        font-size: 28rpx;
        color: #fff;
    }
}

.toast-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.toast-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
}

.toast-message {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
}

.toast-close {
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    
    .iconfont {
        font-size: 24rpx;
        color: #999;
    }
}

/* 不同类型的样式 */
.toast-success {
    .toast-icon {
        background: linear-gradient(135deg, #52c41a, #73d13d);
    }
}

.toast-error {
    .toast-icon {
        background: linear-gradient(135deg, #ff4d4f, #ff7875);
    }
}

.toast-warning {
    .toast-icon {
        background: linear-gradient(135deg, #faad14, #ffc53d);
    }
}

.toast-info {
    .toast-icon {
        background: linear-gradient(135deg, #1890ff, #40a9ff);
    }
}

/* 动画 */
.toast-fade-in {
    animation: toastFadeIn 0.3s ease-out forwards;
}

.toast-fade-out {
    animation: toastFadeOut 0.3s ease-in forwards;
}

@keyframes toastFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-20rpx);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes toastFadeOut {
    0% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    100% {
        opacity: 0;
        transform: scale(0.8) translateY(-20rpx);
    }
}

/* 位置样式 */
.toast-overlay.position-top {
    align-items: flex-start;
    padding-top: 200rpx;
}

.toast-overlay.position-bottom {
    align-items: flex-end;
    padding-bottom: 200rpx;
}
</style>
