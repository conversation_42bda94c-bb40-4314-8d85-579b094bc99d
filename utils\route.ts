// 统一封装获取当前页面路由参数，避免直接使用 getCurrentPages 产生的警告
// 支持 H5、APP、小程序；优先使用 $page.options，其次使用 getEnterOptionsSync().query

export type RouteParamValue = string | undefined

export function getRouteParams(keys?: string[] | null): Record<string, RouteParamValue> {
  let params: Record<string, RouteParamValue> = {}

  try {
    // 方式1：通过当前页面实例
    const pages = getCurrentPages && getCurrentPages()
    const currentPage: any = pages && pages[pages.length - 1]
    const opt = currentPage?.$page?.options || currentPage?.options || {}

    if (opt && typeof opt === 'object') {
      params = { ...opt }
    }

    // 方式2：H5/APP 入口参数
    if (typeof uni !== 'undefined' && (uni as any).getEnterOptionsSync) {
      const q = (uni as any).getEnterOptionsSync().query || {}
      params = { ...q, ...params } // 以页面参数优先
    }
  } catch (_) {
    // ignore
  }

  if (Array.isArray(keys) && keys.length > 0) {
    const picked: Record<string, RouteParamValue> = {}
    keys.forEach(k => {
      picked[k] = params[k]
    })
    return picked
  }

  return params
}

