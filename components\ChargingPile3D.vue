<template>
  <div class="charging-pile-3d-container" ref="container">
    <!-- #ifdef APP-PLUS -->
    <ChargingPile3DApp ref="appComponentRef" :modelType="currentModelType" @loaded="onLoaded"></ChargingPile3DApp>
    <!-- #endif -->

    <!-- #ifndef APP-PLUS -->
    <div class="charging-pile-3d-container" ref="webglContainer"></div>
    <!-- #endif -->
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { useModel3DStore } from '@/store/model3d';

// #ifdef APP-PLUS
import ChargingPile3DApp from './ChargingPile3D/ChargingPile3DApp.vue';
console.log('ChargingPile3D.vue: APP-PLUS环境，已导入ChargingPile3DApp组件');
// #endif

// #ifndef APP-PLUS
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
console.log('ChargingPile3D.vue: 非APP-PLUS环境，已导入THREE库');
// #endif

console.log('ChargingPile3D.vue: 组件开始初始化');

// 使用3D模型store
const model3DStore = useModel3DStore();

// 接收modelType属性，用于指定要加载的模型类型
const props = defineProps({
  modelType: {
    type: String,
    default: 'default', // 默认模型类型
    validator: (value) => ['default', 'arnio'].includes(value)
  }
});

// 定义事件
const emit = defineEmits(['loaded']);

// 当前实际使用的模型类型（响应式）
const currentModelType = ref(props.modelType);

console.log('ChargingPile3D.vue: modelType =', props.modelType);
console.log('ChargingPile3D.vue: currentModelType =', currentModelType.value);

const container = ref(null);
const webglContainer = ref(null);

// #ifdef APP-PLUS
const appComponentRef = ref(null);
// #endif

// #ifndef APP-PLUS
let scene, camera, renderer, controls;
let model;
let animationFrameId;
let isInitialized = false;
let isMobile = false;
let isLowEndDevice = false;
let platform = 'web';

// 检测平台和设备类型
const detectPlatformAndDevice = () => {
  // 检测平台
  // #ifdef H5
  platform = 'h5';
  // #endif

  // #ifdef MP
  platform = 'mp';
  // #endif

  // #ifdef APP-PLUS
  platform = 'app';
  isMobile = true; // APP端默认为移动设备
  isLowEndDevice = false; // APP端默认为非低端设备
  console.log('Platform:', platform);
  console.log('Is mobile device:', isMobile);
  console.log('Is low-end device:', isLowEndDevice);
  return;
  // #endif

  // #ifndef APP-PLUS
  // 检测移动设备
  try {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera || '';
    isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());

    // 检测低端设备
    isLowEndDevice = false;
    if (isMobile) {
      // 简单的低端设备检测 - 可以根据需要调整
      const memory = navigator.deviceMemory;
      if (memory && memory <= 4) {
        isLowEndDevice = true;
      }

      // 检测老旧Android设备
      const androidMatch = userAgent.match(/Android\s([0-9\.]*)/);
      if (androidMatch && parseFloat(androidMatch[1]) < 7) {
        isLowEndDevice = true;
      }
    }
  } catch (error) {
    console.warn('无法检测设备信息:', error);
    isMobile = false;
    isLowEndDevice = false;
  }
  // #endif

  console.log('Platform:', platform);
  console.log('Is mobile device:', isMobile);
  console.log('Is low-end device:', isLowEndDevice);
};

// 获取资源路径
const getResourcePath = (relativePath) => {
  return relativePath;
};

// 初始化Three.js场景
const initThree = () => {
  if (!webglContainer.value || isInitialized) return;

  console.log('Initializing FBX Model Viewer');
  isInitialized = true;

  // 检测平台和设备
  detectPlatformAndDevice();
  
  try {
    // 创建场景
    scene = new THREE.Scene();
    scene.background = null; // 透明背景，让它融入页面

    // 创建相机
    camera = new THREE.PerspectiveCamera(
      40,
      webglContainer.value.clientWidth / webglContainer.value.clientHeight,
      0.1,
      2000
    );
    camera.position.set(3, 4, 10);

    // 创建渲染器，根据设备性能调整渲染质量
    const rendererParams = {
      antialias: !isLowEndDevice, // 低端设备禁用抗锯齿
      alpha: true,
      precision: isLowEndDevice ? 'mediump' : 'highp', // 低端设备使用中等精度
      powerPreference: 'default'
    };
    
    renderer = new THREE.WebGLRenderer(rendererParams);
    renderer.setSize(webglContainer.value.clientWidth, webglContainer.value.clientHeight);
    renderer.setPixelRatio(isLowEndDevice ? 1 : Math.min(window.devicePixelRatio, 2)); // 低端设备使用较低像素比
    
    // 根据设备性能调整阴影设置
    renderer.shadowMap.enabled = !isLowEndDevice;
    if (!isLowEndDevice) {
      renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }
    
    // 设置编码和色调映射
    renderer.outputEncoding = THREE.sRGBEncoding;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;
    
    webglContainer.value.appendChild(renderer.domElement);

    // 添加优化的灯光系统，根据设备性能调整
    const ambientLight = new THREE.AmbientLight(0xffffff, isLowEndDevice ? 0.6 : 0.4);
    scene.add(ambientLight);

    // 简化低端设备的灯光设置
    if (isLowEndDevice) {
      // 简单的主光源
      const mainLight = new THREE.DirectionalLight(0xffffff, 1.0);
      mainLight.position.set(5, 8, 5);
      scene.add(mainLight);
    } else {
      // 高质量灯光设置
      // 主光源 - 从右上方照射
      const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
      directionalLight.position.set(5, 8, 5);
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
      scene.add(directionalLight);

      // 补光 - 从左侧照射，减少阴影
      const fillLight = new THREE.DirectionalLight(0xffffff, 0.8);
      fillLight.position.set(-5, 3, 3);
      scene.add(fillLight);

      // 背光 - 从后方照射，增加立体感
      const backLight = new THREE.DirectionalLight(0xffffff, 0.5);
      backLight.position.set(0, 2, -5);
      scene.add(backLight);
    }

    // 加载FBX模型
    loadFBXModel();

    // 添加控制器
    controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.2;
    controls.enableZoom = false;
    controls.enablePan = false;
    controls.autoRotate = false;
    controls.autoRotateSpeed = 0.4;
    
    // 限制垂直旋转角度
    controls.minPolarAngle = Math.PI / 3;
    controls.maxPolarAngle = Math.PI / 1.8;
    
    // 设置初始旋转位置
    controls.target.set(0, 0.5, 0);
    camera.lookAt(controls.target);
    controls.update();

    // 开始动画循环
    animate();

    // 处理窗口大小变化
    window.addEventListener('resize', onWindowResize);
    
    // 输出WebGL支持信息用于调试
    logWebGLInfo();
    
  } catch (error) {
    console.error('Error initializing Three.js:', error);
    // 尝试显示错误信息
    showErrorMessage(error);
  }
};

// 输出WebGL支持信息
const logWebGLInfo = () => {
  if (!renderer) return;
  
  try {
    const gl = renderer.getContext();
    console.log('WebGL支持情况:', {
      renderer: renderer.getContext().getParameter(gl.RENDERER),
      vendor: renderer.getContext().getParameter(gl.VENDOR),
      version: renderer.getContext().getParameter(gl.VERSION),
      shadingLanguageVersion: renderer.getContext().getParameter(gl.SHADING_LANGUAGE_VERSION),
      maxTextureSize: renderer.getContext().getParameter(gl.MAX_TEXTURE_SIZE),
      extensions: renderer.getContext().getSupportedExtensions()
    });
  } catch (e) {
    console.warn('无法获取WebGL详细信息:', e);
  }
};

// 显示错误信息
const showErrorMessage = (error) => {
  if (!webglContainer.value) return;
  
  // 清除容器内容
  while (webglContainer.value.firstChild) {
    webglContainer.value.removeChild(webglContainer.value.firstChild);
  }
  
  // 创建错误信息元素
  const errorDiv = document.createElement('div');
  errorDiv.style.width = '100%';
  errorDiv.style.height = '100%';
  errorDiv.style.display = 'flex';
  errorDiv.style.alignItems = 'center';
  errorDiv.style.justifyContent = 'center';
  errorDiv.style.color = '#ff5252';
  errorDiv.style.padding = '20px';
  errorDiv.style.textAlign = 'center';
  errorDiv.innerHTML = `
    <div>
      <p>无法加载3D模型</p>
      <p>错误信息: ${error.message || '未知错误'}</p>
      <p>平台: ${platform}</p>
    </div>
  `;
  
  webglContainer.value.appendChild(errorDiv);
};

// 加载FBX模型
const loadFBXModel = () => {
  // 先尝试加载静态图片作为备用
  preloadFallbackImage();

  // 正常加载3D模型
  const loader = new FBXLoader();
  
  // 根据modelType选择不同的模型路径
  let modelPaths = [];

  if (currentModelType.value === 'arnio') {
    // 加载arnio.fbx模型
    modelPaths = [
      getResourcePath('./static/models/arnio.fbx'),
      getResourcePath('/static/models/arnio.fbx'),
      getResourcePath('../static/models/arnio.fbx'),
      getResourcePath('../../static/models/arnio.fbx'),
      getResourcePath('/utils/arnio.fbx')
    ];
  } else {
    // 默认加载GEN3APPFBX.fbx模型
    modelPaths = [
      getResourcePath('./static/GEN3APPFBX.fbx'),
      getResourcePath('/static/GEN3APPFBX.fbx'),
      getResourcePath('../static/GEN3APPFBX.fbx'),
      getResourcePath('../../static/GEN3APPFBX.fbx'),
      getResourcePath('/utils/GEN3APPFBX.fbx')
    ];
  }

  console.log(`Attempting to load FBX model (${currentModelType.value}), paths:`, modelPaths);

  // 尝试加载第一个路径
  tryLoadModel(modelPaths, 0);
};

// 预加载备用图片
const preloadFallbackImage = () => {
  // 根据modelType选择不同的备用图片
  const imagePath = currentModelType.value === 'arnio'
    ? getResourcePath('/static/models/charging-pile.png')
    : getResourcePath('/static/images/charging-pile.png');

  const img = new Image();
  img.src = imagePath;
  console.log('Preloading fallback image:', imagePath);
};

// 尝试加载模型
const tryLoadModel = (paths, index) => {
  if (index >= paths.length) {
    console.error('All model paths failed. Trying fallback image.');
    loadFallbackImage();
    return;
  }
  
  console.log('Trying model path:', paths[index]);
  const loader = new FBXLoader();
  
  loader.load(
    paths[index],
    (fbx) => {
      console.log('FBX model loaded successfully from path:', paths[index]);
      
      // 如果已经有模型，先移除
      if (model) {
        scene.remove(model);
        if (model.geometry) model.geometry.dispose();
        if (model.material) {
          if (Array.isArray(model.material)) {
            model.material.forEach(material => material.dispose());
          } else {
            model.material.dispose();
          }
        }
      }
      
      model = fbx;
      
      // 调整模型大小和位置
      fbx.scale.set(0.18, 0.18, 0.18);  // 调大模型尺寸，与APP端保持一致
      fbx.position.set(0, -1.4, 0);
      fbx.rotation.y = Math.PI * 8/1;
      fbx.rotation.x = Math.PI * 0.02;
      
      // 遍历模型中的所有材质，确保它们能够正确显示
      fbx.traverse((child) => {
        if (child.isMesh) {
          console.log('Found mesh in model:', child.name);
          
          // 根据设备性能设置阴影
          if (!isLowEndDevice) {
            child.castShadow = true;
            child.receiveShadow = true;
          }
          
          // 如果材质是标准材质，增加光泽度
          if (child.material) {
            if (Array.isArray(child.material)) {
              console.log('Mesh has array of materials:', child.material.length);
              child.material.forEach(material => {
                optimizeMaterial(material);
              });
            } else {
              console.log('Mesh has single material type:', child.material.type);
              optimizeMaterial(child.material);
            }
          }
        }
      });
      
      scene.add(fbx);
      console.log('Model added to scene');
      
      // 调整相机位置以适应模型
      const box = new THREE.Box3().setFromObject(fbx);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());
      
      console.log('Model size:', size);
      console.log('Model center:', center);
      
      // 根据模型大小调整相机位置
      const maxDim = Math.max(size.x, size.y, size.z);
      const fov = camera.fov * (Math.PI / 180);
      let cameraZ = Math.abs(maxDim / Math.sin(fov / 2));

      // 添加一些边距
      cameraZ *= 1.1;

      // 保持倾斜角度：向右倾斜，提高高度以看到顶部
      camera.position.set(cameraZ * 0.3, cameraZ * 0.4, cameraZ);
      console.log('Camera position set to:', camera.position);

      // 更新控制器 - 微调聚焦位置，让充电桩在屏幕中居中偏上
      const targetY = center.y + size.y * 0.1; // 轻微向上偏移，平衡显示位置
      controls.target.set(center.x, targetY, center.z);
      controls.update();
      console.log('Controls updated');
      
      // 通知加载完成
      onLoaded();
    },
    (xhr) => {
      console.log(`${paths[index]}: ${(xhr.loaded / xhr.total * 100).toFixed(2)}% loaded`);
    },
    (error) => {
      console.error(`Error loading FBX model from path ${paths[index]}:`, error);
      // 尝试下一个路径
      tryLoadModel(paths, index + 1);
    }
  );
};

// 优化材质
const optimizeMaterial = (material) => {
  if (material.isMeshPhongMaterial || material.isMeshStandardMaterial) {
    material.shininess = 100;
    material.specular = new THREE.Color(0xffffff);
    
    // 在低端设备上简化材质
    if (isLowEndDevice) {
      material.flatShading = true;
      if (material.map) material.map.anisotropy = 1;
      
      // 将复杂材质转换为基础材质以提高性能
      if (material.isMeshStandardMaterial) {
        const color = material.color.clone();
        const map = material.map;
        const basicMaterial = new THREE.MeshBasicMaterial({
          color: color,
          map: map,
          transparent: material.transparent,
          opacity: material.opacity
        });
        return basicMaterial;
      }
    }
  }
  return material;
};

// 加载备用图片
const loadFallbackImage = () => {
  console.log('Loading fallback image');

  // 根据modelType选择不同的备用图片
  const imagePath = currentModelType.value === 'arnio'
    ? getResourcePath('/static/models/charging-pile.png')
    : getResourcePath('/static/images/charging-pile.png');

  const textureLoader = new THREE.TextureLoader();
  textureLoader.load(
    imagePath,
    (texture) => {
      console.log('Fallback image loaded successfully');
      
      // 如果已经有模型，先移除
      if (model) {
        scene.remove(model);
        if (model.geometry) model.geometry.dispose();
        if (model.material) {
          if (Array.isArray(model.material)) {
            model.material.forEach(material => material.dispose());
          } else {
            model.material.dispose();
          }
        }
      }
      
      const material = new THREE.MeshBasicMaterial({ 
        map: texture,
        transparent: true
      });
      const geometry = new THREE.PlaneGeometry(6.5, 6.5);
      const plane = new THREE.Mesh(geometry, material);
      model = plane;
      
      // 添加旋转设置，让备用图片也朝向左边
      plane.rotation.y = Math.PI * 8/1;
      plane.rotation.x = Math.PI * 0.01;
      
      scene.add(plane);
      
      // 更新控制器 - 保持与3D模型相同的视角，居中偏上显示
      controls.target.set(0, 0.5, 0);
      controls.update();
      
      // 通知加载完成
      onLoaded();
    },
    undefined,
    (error) => {
      console.error('Failed to load fallback image:', error);
      showErrorMessage(new Error('无法加载3D模型或备用图片'));
    }
  );
};

// 处理窗口大小变化
const onWindowResize = () => {
  if (webglContainer.value && camera && renderer) {
    camera.aspect = webglContainer.value.clientWidth / webglContainer.value.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(webglContainer.value.clientWidth, webglContainer.value.clientHeight);
  }
};

// 动画循环
const animate = () => {
  try {
    animationFrameId = requestAnimationFrame(animate);
    
    if (controls) {
      controls.update();
    }
    
    if (renderer && scene && camera) {
      renderer.render(scene, camera);
    }
  } catch (error) {
    console.error('Error in animation loop:', error);
    cancelAnimationFrame(animationFrameId);
  }
};

// 监听props.modelType的变化
watch(() => props.modelType, (newType, oldType) => {
  console.log(`🔄 ChargingPile3D: props.modelType变化 ${oldType} -> ${newType}`);
  currentModelType.value = newType;
});

// 监听currentModelType的变化，当它变化时重新加载模型
watch(currentModelType, (newType, oldType) => {
  console.log(`🔄 ChargingPile3D: currentModelType变化 ${oldType} -> ${newType}`);

  // #ifdef APP-PLUS
  // APP环境下，传递给子组件
  console.log('ChargingPile3D: APP环境，currentModelType变化，传递给子组件');
  nextTick(() => {
    if (appComponentRef.value) {
      console.log('✅ 通过currentModelType变化调用子组件reloadModel');
      if (appComponentRef.value.reloadModel) {
        appComponentRef.value.reloadModel(newType);
      } else {
        console.log('❌ 子组件没有reloadModel方法');
      }
    } else {
      console.log('❌ 子组件ref不可用');
    }
  });
  // #endif

  // #ifndef APP-PLUS
  // H5环境下，直接重新加载模型
  if (isInitialized && scene) {
    console.log('ChargingPile3D: H5环境，currentModelType变化，重新加载模型');
    loadFBXModel();
  } else {
    console.log('ChargingPile3D: H5环境，场景未初始化');
  }
  // #endif
});

// 监听Pinia store的模型类型变化
watch(() => model3DStore.currentModelType, (newType, oldType) => {
  console.log(`🏪 ChargingPile3D: Pinia store模型类型变化 ${oldType} -> ${newType}`);

  // 如果store中的类型与当前类型不同，需要更新
  if (newType !== currentModelType.value) {
    console.log('🔄 ChargingPile3D: store类型与currentModelType不同，更新currentModelType');
    currentModelType.value = newType;
  } else {
    console.log('⏭️ ChargingPile3D: store类型与currentModelType相同，跳过更新');
  }
});

// 检查WebGL是否可用
const isWebGLAvailable = () => {
  try {
    const canvas = document.createElement('canvas');
    return !!(window.WebGLRenderingContext && 
      (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
  } catch (e) {
    console.error('WebGL检测失败:', e);
    return false;
  }
};

// 清理资源
const cleanupResources = () => {
  console.log('Cleaning up FBX Model Viewer resources');
  
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  
  if (controls) {
    controls.dispose();
  }
  
  // 清理模型资源
  if (model) {
    if (model.geometry) model.geometry.dispose();
    if (model.material) {
      if (Array.isArray(model.material)) {
        model.material.forEach(material => material.dispose());
      } else {
        model.material.dispose();
      }
    }
    scene.remove(model);
  }
  
  // 清理场景中的所有对象
  if (scene) {
    scene.traverse((object) => {
      if (object.geometry) object.geometry.dispose();
      if (object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose());
        } else {
          object.material.dispose();
        }
      }
    });
  }
  
  if (renderer) {
    renderer.dispose();
    renderer.forceContextLoss();
  }
  
  if (webglContainer.value && renderer && renderer.domElement) {
    try {
      webglContainer.value.removeChild(renderer.domElement);
    } catch (e) {
      console.error('Error removing renderer:', e);
    }
  }
  
  window.removeEventListener('resize', onWindowResize);
  
  // 清除变量
  scene = null;
  camera = null;
  renderer = null;
  controls = null;
  model = null;
  isInitialized = false;
};
// #endif

// 加载完成事件
const onLoaded = () => {
  console.log('ChargingPile3D.vue: 3D模型加载完成');
  emit('loaded');
};

// 暴露方法给父组件调用
const reloadModel = (newType) => {
  console.log('🎯 ChargingPile3D: 通过ref调用reloadModel，类型:', newType);

  // 更新当前模型类型
  currentModelType.value = newType;

  // #ifdef APP-PLUS
  // APP环境下，调用子组件的reloadModel方法
  console.log('ChargingPile3D: APP环境，调用子组件方法');

  // 使用nextTick确保组件完全挂载后再调用
  nextTick(() => {
    if (appComponentRef.value && appComponentRef.value.reloadModel) {
      console.log('✅ nextTick: 找到子组件，调用其reloadModel方法');
      appComponentRef.value.reloadModel(newType);
    } else {
      console.log('❌ nextTick: 子组件ref不可用:', {
        refExists: !!appComponentRef.value,
        hasMethod: !!(appComponentRef.value && appComponentRef.value.reloadModel)
      });
    }
  });
  // #endif

  // #ifndef APP-PLUS
  // H5环境下，直接重新加载模型
  if (scene && camera && renderer) {
    console.log('ChargingPile3D: H5环境，重新加载模型');
    // 清除当前模型
    if (model) {
      scene.remove(model);
      model = null;
    }
    // 重新加载模型
    loadFBXModel();
  } else {
    console.log('ChargingPile3D: H5环境，场景未初始化');
  }
  // #endif
}

// 暴露给父组件
defineExpose({
  reloadModel
});

onMounted(() => {
  console.log('ChargingPile3D.vue: onMounted被调用');
  console.log('ChargingPile3D.vue: container =', container.value);
  console.log('ChargingPile3D.vue: webglContainer =', webglContainer.value);
  
  // 检测当前环境
  // #ifdef APP-PLUS
  console.log('ChargingPile3D.vue: 检测到APP-PLUS环境');
  // #endif
  
  // #ifndef APP-PLUS
  console.log('ChargingPile3D.vue: 检测到非APP-PLUS环境');
  // 确保DOM元素已经渲染
  nextTick(() => {
    console.log('ChargingPile3D.vue: nextTick被调用，webglContainer =', webglContainer.value);
    if (webglContainer.value) {
      // 给容器设置明确的尺寸
      webglContainer.value.style.width = '100%';
      webglContainer.value.style.height = '100%';
      
      // 检查WebGL支持
      if (!isWebGLAvailable()) {
        showErrorMessage(new Error('您的设备不支持WebGL，无法显示3D模型'));
        return;
      }
      
      // 延迟初始化Three.js，确保DOM已完全渲染
      setTimeout(() => {
        console.log('ChargingPile3D.vue: 准备初始化Three.js');
        initThree();
      }, 500);
    }
  });
  // #endif
});

onBeforeUnmount(() => {
  console.log('ChargingPile3D.vue: onBeforeUnmount被调用');
  // #ifndef APP-PLUS
  cleanupResources();
  // #endif
});
</script>

<style scoped>
.charging-pile-3d-container {
  width: 100%;
  height: 100%;
  min-height: 1000rpx;
  overflow: hidden;
  position: relative;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style> 