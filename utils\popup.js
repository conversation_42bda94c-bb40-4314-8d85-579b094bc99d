/**
 * 弹窗工具类
 * 提供全局调用弹窗组件的方法
 */

/**
 * 显示对话框
 * @param {Object} options 配置项
 * @returns {Promise} 返回Promise对象
 */
export function showDialog(options = {}) {
  // 使用uni-app的showModal API
  return new Promise((resolve, reject) => {
    uni.showModal({
      title: options.title || '',
      content: options.message || '',
      confirmText: options.confirmText || 'Confirm', // 默认英文，调用方应传入国际化文本
      cancelText: options.cancelText || 'Cancel', // 默认英文，调用方应传入国际化文本
      showCancel: options.showCancel !== false,
      confirmColor: '#ff4d4f',
      cancelColor: '#999999',
      editable: !!options.editable,
      placeholderText: options.placeholder || '',
      success: (res) => {
        if (res.confirm) {
          resolve(options.editable ? res.content : undefined);
        } else {
          reject(new Error('User cancelled'));
        }
      },
      fail: () => {
        reject(new Error('Dialog display failed'));
      }
    });
  });
}

/**
 * 显示自定义对话框 (由于uni-app限制，实际上使用uni.showModal)
 * @param {Object} options 配置项
 * @returns {Promise} 返回Promise对象
 */
export function showCustomDialog(options = {}) {
  // 美化版弹窗，实际使用uni.showModal
  return showDialog(options);
}

/**
 * 显示成功对话框
 * @param {String|Object} messageOrOptions 消息文本或配置项
 * @returns {Promise} 返回Promise对象
 */
export function showSuccessDialog(messageOrOptions) {
  const options = typeof messageOrOptions === 'string' 
    ? { message: messageOrOptions } 
    : messageOrOptions;
  
  return showDialog({
    title: options.title || 'Success', // 默认英文，调用方应传入国际化文本
    type: 'success',
    ...options
  });
}

/**
 * 显示错误对话框
 * @param {String|Object} messageOrOptions 消息文本或配置项
 * @returns {Promise} 返回Promise对象
 */
export function showErrorDialog(messageOrOptions) {
  const options = typeof messageOrOptions === 'string' 
    ? { message: messageOrOptions } 
    : messageOrOptions;
  
  return showDialog({
    title: options.title || 'Error',
    type: 'error',
    ...options
  });
}

/**
 * 显示确认对话框
 * @param {String|Object} messageOrOptions 消息文本或配置项
 * @returns {Promise} 返回Promise对象
 */
export function showConfirmDialog(messageOrOptions) {
  const options = typeof messageOrOptions === 'string' 
    ? { message: messageOrOptions } 
    : messageOrOptions;
  
  return showDialog({
    title: options.title || 'Confirm', // 默认英文，调用方应传入国际化文本
    showCancel: true,
    confirmColor: options.type === 'warning' ? '#ff9900' : 
                 options.type === 'error' ? '#ff4d4f' : '#ff4d4f',
    ...options
  });
}

/**
 * 显示警告对话框
 * @param {String|Object} messageOrOptions 消息文本或配置项
 * @returns {Promise} 返回Promise对象
 */
export function showWarningDialog(messageOrOptions) {
  const options = typeof messageOrOptions === 'string' 
    ? { message: messageOrOptions } 
    : messageOrOptions;
  
  return showDialog({
    title: options.title || 'Warning',
    type: 'warning',
    ...options
  });
}

/**
 * 显示Toast提示
 * @param {String|Object} messageOrOptions 消息文本或配置项
 * @returns {Promise} 返回Promise对象
 */
export function showToast(messageOrOptions) {
  const options = typeof messageOrOptions === 'string' 
    ? { message: messageOrOptions } 
    : messageOrOptions;
  
  return new Promise((resolve) => {
    uni.showToast({
      title: options.message,
      icon: options.icon || 'none',
      duration: options.duration || 2000,
      mask: !!options.mask,
      image: options.image || '',
      position: options.position || 'center',
      success: resolve
    });
  });
}

/**
 * 显示成功Toast提示
 * @param {String|Object} messageOrOptions 消息文本或配置项
 * @returns {Promise} 返回Promise对象
 */
export function showSuccessToast(messageOrOptions) {
  const options = typeof messageOrOptions === 'string' 
    ? { message: messageOrOptions } 
    : messageOrOptions;
  
  return showToast({
    icon: 'success',
    ...options
  });
}

/**
 * 显示加载中Toast
 * @param {String|Object} messageOrOptions 消息文本或配置项
 * @returns {Promise} 返回Promise对象
 */
export function showLoading(messageOrOptions) {
  const options = typeof messageOrOptions === 'string' 
    ? { message: messageOrOptions } 
    : messageOrOptions;
  
  return new Promise((resolve) => {
    uni.showLoading({
      title: options.message || 'Loading...', // 默认英文，调用方应传入国际化文本
      mask: options.mask !== false,
      success: resolve
    });
  });
}

/**
 * 隐藏加载中Toast
 */
export function hideLoading() {
  uni.hideLoading();
}

/**
 * 显示ActionSheet
 * @param {Object} options 配置项
 * @returns {Promise} 返回Promise对象，resolve参数为选中的项
 */
export function showActionSheet(options = {}) {
  return new Promise((resolve, reject) => {
    // 转换选项格式
    const itemList = options.items.map(item => item.name);
    
    uni.showActionSheet({
      itemList,
      itemColor: options.itemColor || '#333333',
      popover: options.popover || {},
      success: (res) => {
        resolve(options.items[res.tapIndex]);
      },
      fail: () => {
        reject(new Error('User cancelled'));
      }
    });
  });
}

/**
 * 显示输入对话框
 * @param {Object} options 配置项
 * @returns {Promise} 返回Promise对象，resolve参数为用户输入的内容
 */
export function showInputDialog(options = {}) {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title: options.title || 'Please input', // 默认英文，调用方应传入国际化文本
      content: options.message || '',
      editable: true,
      placeholderText: options.placeholder || '',
      success: (res) => {
        if (res.confirm) {
          resolve(res.content);
        } else {
          reject(new Error('User cancelled'));
        }
      },
      fail: () => {
        reject(new Error('Dialog display failed'));
      }
    });
  });
}

// 默认导出
export default {
  showDialog,
  showCustomDialog,
  showSuccessDialog,
  showErrorDialog,
  showWarningDialog,
  showConfirmDialog,
  showInputDialog,
  showToast,
  showSuccessToast,
  showLoading,
  hideLoading,
  showActionSheet
} 