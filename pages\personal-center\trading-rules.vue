<template>
    <view class="trading-rules-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ $t('legal.tradingRules') }}</text>
                </view>
            </view>
        </view>
        
        <!-- 交易规则内容 -->
        <view class="content-container">
            <view class="content-section">
                <view class="section-title">
                    <text>1. General Rules</text>
                </view>
                <view class="section-text">
                    <text>These trading rules govern all transactions conducted through the ARNIO application. By using our services, you agree to comply with these rules. ARNIO reserves the right to modify these rules at any time, with such modifications being effective immediately upon posting.</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>2. Charging Services</text>
                </view>
                <view class="section-text">
                    <text>2.1 Pricing</text>
                </view>
                <view class="section-list">
                    <text>• All charging services are priced according to the current rates displayed in the application.</text>
                    <text>• Prices may vary based on location, time of day, and charging speed.</text>
                    <text>• Special rates may apply for VIP members.</text>
                </view>
                
                <view class="section-text">
                    <text>2.2 Payment</text>
                </view>
                <view class="section-list">
                    <text>• Payment for charging services must be made through the application using approved payment methods.</text>
                    <text>• Users must maintain sufficient funds in their account to cover charging costs.</text>
                    <text>• Automatic payments may be processed when the charging session ends.</text>
                </view>
                
                <view class="section-text">
                    <text>2.3 Refunds</text>
                </view>
                <view class="section-list">
                    <text>• Refunds for charging services may be issued in case of equipment malfunction or service interruption.</text>
                    <text>• Refund requests must be submitted within 48 hours of the charging session.</text>
                    <text>• ARNIO reserves the right to review and approve or deny refund requests at its discretion.</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>3. Account Balance</text>
                </view>
                <view class="section-text">
                    <text>3.1 Top-up</text>
                </view>
                <view class="section-list">
                    <text>• Users can add funds to their account using various payment methods.</text>
                    <text>• Minimum and maximum top-up amounts may apply.</text>
                    <text>• Bonus credits may be awarded for certain top-up amounts as part of promotional activities.</text>
                </view>
                
                <view class="section-text">
                    <text>3.2 Withdrawal</text>
                </view>
                <view class="section-list">
                    <text>• Users may withdraw unused funds from their account subject to verification.</text>
                    <text>• Processing fees may apply for withdrawals.</text>
                    <text>• Withdrawal requests may take up to 7 business days to process.</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>4. Dispute Resolution</text>
                </view>
                <view class="section-text">
                    <text>Any disputes arising from transactions conducted through the ARNIO application shall be resolved through the following process:</text>
                </view>
                <view class="section-list">
                    <text>• Users must submit a detailed description of the dispute through the application's customer service channel.</text>
                    <text>• ARNIO will investigate the dispute and provide a response within 15 business days.</text>
                    <text>• If the dispute cannot be resolved through this process, it may be referred to mediation or arbitration according to applicable laws.</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const statusBarHeight = ref(0)

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.trading-rules-container {
    min-height: 100vh;
    background-color: #ffffff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.content-container {
    padding: 32rpx;
    
    .content-section {
        margin-bottom: 40rpx;
        
        .section-title {
            margin-bottom: 16rpx;
            
            text {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
            }
        }
        
        .section-text {
            margin-bottom: 16rpx;
            
            text {
                font-size: 28rpx;
                color: #666;
                line-height: 1.5;
            }
        }
        
        .section-list {
            margin-bottom: 24rpx;
            
            text {
                display: block;
                font-size: 28rpx;
                color: #666;
                line-height: 1.5;
                margin-bottom: 16rpx;
                padding-left: 16rpx;
            }
        }
    }
}
</style> 