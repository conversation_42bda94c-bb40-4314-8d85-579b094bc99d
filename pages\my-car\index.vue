<template>
  <view class="my-car-page" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
    <!-- 顶部状态栏和返回 -->
    <view class="fixed-header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back"></text>
          </view>
          <!-- 加回标题 -->
          <text class="title">{{ $t('account.myCar') }}</text>
        </view>
      </view>
    </view>

    <!-- 车辆列表 -->
    <view class="car-list">
      <!-- 车辆项 -->
      <view class="car-item" v-for="(car, index) in cars" :key="car.carId">
        <view class="car-info">
          <view class="car-header">
            <text class="car-name">{{ getCarTypeName(car.carType) }}</text>
          </view>
          <text class="car-plate">{{ car.carNum }}</text>
          <view class="range-info">
            <text class="range-text">{{ $t('account.drivingRange') || 'Driving range of' }}</text>
            <text class="range-value">{{ car.range || 500 }}</text>
            <text class="range-unit">km</text>
          </view>
          <view class="range-bar">
            <view class="range-progress"></view>
          </view>
        </view>
        <view class="charge-button" @click.stop="chargeCar(car)">
          <text class="charge-text">{{ $t('charging.start') }}</text>
          <text class="charge-value">{{ car.chargeValue || 127 }}</text>
        </view>
        <image src="/static/images/my-car.png" class="car-image"/>
        <!-- 调整按钮位置 -->
        <view class="car-actions">
          <view class="action-button edit" @click.stop="editCar(car)">
            <text>{{ $t('common.edit') }}</text>
          </view>
          <view class="action-button delete" @click.stop="deleteCar(car)">
            <text>{{ $t('common.delete') }}</text>
          </view>
        </view>
      </view>

      <!-- 无车辆时显示 -->
      <view class="no-car" v-if="cars.length === 0">
        <image src="/static/images/no-car-added.png" mode="aspectFit" class="no-car-image"></image>
        <text class="no-car-text">{{ $t('account.noCarAddedYet') }}</text>
      </view>

      <!-- 添加车辆按钮 -->
      <view class="add-car-button" @click="addCar">
        <text>{{ $t('account.addCar') }}</text>
      </view>
    </view>
    
    <!-- Toast容器 -->
    <ToastContainer />




  </view>
</template>

<script setup>
import { ref, reactive, onMounted, inject } from 'vue'
import { useI18n } from 'vue-i18n'
import ToastContainer from '@/components/common/ToastContainer.vue'


import { getMyCarList, removeCar } from '@/api'
import { useUserStore } from '@/store/user'

// 使用国际化
const { t } = useI18n()

// 注入Toast实例
const toast = inject('toast')



// 使用用户状态store
const userStore = useUserStore()

// 响应式数据
const statusBarHeight = ref(0)
const cars = ref([])
const loading = ref(false)
const pageParams = reactive({
  pageNum: 1,
  pageSize: 10
})



// 车辆类型映射
const carTypes = {
  '0': 'Electric Vehicle',
  '1': 'Hybrid',
  '2': 'Gasoline'
}

// 当前操作的车辆
const currentCar = ref(null)

// 生命周期钩子
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
  
  // 获取车辆列表
  fetchCarList()
})

// 获取车辆类型名称
const getCarTypeName = (type) => {
  return carTypes[type] || 'Vehicle'
}

// 获取车辆列表
const fetchCarList = async () => {
  try {
    loading.value = true
    
    // 检查用户是否已登录
    if (!userStore.loggedIn || !userStore.getToken) {
      console.log('用户未登录或token不存在，跳转到登录页')
      uni.showToast({
        title: 'Please login first',
        icon: 'error',
        duration: 2000
      })
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/auth/login/index'
        })
      }, 1500)
      return
    }
    
    const result = await getMyCarList(pageParams)
    
    if (result.code === 200) {
      cars.value = result.rows.map(car => ({
        ...car,
        range: 500, // 默认续航里程
        chargeValue: 127 // 默认充电值
      }))
    } 
  } catch (error) {
    console.error('获取车辆列表失败', error)
    
    // 检查是否是token错误
    if (error.message && (
        error.message.includes('token') || 
        error.message.includes('unauthorized') || 
        error.message.includes('未授权') ||
        error.message.includes('未能读取到有效 token')
      )) {
    

      // 清除无效的token
      uni.removeStorageSync('token')
      userStore.logout()

      // 跳转到登录页
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/auth/login/index'
        })
      }, 1500)
    } else {
    
    }
  } finally {
    loading.value = false
  }
}

// 方法
const goBack = () => {
  uni.navigateBack()
}
    
const chargeCar = (car) => {
  console.log('Charge car:', car.carId)
  // 跳转到充电页面
  uni.navigateTo({
    url: '/pages/charging-status/index?carId=' + car.carId,
    animationType: 'slide-in-right',
    animationDuration: 300
  })
}
    
const editCar = (car) => {
  console.log('Edit car:', car.carId)
  // 将车辆信息存储到本地，供编辑页面获取
  uni.setStorage({
    key: 'editCarParams',
    data: {
      carId: car.carId,
      carNum: car.carNum,
      carType: car.carType
    },
    success: () => {
      // 跳转到编辑车辆页面
      uni.navigateTo({
        url: '/pages/add-car/index',
        animationType: 'slide-in-right',
        animationDuration: 300
      })
    }
  })
}

const deleteCar = async (car) => {
  try {
    // 使用Toast确认对话框
    await new Promise((resolve, reject) => {
      toast.confirm({
        title: t('account.deleteVehicle'),
        message: t('account.deleteVehicleConfirm'),
        confirmText: t('common.confirm'),
        cancelText: t('common.cancel'),
        onConfirm: resolve,
        onCancel: reject
      })
    })

    // 用户点击确认，显示删除中状态
    uni.showLoading({
      title: t('common.loading'),
      mask: true
    })

    const result = await removeCar(car.carId)

    // 隐藏loading
    uni.hideLoading()

    if (result.code === 200) {
      // 从列表中删除车辆
      cars.value = cars.value.filter(item => item.carId !== car.carId)

      // 显示成功提示
      uni.showToast({
        title: t('account.vehicleDeleted'),
        icon: 'success',
        duration: 2000
      })
    } else {
      // 显示失败提示
      uni.showToast({
        title: result.msg || t('error.deleteFailed'),
        icon: 'error',
        duration: 2000
      })
    }
  } catch (error) {
    // 用户取消操作，不做任何处理
    if (error.message === 'User cancelled') {
      console.log('Delete car cancelled')
      return
    }

    // 隐藏loading并显示失败提示
    uni.hideLoading()
    uni.showToast({
      title: t('error.deleteFailed'),
      icon: 'error',
      duration: 2000
    })

    console.error('Delete car failed', error)
  }
}
    
const addCar = () => {
  uni.navigateTo({
    url: '/pages/add-car/index',
    animationType: 'slide-in-right',
    animationDuration: 300
  })
}


</script>

<style lang="scss" scoped>
@import '@/static/iconfont/iconfont.css';

.my-car-page {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0,0,0,0.04);
}

.status-bar {
  background-color: #fff;
  width: 100%;
}

.header {
  background: #fff;
  width: 100%;
  
  .header-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .back-btn {
      width: 88rpx;
      height: 44px;
      display: flex;
      align-items: center;
      
      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }
    
    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }
  }
}

.car-list {
  padding: 30rpx 24rpx;
  flex: 1;
}

.car-item {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  
  .car-info {
    margin-bottom: 12rpx;
    position: relative;
    padding-right: 50%;
  }
  
  .car-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4rpx;
    
    .car-name {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .car-plate {
    font-size: 28rpx;
    color: #666;
  }
  
  .range-info {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    margin-top: 10rpx;
    .range-text {
      font-size: 28rpx;
      color: #333;
      margin-right: 8rpx;
    }
    
    .range-value {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-right: 8rpx;
    }
    
    .range-unit {
      font-size: 28rpx;
      color: #333;
    }
  }
  
  .range-bar {
    height: 12rpx;
    background: #f0f0f0;
    border-radius: 6rpx;
    overflow: hidden;
    width: 100%;
    
    .range-progress {
      height: 100%;
      background: #4CAF50;
      border-radius: 6rpx;
      width: 60%;
    }
  }
  
  .charge-button {
    background: #fff;
    border: 2rpx solid #4CAF50;
    border-radius: 50rpx;
    padding: 6rpx 24rpx;
    display: flex;
    align-items: center;
    position: absolute;
    right: 40rpx;
    top: 15rpx;
    z-index: 2;
    gap: 4rpx;
    
    .charge-text {
      font-size: 28rpx;
      color: #4CAF50;
    }
    
    .charge-value {
      font-size: 32rpx;
      color: #4CAF50;
      font-weight: bold;
    }
  }
  
  .car-image {
    width: 45%;
    height: 120rpx;
    object-fit: contain;
    position: absolute;
    right: 10rpx;
    top: 28%;
  }
  
  .car-actions {
    display: flex;
    justify-content: right;
    padding-top: 30rpx; /* 添加顶部内边距 */
    
    .action-button {
      padding: 15rpx 50rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 30rpx;
      font-size: 28rpx;
      
      &.edit {
        background: #fff;
        border: 1rpx solid #ddd;
        color: #333;
        margin-right: 16rpx;
      }
      
      &.delete {
        background: #f5f5f5;
        border: none;
        color: #333;
        margin-left: 16rpx;
      }
    }
  }
}

.no-car {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  
  .no-car-image {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 32rpx;
  }
  
  .no-car-text {
    font-size: 32rpx;
    color: #999;
  }
}

.add-car-button {
  margin: 40rpx 32rpx;
  height: 90rpx;
  background: #ff4d4f;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.2);
  
  &:active {
    transform: scale(0.98);
    background: #e63e40;
  }
}
</style> 