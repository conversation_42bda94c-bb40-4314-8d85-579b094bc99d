<template>
  <view class="popup-demo">
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <text class="iconfont icon-back"></text>
        </view>
        <text class="title">弹窗组件演示</text>
      </view>
    </view>
    
    <view class="content">
      <view class="section">
        <view class="section-title">对话框 Dialog</view>
        <view class="button-group">
          <button class="btn" @click="showDefaultDialog">默认对话框</button>
          <button class="btn" @click="showSuccessDialog">成功对话框</button>
          <button class="btn" @click="showErrorDialog">错误对话框</button>
          <button class="btn" @click="showWarningDialog">警告对话框</button>
          <button class="btn" @click="showConfirmDialog">确认对话框</button>
        </view>
      </view>
      
      <view class="section">
        <view class="section-title">提示 Toast</view>
        <view class="button-group">
          <button class="btn" @click="showDefaultToast">默认提示</button>
          <button class="btn" @click="showSuccessToast">成功提示</button>
          <button class="btn" @click="showErrorToast">错误提示</button>
          <button class="btn" @click="showLoadingToast">加载提示</button>
        </view>
      </view>
      
      <view class="section">
        <view class="section-title">底部菜单 ActionSheet</view>
        <view class="button-group">
          <button class="btn" @click="showActionSheet">显示底部菜单</button>
        </view>
      </view>
    </view>
    
    <!-- 组件演示 -->
    <Dialog 
      v-model:visible="dialogVisible"
      :type="dialogOptions.type"
      :title="dialogOptions.title"
      :message="dialogOptions.message"
      :icon="dialogOptions.icon"
      :show-cancel="dialogOptions.showCancel"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
    />
    
    <Toast 
      v-model:visible="toastVisible"
      :type="toastOptions.type"
      :message="toastOptions.message"
      :icon="toastOptions.icon"
      :duration="toastOptions.duration"
    />
    
    <ActionSheet 
      v-model:visible="actionSheetVisible"
      :title="actionSheetOptions.title"
      :items="actionSheetOptions.items"
      @select="handleActionSheetSelect"
    />
  </view>
</template>

<script>
import Dialog from '@/components/common/Dialog.vue'
import Toast from '@/components/common/Toast.vue'
import ActionSheet from '@/components/common/ActionSheet.vue'
import popup from '@/utils/popup.js'

export default {
  components: {
    Dialog,
    Toast,
    ActionSheet
  },
  data() {
    return {
      // 对话框
      dialogVisible: false,
      dialogOptions: {
        type: 'default',
        title: '提示',
        message: '这是一个对话框',
        icon: false,
        showCancel: true
      },
      
      // Toast提示
      toastVisible: false,
      toastOptions: {
        type: 'default',
        message: '这是一个提示',
        icon: false,
        duration: 2000
      },
      
      // 底部菜单
      actionSheetVisible: false,
      actionSheetOptions: {
        title: '请选择',
        items: [
          { name: '选项一', icon: '' },
          { name: '选项二', icon: '' },
          { name: '选项三', icon: '', disabled: true },
          { name: '删除', icon: '', type: 'danger' }
        ]
      }
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    
    // 对话框示例
    showDefaultDialog() {
      this.dialogOptions = {
        type: 'default',
        title: '提示',
        message: '这是一个默认对话框',
        icon: false,
        showCancel: true
      }
      this.dialogVisible = true
    },
    
    showSuccessDialog() {
      this.dialogOptions = {
        type: 'success',
        title: '成功',
        message: '操作成功',
        icon: true,
        showCancel: false
      }
      this.dialogVisible = true
    },
    
    showErrorDialog() {
      this.dialogOptions = {
        type: 'error',
        title: '错误',
        message: '操作失败',
        icon: true,
        showCancel: false
      }
      this.dialogVisible = true
    },
    
    showWarningDialog() {
      this.dialogOptions = {
        type: 'warning',
        title: '警告',
        message: '确定要执行此操作吗？',
        icon: true,
        showCancel: true
      }
      this.dialogVisible = true
    },
    
    showConfirmDialog() {
      this.dialogOptions = {
        type: 'info',
        title: '确认',
        message: '确定要删除此项吗？',
        icon: false,
        showCancel: true
      }
      this.dialogVisible = true
    },
    
    handleDialogConfirm() {
      this.showToast('Confirm button clicked')
      this.dialogVisible = false
    },

    handleDialogCancel() {
      this.showToast('Cancel button clicked')
      this.dialogVisible = false
    },
    
    // Toast示例
    showDefaultToast() {
      this.toastOptions = {
        type: 'default',
        message: 'This is a default toast',
        icon: false,
        duration: 2000
      }
      this.toastVisible = true
    },

    showSuccessToast() {
      this.toastOptions = {
        type: 'success',
        message: 'Operation successful',
        icon: true,
        duration: 2000
      }
      this.toastVisible = true
    },

    showErrorToast() {
      this.toastOptions = {
        type: 'error',
        message: 'Operation failed',
        icon: true,
        duration: 2000
      }
      this.toastVisible = true
    },

    showLoadingToast() {
      this.toastOptions = {
        type: 'loading',
        message: 'Loading...',
        icon: false,
        duration: 2000
      }
      this.toastVisible = true
    },
    
    // 简单提示
    showToast(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      })
    },
    
    // 底部菜单示例
    showActionSheet() {
      this.actionSheetVisible = true
    },
    
    handleActionSheetSelect(item) {
      this.showToast(`Selected: ${item.name}`)
    },
    
    // 使用工具类示例
    async usePopupUtils() {
      try {
        // 显示确认对话框
        await popup.showConfirmDialog('Are you sure you want to perform this operation?')

        // 显示加载中
        popup.showLoading('Processing...')

        // 模拟异步操作
        setTimeout(() => {
          // 隐藏加载中
          popup.hideLoading()

          // 显示成功提示
          popup.showSuccessToast('Operation successful')
        }, 2000)
      } catch (error) {
        // 用户取消
        popup.showToast('Operation cancelled')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-demo {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

.header {
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  
  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    
    .back-btn {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      
      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }
    
    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }
  }
}

.content {
  padding: 120rpx 32rpx 32rpx;
}

.section {
  margin-bottom: 40rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .button-group {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -16rpx;
    
    .btn {
      width: calc(50% - 32rpx);
      margin: 16rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      background-color: #ff4d4f;
      color: #fff;
      border-radius: 8rpx;
      font-size: 28rpx;
      
      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style> 