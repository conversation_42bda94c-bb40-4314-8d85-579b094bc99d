/**
 * 环境变量配置
 */

// 环境配置接口
interface EnvConfig {
  BASE_API: string;
  WS_URL: string; // 添加 WebSocket URL
  NODE_ENV: string;
  GOOGLE_MAPS_API_KEY: string; // 添加 Google Maps API Key
}

// 开发环境
const development: EnvConfig = {
  // BASE_API: 'https://arnioci.com/prod-api',
  // WS_URL: 'wss://arnioci.com/prod-api/resource/websocket',
  BASE_API: 'https://charge.arnioci.com/prod-api',
  WS_URL: 'wss://charge.arnioci.com/prod-api/resource/websocket',
  NODE_ENV: 'development',
  GOOGLE_MAPS_API_KEY: (typeof process !== 'undefined' && process.env && (process.env.GMAPS_KEY as string)) || '' // 开发环境 API Key 从环境变量注入
}

// 生产环境
const production: EnvConfig = {
  // BASE_API: 'https://arnioci.com/prod-api',
  // WS_URL: 'wss://arnioci.com/prod-api/resource/websocket',
  BASE_API: 'https://charge.arnioci.com/prod-api',
  WS_URL: 'wss://charge.arnioci.com/prod-api/resource/websocket',
  NODE_ENV: 'production',
  GOOGLE_MAPS_API_KEY: (typeof process !== 'undefined' && process.env && (process.env.GMAPS_KEY as string)) || '' // 生产环境 Key 从环境变量注入
}

// 测试环境
const test: EnvConfig = {
  // BASE_API: 'https://arnioci.com/prod-api',
  // WS_URL: 'wss://arnioci.com/prod-api/resource/websocket',
  BASE_API: 'https://charge.arnioci.com/prod-api',
  WS_URL: 'wss://charge.arnioci.com/prod-api/resource/websocket',
  NODE_ENV: 'test',
  GOOGLE_MAPS_API_KEY: (typeof process !== 'undefined' && process.env && (process.env.GMAPS_KEY as string)) || '' // 测试环境 Key 从环境变量注入
}

// 声明 process 环境变量
declare const process: {
  env: {
    NODE_ENV?: string;
  }
}

// 根据当前环境导出配置
const env = process.env.NODE_ENV || 'development'
const configs: Record<string, EnvConfig> = {
  development,
  production,
  test
}

export default configs[env as keyof typeof configs] 