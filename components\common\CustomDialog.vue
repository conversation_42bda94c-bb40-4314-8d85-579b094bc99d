<template>
  <view class="custom-dialog-container" v-if="visible" @touchmove.stop.prevent>
    <!-- 遮罩层 -->
    <view class="dialog-mask" @click="maskClose && close()"></view>
    
    <!-- 弹窗内容 -->
    <view class="dialog-content" :class="[`dialog-${type}`]">
      <!-- 标题 -->
      <view class="dialog-header" v-if="title">
        <text class="dialog-title">{{ title }}</text>
      </view>
      
      <!-- 内容区域 -->
      <view class="dialog-body">
        <!-- 消息内容 -->
        <view class="dialog-message" v-if="message">
          <text class="dialog-message-text">{{ message }}</text>
        </view>
        
        <!-- 自定义内容插槽 -->
        <slot></slot>
      </view>
      
      <!-- 底部按钮区域 -->
      <view class="dialog-footer" :class="{'single-button': !showCancel}">
        <!-- 取消按钮 -->
        <view 
          class="dialog-btn dialog-btn-cancel" 
          v-if="showCancel" 
          @click="handleCancel"
        >
          <text>{{ cancelText }}</text>
        </view>
        
        <!-- 确认按钮 -->
        <view 
          class="dialog-btn dialog-btn-confirm" 
          :class="[`dialog-btn-${type}`, {'dialog-btn-loading': loading}]" 
          @click="handleConfirm"
        >
          <view class="dialog-loading" v-if="loading"></view>
          <text>{{ confirmText }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomDialog',
  props: {
    // 弹窗是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 弹窗类型: default, success, warning, error, info
    type: {
      type: String,
      default: 'default',
      validator: function(value) {
        return ['default', 'success', 'warning', 'error', 'info'].includes(value)
      }
    },
    // 弹窗标题
    title: {
      type: String,
      default: ''
    },
    // 弹窗内容
    message: {
      type: String,
      default: ''
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: false
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确认'
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 点击遮罩是否关闭
    maskClose: {
      type: Boolean,
      default: true
    },
    // 是否显示加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 根据类型获取对应的图标
    iconSrc() {
      const iconMap = {
        success: '/static/images/success.png',
        warning: '/static/images/tip.png',
        error: '/static/images/tip-2.png',
        info: '/static/images/tip.png',
        default: '/static/images/tip.png'
      }
      return iconMap[this.type]
    }
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    
    // 处理取消按钮点击
    handleCancel() {
      this.$emit('cancel')
      this.close()
    },
    
    // 处理确认按钮点击
    handleConfirm() {
      if (this.loading) return
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-dialog-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  animation: fade-in 0.3s ease-out;
}

.dialog-content {
  width: 680rpx;
  background-color: #fff;
  position: relative;
  z-index: 10000;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: scale-in 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  padding: 0;
}

.dialog-icon-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 48rpx;
  
  .dialog-icon {
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
  }
}

.dialog-header {
  padding: 48rpx 48rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;

  .dialog-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    text-align: left;
    letter-spacing: 0.5rpx;
  }
}

.dialog-body {
  padding: 0 48rpx 48rpx;

  .dialog-message {
    text-align: left;

    .dialog-message-text {
      font-size: 30rpx;
      color: #666;
      line-height: 1.6;
      white-space: pre-line;
    }
  }
}

.dialog-footer {
  display: flex;
  padding: 32rpx 48rpx 48rpx;
  gap: 24rpx;

  &.single-button {
    .dialog-btn-confirm {
      border-radius: 12rpx;
    }
  }

  .dialog-btn {
    flex: 1;
    height: 96rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    position: relative;
    transition: all 0.2s;
    border-radius: 12rpx;

    &.dialog-btn-cancel {
      color: #666;
      background-color: #f8f9fa;
      font-weight: 500;
      border: 2rpx solid #e9ecef;

      &:active {
        background-color: #e9ecef;
        transform: scale(0.98);
      }
    }

    &.dialog-btn-confirm {
      color: #fff;
      font-weight: 600;
      border: none;

      &:active {
        opacity: 0.9;
        transform: scale(0.98);
      }

      &.dialog-btn-loading {
        opacity: 0.7;
      }
    }
    
    &.dialog-btn-default {
      background: #007AFF;
    }

    &.dialog-btn-success {
      background: #34C759;
    }

    &.dialog-btn-warning {
      background: #FF9500;
    }

    &.dialog-btn-error {
      background: #FF3B30;
    }

    &.dialog-btn-info {
      background: #007AFF;
    }
  }
  
  .dialog-loading {
    width: 32rpx;
    height: 32rpx;
    border: 4rpx solid rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    border-top-color: transparent;
    margin-right: 16rpx;
    animation: loading-rotate 0.8s linear infinite;
  }
}

@keyframes loading-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.85);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style> 