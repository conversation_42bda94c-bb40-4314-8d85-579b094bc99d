const { defineConfig } = require('vite');
const uni = require('@dcloudio/vite-plugin-uni');
const path = require('path');

// https://vitejs.dev/config/
module.exports = defineConfig({
  plugins: [
    uni.default(),
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true
      }
    }
  },
  // 定义环境变量
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV)
  },
  // 添加外部依赖配置，解决three.js相关模块的导入问题
  build: {
    cssCodeSplit: false, // 进一步避免代码分割
    rollupOptions: {
      external: [
        'three',
        'three/examples/jsm/controls/OrbitControls.js',
        'three/examples/jsm/loaders/FBXLoader.js',
        '@stagewise/toolbar'
      ],
      // Fix: prevent Rollup error "Invalid value 'iife' for option 'output.format' - UMD and IIFE output formats are not supported for code-splitting builds."
      // By inlining dynamic imports and disabling manualChunks, IIFE/UMD outputs (if set by plugins) won't use code-splitting.
      output: {
        inlineDynamicImports: true,
        manualChunks: undefined // 禁用手动分块
      }
    }
  },
  optimizeDeps: {
    include: [
      'three', 
      'three/examples/jsm/controls/OrbitControls.js', 
      'three/examples/jsm/loaders/FBXLoader.js'
    ],
    exclude: ['@stagewise/toolbar']
  }
}); 