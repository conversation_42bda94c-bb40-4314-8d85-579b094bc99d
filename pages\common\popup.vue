<template>
  <view class="popup-container">
    <!-- 对话框 -->
    <Dialog
      v-if="popupType === 'dialog'"
      v-model:visible="dialogVisible"
      :type="dialogOptions.type || 'default'"
      :title="dialogOptions.title"
      :message="dialogOptions.message"
      :icon="dialogOptions.icon"
      :show-header="dialogOptions.showHeader !== false"
      :show-footer="dialogOptions.showFooter !== false"
      :show-cancel="dialogOptions.showCancel"
      :show-close="dialogOptions.showClose"
      :confirm-text="dialogOptions.confirmText || '确认'"
      :cancel-text="dialogOptions.cancelText || '取消'"
      :round="dialogOptions.round !== false"
      :mask-close="dialogOptions.maskClose !== false"
      :loading="dialogLoading"
      @close="handleDialogClose"
      @cancel="handleDialogCancel"
      @confirm="handleDialogConfirm"
    >
      <template v-if="dialogOptions.content">
        <rich-text :nodes="dialogOptions.content"></rich-text>
      </template>
    </Dialog>
    
    <!-- Toast提示 -->
    <Toast
      v-if="popupType === 'toast'"
      v-model:visible="toastVisible"
      :type="toastOptions.type || 'default'"
      :message="toastOptions.message"
      :icon="toastOptions.icon"
      :duration="toastOptions.duration || 2000"
      :position="toastOptions.position || 'center'"
      @close="handleToastClose"
    />
    
    <!-- 底部菜单 -->
    <ActionSheet
      v-if="popupType === 'actionSheet'"
      v-model:visible="actionSheetVisible"
      :title="actionSheetOptions.title"
      :description="actionSheetOptions.description"
      :items="actionSheetOptions.items || []"
      :show-cancel="actionSheetOptions.showCancel !== false"
      :cancel-text="actionSheetOptions.cancelText || '取消'"
      :show-close="actionSheetOptions.showClose"
      :mask-close="actionSheetOptions.maskClose !== false"
      @close="handleActionSheetClose"
      @select="handleActionSheetSelect"
    />
  </view>
</template>

<script>
import Dialog from '@/components/common/Dialog.vue'
import Toast from '@/components/common/Toast.vue'
import ActionSheet from '@/components/common/ActionSheet.vue'

export default {
  components: {
    Dialog,
    Toast,
    ActionSheet
  },
  data() {
    return {
      // 弹窗类型：dialog, toast, actionSheet
      popupType: '',
      
      // 对话框
      dialogVisible: false,
      dialogOptions: {},
      dialogLoading: false,
      
      // Toast提示
      toastVisible: false,
      toastOptions: {},
      
      // 底部菜单
      actionSheetVisible: false,
      actionSheetOptions: {}
    }
  },
  onLoad(options) {
    // 获取弹窗类型和参数
    this.popupType = options.type || 'dialog'
    const params = options.params ? JSON.parse(decodeURIComponent(options.params)) : {}
    
    // 根据类型显示对应的弹窗
    switch (this.popupType) {
      case 'dialog':
        this.dialogOptions = params
        this.dialogVisible = true
        break
      case 'toast':
        this.toastOptions = params
        this.toastVisible = true
        break
      case 'actionSheet':
        this.actionSheetOptions = params
        this.actionSheetVisible = true
        break
    }
  },
  methods: {
    // 对话框事件处理
    handleDialogClose() {
      this.dialogVisible = false
      setTimeout(() => {
        uni.navigateBack()
      }, 200)
    },
    handleDialogCancel() {
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.emit('cancel')
      this.handleDialogClose()
    },
    handleDialogConfirm() {
      // 如果有异步操作
      if (this.dialogOptions.async) {
        this.dialogLoading = true
        
        // 发送确认事件
        const eventChannel = this.getOpenerEventChannel()
        eventChannel.emit('confirm')
        
        // 等待异步操作完成
        setTimeout(() => {
          this.dialogLoading = false
          this.handleDialogClose()
        }, 500)
      } else {
        // 直接发送确认事件
        const eventChannel = this.getOpenerEventChannel()
        eventChannel.emit('confirm')
        this.handleDialogClose()
      }
    },
    
    // Toast事件处理
    handleToastClose() {
      this.toastVisible = false
      setTimeout(() => {
        uni.navigateBack()
      }, 200)
    },
    
    // 底部菜单事件处理
    handleActionSheetClose() {
      this.actionSheetVisible = false
      setTimeout(() => {
        uni.navigateBack()
      }, 200)
    },
    handleActionSheetSelect(item, index) {
      const eventChannel = this.getOpenerEventChannel()
      eventChannel.emit('select', { item, index })
      this.handleActionSheetClose()
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: transparent;
}
</style> 