<template>
  <view class="order-detail-page">
    <!-- 顶部背景区域 -->
    <view class="top-bg">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

      <!-- 导航栏 -->
      <view class="header">
        <view class="back-btn" @click="goBack">
          <text class="iconfont icon-back"></text>
        </view>
        <text class="title">{{ t('payment.orderDetails') }}</text>
        <image src="/static/images/logo-white.png" class="logo" mode="aspectFit"></image>
      </view>

      <!-- 订单状态 -->
      <view class="order-status" :class="{ 'pending-payment': isPaymentPage }">
        <view class="status-icon" :class="{ 'isColor': !isPaymentPage }">
          <image v-if="isPaymentPage" src="/static/images/Fval.png" class="warning-icon" mode="aspectFit"></image>
          <image v-else src="/static/images/success.png" class="success-icon" mode="aspectFit"></image>
        </view>
        <view class="status-info">
          <text class="status-text">{{ isPaymentPage ? t('payment.orderPendingPayment') : t('payment.orderCompleted')
          }}</text>
          <text class="status-message">{{ isPaymentPage ? t('payment.insufficientBalance') :
            t('payment.thankYouForUsing') }}</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content" :class="{ 'payment-page': isPaymentPage }">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ t('payment.loadingOrderDetails') }}</text>
      </view>

      <!-- 内容区域 -->
      <template v-else>
        <!-- 充电站信息 -->
        <view class="station-card">
          <text class="station-name">{{ orderData?.plotName || t('charging.stationName') }}</text>
          <view class="gun-info">
            <text>{{ t('payment.chargingGun') }} {{ orderData?.spearNum || '001' }} ( {{ orderData?.spearName ||
              t('payment.chargingGun') }} )</text>
          </view>
          <view class="divider"></view>
          <view class="vehicle-info">
            <text class="label">{{ t('payment.chargingVehicle') }}</text>
            <text class="value">{{ orderData?.carNum || orderData?.carId || t('payment.chargingVehicle') }}</text>
          </view>
        </view>

        <!-- 费用信息 -->
        <view class="payment-card">
          <view class="total-amount-header">
            <text class="total-amount">{{ orderData?.actualPaymentPrice || orderData?.orderPrice || '0.00' }} F</text>
            <view class="collapse-btn" @click="toggleDetails">
              <text>{{ showDetails ? t('payment.collapseDetails') : t('payment.expandDetails') }}</text>
              <text class="arrow-icon" :class="{ 'expanded': showDetails }">^</text>
            </view>
          </view>

          <view class="payment-details" v-if="showDetails">
            <view class="payment-item">
              <text class="item-label">{{ t('payment.chargingAmount') }}</text>
              <text class="item-value">{{ orderData?.orderPrice || '0.00' }} F</text>
            </view>
            <view class="payment-item">
              <text class="item-label">{{ t('payment.electricityBill') }}</text>
              <text class="item-value">{{ orderData?.chargeFee || '0.00' }} F</text>
            </view>
            <view class="payment-item">
              <text class="item-label">{{ t('payment.serviceFee') }}</text>
              <text class="item-value">{{ orderData?.serviceFee || '0.00' }} F</text>
            </view>
            <view class="payment-item discount" v-if="orderData?.couponPrice">
              <text class="item-label">{{ t('payment.coupon') }}</text>
              <text class="item-value">-{{ orderData?.couponPrice }} F</text>
            </view>
            <view class="payment-item" v-if="orderData?.refundAmount && parseFloat(orderData.refundAmount) > 0">
              <text class="item-label">{{ t('payment.refundAmount') }}</text>
              <text class="item-value">{{ orderData?.refundAmount }} F</text>
            </view>
            <view class="payment-total">
              <text class="total-label">{{ t('payment.totalPaid') }}:</text>
              <text class="total-value">{{ orderData?.actualPaymentPrice || orderData?.orderPrice || '0.00' }} F</text>
            </view>
          </view>
        </view>

        <!-- 充电量信息 -->
        <view class="charged-kwh">
          <text>{{ t('payment.chargedAmount') }}: </text>
          <text class="value">{{ orderData?.degree || '0' }} kWh</text>
        </view>

        <!-- 充电信息 -->
        <view class="charging-info-card">
          <text class="section-title">{{ t('payment.chargingInformation') }}</text>
          <view class="info-list">
            <view class="info-item">
              <text class="info-label">{{ t('payment.orderNumber') }}</text>
              <text class="info-value">{{ orderData?.orderNumber || orderNumber }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ t('payment.startTime') }}</text>
              <text class="info-value">{{ orderData?.startTime || '--' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">{{ t('payment.endTime') }}</text>
              <text class="info-value">{{ orderData?.endTime || orderData?.realEndTime || '--' }}</text>
            </view>
            <view class="info-item" v-if="orderData?.realHour || orderData?.realMinute">
              <text class="info-label">{{ t('payment.chargingDuration') }}</text>
              <text class="info-value"> {{ orderData?.realMinute || '0' }} min</text>
            </view>
            <view class="info-item" v-if="orderData?.deviceType">
              <text class="info-label">{{ t('payment.deviceType') }}</text>
              <text class="info-value">{{ orderData?.deviceType === 2 ? t('payment.dcFastCharger') :
                t('payment.acCharger') }}</text>
            </view>
            <view class="info-item" v-if="orderData?.orderState">
              <text class="info-label">{{ t('payment.orderStatus') }}</text>
              <text class="info-value">{{ orderData?.orderState }}</text>
            </view>
          </view>
        </view>
      </template>
    </view>

    <!-- 底部按钮区域 -->
    <!-- 支付按钮 - 仅在支付页面显示 -->
    <view v-if="isPaymentPage" class="payment-button-container">
      <button 
        class="payment-button" 
        :class="{ 'loading': isPaymentLoading }"
        :disabled="isPaymentLoading"
        @click="goToPay">
        {{ isPaymentLoading ? t('payment.processing') : t('payment.goToPay') }}
      </button>
    </view>

    <!-- 返回首页按钮 - 仅在订单完成页面显示 -->
    <view v-if="!isPaymentPage" class="payment-button-container">
      <button class="payment-button home-button" @click="goToHome">{{ t('payment.returnToHomepage') }}</button>
    </view>
    
    <!-- HUD 局部挂载，确保弹窗可见 -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { getOrderAppletDetail, getOrderDetailPay } from '@/api/modules/charging'
import { payOrder } from '@/api/modules/payment'
import { useI18n } from '@/composables/useI18n.js'
import GlobalHUD from '@/components/common/GlobalHUD.vue'
import { useGlobalHud } from '@/composables/useHud'

// 国际化
const { t } = useI18n()

// HUD 组件
const hud = useGlobalHud()

// 响应式数据
const statusBarHeight = ref(0)
const showDetails = ref(true)
const loading = ref(false)
const orderData = ref(null)
const orderNumber = ref('')
const orderId = ref('') // 存储 orderId
const isPaymentPage = ref(false) // 是否为支付页面
const isPaymentLoading = ref(false) // 支付加载状态
const paymentSuccess = ref(false) // 支付是否成功

// 使用 onLoad 接收页面参数
onLoad((options) => {
  console.log('=== order-detail onLoad 接收参数 ===')
  console.log('接收到的参数:', options)
  console.log('URL 参数详情:', JSON.stringify(options, null, 2))

  // 检测页面来源 - 如果有 fromPayment 参数，则为支付页面
  if (options && options.fromPayment === 'true') {
    isPaymentPage.value = true
    console.log('✅ 检测到来自支付页面')
  }

  // 兼容多种参数传递方式（优先处理 orderId）
  let orderParam = null
  let orderIdParam = null

  // 优先检查 orderId（接口需要的参数）
  if (options && options.orderId) {
    orderIdParam = decodeURIComponent(options.orderId)
    console.log('✅ 从onLoad获取订单ID(orderId):', orderIdParam)
  }

  // 检查 orderNumber 相关参数
  if (options && options.orderNumber) {
    orderParam = decodeURIComponent(options.orderNumber)
    console.log('✅ 从onLoad获取订单号(orderNumber):', orderParam)
  } else if (options && options.orderNum) {
    orderParam = decodeURIComponent(options.orderNum)
    console.log('✅ 从onLoad获取订单号(orderNum):', orderParam)
  }

  console.log('参数解析结果 - orderIdParam:', orderIdParam, 'orderParam:', orderParam)

  if (orderIdParam) {
    // 如果有 orderId，优先使用（接口需要的是 orderId）
    orderId.value = orderIdParam
    orderNumber.value = orderIdParam // 也存储到 orderNumber 用于显示
    console.log('✅ 使用 orderId 调用接口:', orderIdParam)
    console.log('✅ 设置后的值 - orderId.value:', orderId.value, 'orderNumber.value:', orderNumber.value)
    fetchOrderDetail()
  } else if (orderParam) {
    // 如果只有 orderNumber，存储但可能接口调用会有问题
    orderNumber.value = orderParam
    console.log('⚠️ 只有 orderNumber，可能需要转换为 orderId')
    console.log('⚠️ 设置后的值 - orderNumber.value:', orderNumber.value)
    fetchOrderDetail()
  } else {
    console.log('❌ onLoad中没有orderNumber或orderId参数，使用默认数据')
  }
})

// 获取订单详情
const fetchOrderDetail = async () => {
  // 优先使用 orderId，如果没有则使用 orderNumber
  const paramToUse = orderId.value || orderNumber.value

  if (!paramToUse) {
    console.error('订单号/订单ID为空，无法获取订单详情')
    return
  }

  try {
    loading.value = true
    console.log('开始获取订单详情，参数:', paramToUse, '(类型:', orderId.value ? 'orderId' : 'orderNumber', ')')

    // 根据页面类型调用不同的API，都传递 orderId
    const response = isPaymentPage.value
      ? await getOrderDetailPay(paramToUse)
      : await getOrderAppletDetail(paramToUse)
    console.log('订单详情API响应:', response)

    if (response && response.code === 200 && response.data) {
      orderData.value = response.data
      updatePageData(response.data)
      console.log('✅ 订单详情获取成功')
    } else {
      throw new Error(response?.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 更新页面数据
const updatePageData = (data) => {
  console.log('更新页面数据:', data)
  orderData.value = data
  console.log('页面数据更新完成')
}

// 生命周期
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
})

// 方法
const goBack = () => {
  uni.navigateTo({
    url: '/pages/more/index'
  })
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

// 支付按钮点击事件
const goToPay = async () => {
  console.log('点击支付按钮，订单号:', orderNumber.value)
  
  // 防止重复点击
  if (isPaymentLoading.value) {
    return
  }
  
  const ordNumber = orderData.value?.orderNumber || orderNumber.value
  const prepaidFees = orderData.value?.actualPaymentPrice || orderData.value?.orderPrice || '0'
  
  if (!ordNumber) {
    hud.error(t('toast.orderNumberNotFound'))
    return
  }
  
  try {
    isPaymentLoading.value = true
    
    // 显示支付中提示
    hud.loading(t('payment.processing'))
    
    // 调用支付API
    const paymentParams = {
      orderNum: ordNumber,
      paymentMethod: "2", // 固定传 "2"
      prepaidFees: prepaidFees,
      type: '0' // 固定传 '0'
    }
    
    console.log('支付参数:', paymentParams)
    const response = await payOrder(paymentParams)
    
    hud.done()
    
    if (response && (response.code === 200 || response.code === "200")) {
      // 支付成功
      paymentSuccess.value = true
      isPaymentPage.value = false // 改变页面状态为已完成
      
      hud.success(t('payment.paymentSuccessful'))
      
      // 刷新订单详情
      await fetchOrderDetail()
      
      console.log('支付成功，响应:', response)
    } else {
      throw new Error(response?.message || response?.msg || t('payment.failed'))
    }
  } catch (error) {
    hud.done()
    console.error('支付失败:', error)
    
    let errorMessage = t('payment.failed')
    if (error.response) {
      errorMessage = error.response.data?.message || error.response.data?.msg || errorMessage
    } else if (error.message) {
      errorMessage = error.message
    }
    
    hud.error(errorMessage)
  } finally {
    isPaymentLoading.value = false
  }
}

// 返回首页按钮点击事件
const goToHome = () => {
  console.log('点击返回首页按钮')
  uni.navigateTo({
    url: '/pages/home/<USER>'
  })
}
// 从支付页返回时刷新
onShow(() => {
  // 如果有订单ID或订单号，刷新订单详情
  if (orderId.value || orderNumber.value) {
    fetchOrderDetail()
  }
})

</script>

<style lang="scss">
@import '@/static/iconfont/iconfont.css';

.order-detail-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.top-bg {
  background-color: #ff4d4f;
  padding-bottom: 40rpx;
  position: relative;
  overflow: hidden;

  &:after {
    content: '';
    position: absolute;
    right: -100rpx;
    top: -100rpx;
    width: 400rpx;
    height: 400rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 50%;
    z-index: 1;
  }
}

.status-bar {
  width: 100%;
  background-color: #ff4d4f;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 20rpx 32rpx;

  .back-btn {
    position: absolute;
    left: 32rpx;
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .iconfont {
      font-size: 40rpx;
      color: #fff;
    }
  }

  .title {
    font-size: 36rpx;
    color: #fff;
    font-weight: 600;
  }

  .logo {
    position: absolute;
    right: 32rpx;
    width: 100rpx;
    height: 80rpx;
  }
}

.order-status {
  display: flex;
  align-items: center;
  padding: 40rpx 32rpx 20rpx;
  z-index: 2;
  position: relative;

  .isColor {
    background-color: #fff;
  }

  .status-icon {
    width: 100rpx;
    height: 100rpx;
    // background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;

    .success-icon,
    .warning-icon {
      width: 60rpx;
      height: 60rpx;
    }
  }

  .status-info {
    display: flex;
    flex-direction: column;

    .status-text {
      font-size: 40rpx;
      color: #fff;
      font-weight: 600;
      margin-bottom: 8rpx;
    }

    .status-message {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
    }
  }
}

.content {
  flex: 1;
  padding: 0 32rpx;
  margin-top: -20rpx;
  position: relative;
  z-index: 2;
  overflow-y: auto;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */

  // 为所有情况添加底部内边距避免被按钮遮挡
  padding-bottom: 180rpx;
  
  // 隐藏滚动条 - Webkit浏览器
  &::-webkit-scrollbar {
    display: none;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #6c5ce7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.station-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .station-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .gun-info {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 24rpx;
  }

  .divider {
    height: 1rpx;
    background-color: #f0f0f0;
    margin: 24rpx 0;
  }

  .vehicle-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .label {
      font-size: 28rpx;
      color: #666;
    }

    .value {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.payment-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .total-amount-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .total-amount {
      font-size: 48rpx;
      font-weight: 600;
      color: #333;
    }

    .collapse-btn {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #666;

      .arrow-icon {
        margin-left: 8rpx;
        display: inline-block;
        transform: rotate(180deg);
        transition: transform 0.3s ease;
        font-size: 24rpx;

        &.expanded {
          transform: rotate(0deg);
        }
      }
    }
  }

  .payment-details {
    transition: all 0.3s ease;
    overflow: hidden;

    .payment-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .item-label {
        font-size: 28rpx;
        color: #333;
      }

      .item-value {
        font-size: 28rpx;
        color: #333;
      }

      &.discount {
        .item-value {
          color: #ff4d4f;
        }
      }
    }

    .payment-total {
      display: flex;
      justify-content: space-between;
      margin-top: 24rpx;
      padding-top: 24rpx;
      border-top: 1rpx solid #f0f0f0;

      .total-label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
      }

      .total-value {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
      }
    }
  }
}

.charged-kwh {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  font-size: 28rpx;
  color: #333;

  .value {
    font-weight: 600;
  }
}

.charging-info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }

  .info-list {
    margin-top: 20rpx;

    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 28rpx;
        color: #999;
      }

      .info-value {
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}

// 支付按钮样式
.payment-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  z-index: 999;

  .payment-button {
    width: 100%;
    height: 88rpx;
    background-color: #ff4d4f;
    color: #fff;
    font-size: 32rpx;
    font-weight: 600;
    border: none;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      background-color: #d9363e;
    }

    &:disabled,
    &.loading {
      background-color: #ccc !important;
      opacity: 0.6;
      pointer-events: none;
    }

    &.home-button {
      background-color: #d9363e;

      &:active {
        background-color: #d9363e;
      }
    }
  }
}
</style>