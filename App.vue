<script setup>
import { ref, provide } from 'vue'
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useUserStore } from './store/user'
import { useVersionStore } from './store/version.js'
import { useNetworkStore } from './store/network'
import { useGlobalHud } from './composables/useHud'
import ChargingSettlementModal from './components/common/ChargingSettlementModal.vue'

// 显式声明组件（某些uni-app版本需要）
const components = {
  ChargingSettlementModal
}
import { useGlobalSimpleNetworkError } from './composables/useSimpleNetworkError'

// 获取简化版网络异常处理实例
const networkError = useGlobalSimpleNetworkError()
console.log('🔍 [App.vue] 简化版网络异常处理实例:', networkError)
console.log('🔍 [App.vue] 网络异常状态对象:', networkError.networkErrorState)

// 暴露测试函数（开发环境使用）
if (process.env.NODE_ENV === 'development') {
  // 检查window对象是否存在（H5平台才有）
  if (typeof window !== 'undefined') {
    window.showNetworkErrorTest = () => {
      console.log('🔍 [App.vue] 测试显示网络异常弹窗')
      networkError.showNetworkError()
    }
  } else {
    // 在非H5平台，将函数挂载到globalThis上
    globalThis.showNetworkErrorTest = () => {
      console.log('🔍 [App.vue] 测试显示网络异常弹窗')
      networkError.showNetworkError()
    }
  }
}
// 测试国际化文本（同样需要检查平台）
if (process.env.NODE_ENV === 'development') {
  if (typeof window !== 'undefined') {
    window.testNetworkErrorI18n = () => {
      console.log('🌐 [测试] 当前语言:', networkError.getCurrentLocale())
      console.log('🌐 [测试] 弹窗文本:', networkError.getNetworkErrorTexts())
    }
  } else {
    globalThis.testNetworkErrorI18n = () => {
      console.log('🌐 [测试] 当前语言:', networkError.getCurrentLocale())
      console.log('🌐 [测试] 弹窗文本:', networkError.getNetworkErrorTexts())
    }
  }
  // 切换语言并测试（同样需要检查平台）
  if (typeof window !== 'undefined') {
    window.testLanguageSwitch = (lang) => {
      if (lang === 'fr' || lang === 'en') {
        uni.setStorageSync('locale', lang)
        console.log('🌐 [测试] 语言已切换为:', lang)
        console.log('🌐 [测试] 新的弹窗文本:', networkError.getNetworkErrorTexts())
        // 显示弹窗来查看效果
        networkError.showNetworkError()
      } else {
        console.error('❌ [测试] 不支持的语言:', lang, '支持的语言: fr, en')
      }
    }
  } else {
    globalThis.testLanguageSwitch = (lang) => {
      if (lang === 'fr' || lang === 'en') {
        uni.setStorageSync('locale', lang)
        console.log('🌐 [测试] 语言已切换为:', lang)
        console.log('🌐 [测试] 新的弹窗文本:', networkError.getNetworkErrorTexts())
        // 显示弹窗来查看效果
        networkError.showNetworkError()
      } else {
        console.error('❌ [测试] 不支持的语言:', lang, '支持的语言: fr, en')
      }
    }
  }
}

console.log('✅ [App.vue] 简化版网络异常弹窗模块已加载')



// 全局数据
const globalData = ref({
	screenWidth: 375,
	screenHeight: 667,
	statusBarHeight: 20,
	safeAreaInsets: { bottom: 0 }
})

// 提供全局数据给子组件使用
provide('globalData', globalData)

// 生命周期钩子
onLaunch(() => {
	// 获取系统信息
	const systemInfo = uni.getSystemInfoSync()
	// 设置全局变量
	globalData.value = {
		screenWidth: systemInfo.screenWidth,
		screenHeight: systemInfo.screenHeight,
		statusBarHeight: systemInfo.statusBarHeight,
		safeAreaInsets: systemInfo.safeAreaInsets || { bottom: 0 }
	}

	// 初始化应用版本号（Pinia统一版本来源）
	const versionStore = useVersionStore()
	versionStore.initVersion()

	// 初始化全局HUD，应用启动时确保干净状态
	useGlobalHud().hide()

	// 初始化用户状态
	const userStore = useUserStore()
	userStore.initUserFromStorage()

	// 初始化网络状态监听
	const networkStore = useNetworkStore()
	networkStore.initNetworkListener()
	
	// 简化版网络异常处理无需额外初始化
	console.log('✅ 简化版网络异常处理模块已就绪')

	// 详细打印存储信息
	console.log('🔍 [App启动] ========== App.vue 初始化存储信息检查 ==========')
	const uniPhone = uni.getStorageSync('savedPhoneNumber')
	const uniQuickLogin = uni.getStorageSync('enableQuickLogin')
	const piniaUserStore = uni.getStorageSync('user-store')
	
	console.log('📱 [App初始化] uni.storage中的手机号:', uniPhone)
	console.log('⚡ [App初始化] uni.storage中的快速登录状态:', uniQuickLogin)
	console.log('🗄️ [App初始化] Pinia持久化数据:', piniaUserStore)
	console.log('🏪 [App初始化] Store中的手机号:', userStore.getSavedPhoneNumber)
	console.log('✅ [App初始化] Store中的快速登录状态:', userStore.isQuickLoginEnabled)
	console.log('🎯 [App初始化] hasQuickLoginPhone:', userStore.hasQuickLoginPhone)
	console.log('🔍 [App启动] ========== App.vue 初始化完成 ==========\n')
	
	console.log('🎬 [App启动] 等待跳转到welcome页面执行登录检查...')
	
})


onShow(() => {
	console.log('🌟 [应用] App Show - 应用进入前台')
})

onHide(() => {
	console.log('🌙 [应用] App Hide - 应用进入后台')
})


// 检查用户登录状态和决定跳转逻辑
function checkUserLoginStatus(userStore) {
	console.log('=== 开始检查用户登录状态 ===')
	
	// 检查是否已经登录
	if (userStore.loggedIn && userStore.accessToken) {
		console.log('✅ [启动] 用户已登录，无需跳转')
		return
	}

	// 双重检查手机号和快速登录状态
	const storePhone = userStore.getSavedPhoneNumber
	const storeQuickLogin = userStore.isQuickLoginEnabled
	const uniPhone = uni.getStorageSync('savedPhoneNumber')
	const uniQuickLogin = uni.getStorageSync('enableQuickLogin')
	
	console.log('Store中的手机号:', storePhone)
	console.log('Store中的快速登录状态:', storeQuickLogin)
	console.log('UniStorage中的手机号:', uniPhone)
	console.log('UniStorage中的快速登录状态:', uniQuickLogin)
	
	// 优先使用 uni.storage 中的数据，因为它更可靠
	const hasPhone = uniPhone || storePhone
	const quickLoginEnabled = uniQuickLogin || storeQuickLogin
	
	console.log('最终判断 - 手机号:', hasPhone, '快速登录启用:', quickLoginEnabled)

	// 检查是否有保存的手机号用于快速登录
	if (hasPhone && quickLoginEnabled) {
		console.log('📱 [启动] 检测到保存的手机号，跳转到PIN登录页面')
		console.log('📱 [启动] 保存的手机号:', hasPhone)
		
		// 如果 store 中的数据不完整，先更新 store
		if (!storePhone && uniPhone) {
			console.log('更新 store 中的手机号数据')
			userStore.savedPhoneNumber = uniPhone
			userStore.enableQuickLogin = uniQuickLogin
		}
		
		// 延迟跳转确保页面完全加载
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/auth/pin-login/index',
				success: () => {
					console.log('✅ [启动] 成功跳转到PIN登录页面')
				},
				fail: (err) => {
					console.error('❌ [启动] PIN登录页面跳转失败:', err)
					// 如果PIN登录页面跳转失败，回退到正常登录页面
					fallbackToNormalLogin()
				}
			})
		}, 500)
	} else {
		console.log('🔐 [启动] 未检测到快速登录配置，跳转到正常登录页面')
		
		// 延迟跳转确保页面完全加载
		setTimeout(() => {
			uni.reLaunch({
				url: '/pages/auth/login/index',
				success: () => {
					console.log('✅ [启动] 成功跳转到登录页面')
				},
				fail: (err) => {
					console.error('❌ [启动] 登录页面跳转失败:', err)
				}
			})
		}, 500)
	}
}

// 回退到正常登录页面的方法
function fallbackToNormalLogin() {
	console.log('🔄 [启动] 回退到正常登录页面')
	uni.reLaunch({
		url: '/pages/auth/login/index',
		success: () => {
			console.log('✅ [启动] 回退成功')
		},
		fail: (err) => {
			console.error('❌ [启动] 回退失败:', err)
		}
	})
}
</script>

<template>
  <view id="app">
    <!-- 网络异常弹窗使用 uni.showModal，无需在模板中定义 -->
    
    <!-- 充电结算弹窗 - 在home和charging页面使用 -->
    <ChargingSettlementModal 
      :visible="false"
      message=""
    />
  </view>
</template>

<style lang="less">
/*每个页面公共css */
@import url('static/iconfont/iconfont.css');

/* 全局断网弹窗动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 全局复选框样式 */
checkbox {
  .uni-checkbox-input {
    border-radius: 50%;
    width: 36rpx;
    height: 36rpx;
    border: 2rpx solid #ddd;
    background-color: #fff;

    &.uni-checkbox-input-checked {
      background-color: #f23030;
      border-color: #f23030;

      &::before {
        font-size: 28rpx;
        color: #fff;
      }
    }
  }
}

// Less 变量定义
@primary-color: #f23030;
@text-color: #000000;
@text-color-secondary: #999999;
@page-padding: 40rpx;
@button-height: 90rpx;
@input-height: 60rpx;
@font-size-small: 24rpx;
@font-size-base: 28rpx;
@font-size-large: 32rpx;
@font-size-xlarge: 36rpx;
@font-size-xxlarge: 44rpx;

// Less 混入定义
.flex-center() {
	display: flex;
	align-items: center;
	justify-content: center;
}

.safe-area-bottom() {
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}

.base-button() {
	width: 100%;
	height: @button-height;
	border-radius: (@button-height / 2);
	font-size: @font-size-large;
	.flex-center();
	border: none;
	margin: 0;
	padding: 0;
}

.base-input() {
	width: 100%;
	height: @input-height;
	font-size: @font-size-large;
	color: @text-color;
	padding: 0;
	border: none;
	background: transparent;
}

// 全局样式
page {
	font-size: @font-size-base;

	// 隐藏滚动条
	&::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}
}

// 全局隐藏滚动条样式
::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}

// 兼容不同平台的滚动条隐藏
* {
	// Firefox
	scrollbar-width: none;
	// IE
	-ms-overflow-style: none;

	&::-webkit-scrollbar {
		display: none;
		width: 0 !important;
		height: 0 !important;
		-webkit-appearance: none;
		background: transparent;
	}
}

.page-container {
	min-height: 100vh;
	background-color: #fff;
	padding: 0 @page-padding;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}

.safe-area-bottom {
	.safe-area-bottom();
}

.flex-center {
	.flex-center();
}

.primary-button {
	.base-button();
	background: @primary-color;
	color: #fff;
}

.input-base {
	.base-input();
}

// CSS 变量（供组件使用）
:root {
	// 主题颜色
	--primary-color: @primary-color;
	--text-color: @text-color;
	--text-color-secondary: @text-color-secondary;

	// 布局尺寸
	--page-padding: @page-padding;
	--button-height: @button-height;
	--input-height: @input-height;

	// 字体大小
	--font-size-small: @font-size-small;
	--font-size-base: @font-size-base;
	--font-size-large: @font-size-large;
	--font-size-xlarge: @font-size-xlarge;
	--font-size-xxlarge: @font-size-xxlarge;

	// 基础间距
	--safe-area-inset-top: 0px;
	--safe-area-inset-right: 0px;
	--safe-area-inset-bottom: 0px;
	--safe-area-inset-left: 0px;
}

/* 网络异常弹窗使用 uni.showModal，无需自定义样式 */
</style>
