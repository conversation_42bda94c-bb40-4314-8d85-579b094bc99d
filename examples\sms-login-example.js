/**
 * 验证码登录使用示例
 * 展示如何在Vue组件中使用验证码登录功能
 */

// 导入登录相关API
import { smsLogin } from '@/api/modules/auth'
import { sendSmsCode } from '@/api/modules/sms'

/**
 * 验证码登录完整流程示例
 */
export default {
  data() {
    return {
      // 表单数据
      formData: {
        phonenumber: '',
        smsCode: ''
      },
      // 验证码发送状态
      isSendingCode: false,
      countdown: 0,
      // 登录状态
      isLogging: false
    }
  },

  methods: {
    /**
     * 发送验证码
     */
    async sendVerificationCode() {
      // 验证手机号
      if (!this.formData.phonenumber) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      // 手机号格式验证
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(this.formData.phonenumber)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      // 防止重复发送
      if (this.isSendingCode || this.countdown > 0) {
        return
      }

      try {
        this.isSendingCode = true
        
        // 调用发送验证码API
        await sendSmsCode(this.formData.phonenumber)
        
        // 发送成功，开始倒计时
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })
        
        this.startCountdown()
      } catch (error) {
        console.error('发送验证码失败:', error)
        uni.showToast({
          title: error.message || '发送失败，请稍后重试',
          icon: 'none'
        })
      } finally {
        this.isSendingCode = false
      }
    },

    /**
     * 开始倒计时
     */
    startCountdown() {
      this.countdown = 60
      const timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    },

    /**
     * 验证码登录
     */
    async loginWithSms() {
      // 表单验证
      if (!this.formData.phonenumber) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!this.formData.smsCode) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }

      // 验证码长度检查
      if (this.formData.smsCode.length !== 6) {
        uni.showToast({
          title: '请输入6位验证码',
          icon: 'none'
        })
        return
      }

      try {
        this.isLogging = true
        
        // 调用验证码登录API
        const result = await smsLogin({
          phonenumber: this.formData.phonenumber,
          smsCode: this.formData.smsCode
        })

        // 登录成功处理
        this.handleLoginSuccess(result)
        
      } catch (error) {
        console.error('登录失败:', error)
        this.handleLoginError(error)
      } finally {
        this.isLogging = false
      }
    },

    /**
     * 登录成功处理
     */
    handleLoginSuccess(result) {
      const { access_token, refresh_token, user_info } = result

      // 保存token和用户信息
      uni.setStorageSync('access_token', access_token)
      uni.setStorageSync('refresh_token', refresh_token)
      uni.setStorageSync('user_info', user_info)

      // 显示成功提示
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      })

      // 跳转到首页
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/home/<USER>'
        })
      }, 1500)
    },

    /**
     * 登录失败处理
     */
    handleLoginError(error) {
      let errorMessage = '登录失败，请稍后重试'
      
      // 根据错误类型显示不同提示
      if (error.message) {
        if (error.message.includes('验证码')) {
          errorMessage = '验证码错误或已过期'
        } else if (error.message.includes('手机号')) {
          errorMessage = '手机号未注册'
        } else {
          errorMessage = error.message
        }
      }

      uni.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 3000
      })
    }
  }
}

/**
 * 在页面中使用示例：
 * 
 * <template>
 *   <view class="login-container">
 *     <!-- 手机号输入 -->
 *     <input 
 *       v-model="formData.phonenumber" 
 *       placeholder="请输入手机号" 
 *       type="number"
 *     />
 *     
 *     <!-- 验证码输入 -->
 *     <view class="code-input-row">
 *       <input 
 *         v-model="formData.smsCode" 
 *         placeholder="请输入验证码" 
 *         type="number"
 *         maxlength="6"
 *       />
 *       <button 
 *         @click="sendVerificationCode"
 *         :disabled="isSendingCode || countdown > 0"
 *       >
 *         {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
 *       </button>
 *     </view>
 *     
 *     <!-- 登录按钮 -->
 *     <button 
 *       @click="loginWithSms"
 *       :disabled="isLogging"
 *       class="login-btn"
 *     >
 *       {{ isLogging ? '登录中...' : '登录' }}
 *     </button>
 *   </view>
 * </template>
 */
