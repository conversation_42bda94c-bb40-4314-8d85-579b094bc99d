<template>
    <view class="privacy-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">Privacy</text>
                </view>
            </view>
        </view>
        
        <!-- 隐私选项列表 -->
        <view class="privacy-card">
            <!-- 隐私政策 -->
            <view class="privacy-item" @click="navigateTo('/pages/personal-center/privacy-policy')">
                <text class="privacy-label">{{ $t('legal.privacyPolicy') }}</text>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 使用条款 -->
            <view class="privacy-item" @click="navigateTo('/pages/personal-center/terms-of-use')">
                <text class="privacy-label">{{ $t('legal.termsOfUse') }}</text>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 交易规则说明 -->
            <view class="privacy-item" @click="navigateTo('/pages/personal-center/trading-rules')">
                <text class="privacy-label">{{ $t('legal.explanationOfTradingRules') }}</text>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const statusBarHeight = ref(0)

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}

const navigateTo = (url) => {
    uni.navigateTo({ url })
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.privacy-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.privacy-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .privacy-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        
        &:active {
            background-color: #f5f5f5;
        }
        
        .privacy-label {
            font-size: 32rpx;
            color: #333;
            flex: 1;
        }
        
        .arrow-container {
            .iconfont {
                color: #ccc;
                font-size: 32rpx;
            }
        }
    }
    
    .divider {
        height: 1rpx;
        background-color: #f0f0f0;
        margin-left: 32rpx;
        margin-right: 32rpx;
    }
}
</style> 