/**
 * API 相关的通用类型定义
 */

// 通用 API 响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应类型
export interface PageResponse<T> {
  total: number
  list: T[]
  pageNum: number
  pageSize: number
}

// 分页请求参数
export interface PageParams {
  pageNum?: number
  pageSize?: number
  isAsc?: 'asc' | 'desc'
  orderByColumn?: string
}

// 通用操作响应
export interface OperationResponse {
  code: number
  message: string
  data: boolean
}

// 错误响应类型
export interface ErrorResponse {
  code: number
  message: string
  data?: null
}

// 文件上传响应
export interface UploadResponse {
  code: number
  message: string
  data: {
    url: string
    fileName: string
    originalName: string
    size: number
  }
}

// 通用列表查询参数
export interface ListParams extends PageParams {
  keyword?: string
  status?: number
  startTime?: string
  endTime?: string
}

// 地理位置相关
export interface LocationInfo {
  latitude: number
  longitude: number
  address?: string
  city?: string
  province?: string
  country?: string
}

// 时间范围
export interface TimeRange {
  startTime: string
  endTime: string
}

// 统计数据基础类型
export interface StatisticsBase {
  total: number
  today: number
  month: number
  year: number
}

// 金额统计
export interface AmountStatistics extends StatisticsBase {
  currency: string
}

// 数量统计
export interface CountStatistics extends StatisticsBase {
  unit?: string
}

// 请求配置
export interface RequestConfig {
  timeout?: number
  headers?: Record<string, string>
  cancelToken?: any
  withCredentials?: boolean
}

// 注意：具体的模块类型由 index.ts 统一导出
// 这里只定义通用的基础类型
