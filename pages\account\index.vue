<template>
  <view class="account-page" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
    <!-- 顶部状态栏和返回 -->
    <view class="fixed-header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back"></text>
          </view>
          <text class="title">{{ $t('nav.account') }}</text>
        </view>
      </view>
    </view>

    <!-- 账户余额卡片 -->
    <view class="balance-card">
      <view class="avatar">
        <text class="iconfont icon-toubuyonghutouxiang"></text>
      </view>
      <text class="balance-label">{{ $t('account.balance') }}</text>
      <text class="balance-amount">{{ balance }} F</text>
    </view>

    <!-- Add Money 按钮 -->
    <view class="add-money-btn" @click="addMoney">
      <image src="/static/images/recharge.png" class="add-money-icon" />
      <text class="add-money-text">{{ $t('account.recharge') }}</text>
      <text class="add-money-plus">+</text>
    </view>

    <!-- 历史记录 -->
    <!-- <view class="history-section">
      <text class="history-title">{{ $t('account.history') || 'HISTORIQUES' }}</text>
      <view class="history-list">
        <view class="history-item" v-for="(item, idx) in histories" :key="idx"
              @click="item.isPending ? goToPay(item) : null">
          <view class="history-info">
            <text class="history-main">{{ item.title }}</text>
            <text class="history-sub">{{ item.subtitle }}</text>
          </view>
          <view class="history-amount-block"> -->
			<!-- 不需要去支付  -->
           <!-- <text :class="['history-amount', item.amount > 0 ? 'income' : (item.amount < 0 ? 'expense' : ''), item.isPending ? 'pending' : '']">
              {{ item.isPending ? $t('payment.goToPay') || 'Go to pay' : (item.amount > 0 ? '+' : '') + item.amount + ' F' }}
            </text> -->
			<!-- <text :class="['history-amount', item.amount > 0 ? 'income' : (item.amount < 0 ? 'expense' : ''), item.isPending ? '' : '']">
			  {{ item.amount + ' F' }}
			</text> -->
            <!-- <text class="history-meta">{{ item.meta }}</text>
          </view> -->
         <!-- <view v-if="item.isPending" class="go-arrow">
            <text class="arrow-icon">›</text>
          </view> -->
        <!-- </view>
      </view> -->
    <!-- </view> -->
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/user'

// 使用国际化
const { t } = useI18n()
const statusBarHeight = ref(0)
const userStore = useUserStore()

// 从 store 中获取用户余额
const balance = computed(() => {
    const userInfo = userStore.getUserInfo
    if (userInfo) {
        return userInfo.regularRechargeAccount || userInfo.accountBalance || '0.00'
    }
    return '0.00'
})

const headerHeight = computed(() => statusBarHeight.value + 44) // 44px为标题栏高度

const histories = ref([
  {
    id: '************',
    icon: '/static/images/ARNIO.png',
    title: 'ARNIO',
    subtitle: 'Primacenter',
    amount: -5000,
    meta: '50kw. 21.avr.2025.',
    isPending: false
  },
  {
    id: '************',
    icon: '/static/images/wave.png',
    title: 'Wave',
    subtitle: 'Mobil Money',
    amount: 5000,
    meta: '21.avr.2025. - 08:31',
    isPending: false
  },
  {
    id: '************',
    icon: '/static/images/orange.png',
    title: 'Orange',
    subtitle: 'Mobil Money',
    amount: 5000,
    meta: '21.avr.2025. - 08:31',
    isPending: false
  },
  {
    id: '************',
    icon: '/static/images/ARNIO.png',
    title: 'ARNIO',
    subtitle: 'Station Shell',
    amount: -5000,
    meta: '30kw. 21.avr.2025.',
    isPending: false
  },
  {
    id: '************',
    icon: '/static/images/ARNIO.png',
    title: 'ARNIO',
    subtitle: 'Arnio charging 01 PIL',
    amount: -280,
    meta: '29.425kWh. 22.avr.2025.',
    isPending: true
  }
])

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
  uni.navigateBack()
}

const addMoney = () => {
  uni.navigateTo({
    url: '/pages/account/recharge/index',
    animationType: 'slide-in-right',
    animationDuration: 300
  })
}

const goToPay = (item) => {
  uni.navigateTo({
    url: `/pages/payment/order-pending?orderId=${item.id}`,
    animationType: 'slide-in-right',
    animationDuration: 300
  })
}
</script>

<style lang="scss" scoped>
@import '@/static/iconfont/iconfont.css';
.account-page {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0,0,0,0.04);
}
.status-bar {
  background-color: #fff;
  width: 100%;
}
.header {
  background: #fff;
  width: 100%;
  .header-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;
    .back-btn {
      width: 88rpx;
      height: 44px;
      display: flex;
      align-items: center;
      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }
    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }
  }
}
.balance-card {
  margin: 16rpx 32rpx;
  background: #168CFA;
  border-radius: 24rpx;
  padding: 48rpx 0 32rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(22, 140, 250, 0.2);
  .avatar {
    position: absolute;
    top: 24rpx;
    left: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56rpx;
    height: 56rpx;
    border-radius: 50%;
    
    .iconfont {
      font-size: 60rpx;
      color: #fff;
      
    }
  }
  .balance-label {
    color: #fff;
    font-size: 24rpx;
    margin-top: 16rpx;
    margin-bottom: 16rpx;
    letter-spacing: 4rpx;
  }
  .balance-amount {
    color: #fff;
    font-size: 56rpx;
    font-weight: bold;
    letter-spacing: 6rpx;
  }
}
.add-money-btn {
  margin: 32rpx 32rpx 0 32rpx;
  border: 2rpx dashed #bdbdbd;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 28rpx 0;
  background: #fff;
  transition: all 0.2s;
  
  &:active {
    background: #f9f9f9;
    transform: scale(0.98);
  }
  
  .add-money-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
  }
  .add-money-text {
    font-size: 32rpx;
    color: #222;
    font-weight: 500;
    margin-right: 16rpx;
  }
  .add-money-plus {
    font-size: 48rpx;
    color: #222;
    font-weight: 600;
  }
}
.history-section {
  margin: 48rpx 32rpx 32rpx 32rpx;
  padding-bottom: 40rpx;
  
  .history-title {
    font-size: 28rpx;
    color: #222;
    font-weight: 600;
    letter-spacing: 2rpx;
    margin-bottom: 24rpx;
    display: block;
  }
  .history-list {
    .history-item {
      display: flex;
      align-items: center;
      margin-bottom: 36rpx;
      padding: 24rpx 20rpx;
      border-radius: 16rpx;
      background: #fff;
      box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);
      transition: transform 0.2s;
      position: relative;
      
      &:active {
        transform: scale(0.98);
      }
            

      
      .history-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        .history-main {
          font-size: 28rpx;
          color: #222;
          font-weight: 600;
        }
        .history-sub {
          font-size: 24rpx;
          color: #888;
        }
      }
      .history-amount-block {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        .history-amount {
          font-size: 32rpx;
          font-weight: 600;
          &.income {
            color: #1ED69E;
          }
          &.expense {
            color: #FF4D4F;
          }
          &.pending {
            color: #0088FF;
            background-color: rgba(0, 136, 255, 0.1);
            padding: 6rpx 20rpx;
            border-radius: 30rpx;
            font-size: 28rpx;
          }
        }
        .history-meta {
          font-size: 20rpx;
          color: #bbb;
          margin-top: 6rpx;
        }
      }
      
      .go-arrow {
        margin-left: 16rpx;
        
        .arrow-icon {
          font-size: 36rpx;
          color: #0088FF;
          font-weight: 400;
        }
      }
    }
  }
}
</style> 