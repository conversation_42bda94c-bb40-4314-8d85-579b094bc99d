<template>
  <view class="success-page">
    <!-- 顶部状态栏 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 页面标题 -->
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <text class="iconfont icon-back"></text>
        </view>
        <text class="title">Add money</text>
      </view>
    </view>
    
    <!-- 成功内容 -->
    <view class="success-content">
      <!-- 成功图标 -->
      <view class="success-icon">
        <view class="check-circle">
          <text class="check-mark">✓</text>
        </view>
        <view class="small-circle circle-top-right"></view>
        <view class="small-circle circle-bottom-left"></view>
      </view>
      
      <!-- 成功信息 -->
      <view class="success-info">
        <text class="success-title">Payment successful</text>
        <text class="success-amount">3000 F</text>
      </view>
      
      <!-- 按钮区域 -->
      <view class="buttons">
        <button class="primary-btn" @click="viewDetails">View details</button>
        <text class="secondary-btn" @click="returnToHomepage">Return to homepage</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 状态栏高度
const statusBarHeight = ref(20);

// 页面加载时获取状态栏高度
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight;
    }
  });
});

// 返回按钮
const goBack = () => {
  uni.navigateBack();
};

// 查看详情
const viewDetails = () => {
  // 跳转到订单详情页面
  uni.navigateTo({
    url: '/pages/payment/order-detail'
  });
};

// 返回首页
const returnToHomepage = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  });
};
</script>

<style lang="scss" scoped>
@import '@/static/iconfont/iconfont.css';

.success-page {
  min-height: 100vh;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;
}

.status-bar {
  width: 100%;
  background-color: #fff;
}

.header {
  background: #fff;
  width: 100%;
  
  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    
    .back-btn {
      width: 88rpx;
      height: 44px;
      display: flex;
      align-items: center;
      z-index: 10;
      
      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }
    
    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      pointer-events: none;
    }
  }
}

.success-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 32rpx;
  margin-top: 80rpx;
}

.success-icon {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 60rpx;
  
  .check-circle {
    width: 180rpx;
    height: 180rpx;
    background-color: #FF4D4F;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    
    .check-mark {
      color: white;
      font-size: 100rpx;
      font-weight: 300;
      line-height: 1;
    }
  }
  
  .small-circle {
    width: 20rpx;
    height: 20rpx;
    background-color: #FF8F1F;
    border-radius: 50%;
    position: absolute;
  }
  
  .circle-top-right {
    top: 0;
    right: 40rpx;
  }
  
  .circle-bottom-left {
    bottom: 40rpx;
    left: 0;
  }
}

.success-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
  
  .success-title {
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }
  
  .success-amount {
    font-size: 60rpx;
    font-weight: 700;
    color: #333;
  }
}

.buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .primary-btn {
    width: 100%;
    height: 88rpx;
    background-color: #FF4D4F;
    color: white;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40rpx;
    border: none;
  }
  
  .secondary-btn {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    padding: 20rpx;
  }
}
</style> 