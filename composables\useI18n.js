/**
 * 国际化组合式函数
 * 提供便捷的国际化功能
 */
import { useI18n as useVueI18n } from 'vue-i18n'
import { switchLanguage, getCurrentLanguage, getSupportedLanguages } from '@/locale/index.js'

export function useI18n() {
  const { t, locale } = useVueI18n()
  
  return {
    // 翻译函数
    t,
    
    // 当前语言
    locale,
    
    // 切换语言
    switchLanguage: (langCode) => {
      switchLanguage(langCode)
    },
    
    // 获取当前语言
    getCurrentLanguage,
    
    // 获取支持的语言列表
    getSupportedLanguages,
    
    // 格式化日期
    formatDate: (date, options = {}) => {
      const currentLocale = getCurrentLanguage()
      const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }
      
      return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(new Date(date))
    },
    
    // 格式化数字
    formatNumber: (number, options = {}) => {
      const currentLocale = getCurrentLanguage()
      return new Intl.NumberFormat(currentLocale, options).format(number)
    },
    
    // 格式化货币
    formatCurrency: (amount, currency = 'EUR') => {
      const currentLocale = getCurrentLanguage()
      return new Intl.NumberFormat(currentLocale, {
        style: 'currency',
        currency: currency
      }).format(amount)
    },
    
    // 获取本地化的文本方向
    getTextDirection: () => {
      const currentLocale = getCurrentLanguage()
      // 大部分语言都是从左到右，阿拉伯语、希伯来语等是从右到左
      const rtlLanguages = ['ar', 'he', 'fa', 'ur']
      return rtlLanguages.includes(currentLocale) ? 'rtl' : 'ltr'
    },
    
    // 检查是否为当前语言
    isCurrentLanguage: (langCode) => {
      return getCurrentLanguage() === langCode
    },
    
    // 获取语言显示名称
    getLanguageName: (langCode) => {
      const languages = getSupportedLanguages()
      const language = languages.find(lang => lang.code === langCode)
      return language ? language.name : langCode
    },
    
    // 获取语言标志
    getLanguageFlag: (langCode) => {
      const languages = getSupportedLanguages()
      const language = languages.find(lang => lang.code === langCode)
      return language ? language.flag : '🌐'
    }
  }
}
