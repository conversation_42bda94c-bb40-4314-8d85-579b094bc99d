<template>
  <view class="charging-settlement-modal" v-if="visible">
    <view class="modal-mask"></view>
    <view class="modal-content">
      <!-- Loading图标 -->
      <view class="loading-container">
        <view class="loading-spinner"></view>
      </view>
      
      <!-- 提示文字 -->
      <text class="message">{{ message }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  message: {
    type: String,
    default: 'Calcul de la facture en cours, veuillez patienter...'
  }
})

const emit = defineEmits(['close'])

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    console.log('充电结算弹窗显示:', props.message)
  }
})
</script>

<style lang="less" scoped>
.charging-settlement-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000000;
  pointer-events: auto;

  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
  }

  .modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 520rpx;
    padding: 60rpx 40rpx;
    background-color: #fff;
    border-radius: 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease-out;
  }

  .loading-container {
    margin-bottom: 30rpx;
    
    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 5rpx solid #f3f3f3;
      border-top: 5rpx solid #007aff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .message {
    font-size: 28rpx;
    color: #333;
    text-align: center;
    line-height: 1.5;
    font-weight: 500;
  }
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>