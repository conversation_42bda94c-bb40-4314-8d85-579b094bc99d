import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useModel3DStore = defineStore('model3d', () => {
  // 当前模型类型
  const currentModelType = ref('default')
  
  // 模型切换触发器（用于强制更新）
  const modelSwitchTrigger = ref(0)
  
  // 切换模型类型
  const switchModelType = (newType) => {
    console.log('🏪 Pinia Store: 切换模型类型', currentModelType.value, '->', newType)
    
    if (currentModelType.value !== newType) {
      currentModelType.value = newType
      // 增加触发器值，强制触发组件更新
      modelSwitchTrigger.value++
      
      console.log('✅ Pinia Store: 模型类型已更新', {
        newType: currentModelType.value,
        trigger: modelSwitchTrigger.value
      })
    } else {
      console.log('⏭️ Pinia Store: 模型类型未变化，跳过更新')
    }
  }
  
  // 获取当前模型类型
  const getCurrentModelType = () => {
    return currentModelType.value
  }
  
  // 重置状态
  const resetState = () => {
    currentModelType.value = 'default'
    modelSwitchTrigger.value = 0
    console.log('🔄 Pinia Store: 状态已重置')
  }
  
  return {
    currentModelType,
    modelSwitchTrigger,
    switchModelType,
    getCurrentModelType,
    resetState
  }
})
