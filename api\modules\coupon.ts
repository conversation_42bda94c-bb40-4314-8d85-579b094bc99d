/**
 * 优惠券相关API
 */
import Request from '@/utils/request'

// 优惠券相关类型定义
export interface CouponReceiveItem {
  id: string
  name: string
  type: number
  amount: number
  minAmount: number
  validDays: number
  description?: string
  status: number
  createTime: string
}

export interface MyCouponItem {
  id: string
  name: string
  type: number
  amount: number
  minAmount: number
  status: number
  startTime: string
  endTime: string
  createTime: string
  useTime?: string
  orderId?: string
}

export interface CouponListParams {
  pageNum?: number
  pageSize?: number
  isAsc?: 'asc' | 'desc'
  status?: number
}

export interface CouponReceiveListResponse {
  code: number
  message: string
  data: CouponReceiveItem[]
}

export interface MyCouponListResponse {
  code: number
  message: string
  data: {
    total: number
    list: MyCouponItem[]
    pageNum: number
    pageSize: number
  }
}

export interface CouponReceiveResponse {
  code: number
  message: string
  data: boolean
}

export interface BatchReceiveResult {
  status: 'fulfilled' | 'rejected'
  value?: CouponReceiveResponse
  reason?: any
}

/**
 * 获取领取中心优惠券列表
 * @returns {Promise<CouponReceiveListResponse>} 优惠券列表
 */
export const getCouponReceiveList = (): Promise<CouponReceiveListResponse> => {
  return Request.get('/coupon/storeCoupon/receive/list')
}

/**
 * 获取我的优惠券列表
 * @param {CouponListParams} params - 查询参数
 * @returns {Promise<MyCouponListResponse>} 我的优惠券列表
 */
export const getMyCouponList = (params: CouponListParams = {}): Promise<MyCouponListResponse> => {
  const defaultParams: Required<CouponListParams> = {
    pageNum: 1,
    pageSize: 50,
    isAsc: 'asc',
    status: 0
  }
  return Request.get('/app/coupon/page', { 
    params: { ...defaultParams, ...params } 
  })
}

/**
 * 领取优惠券
 * @param {string} couponId - 优惠券ID
 * @returns {Promise<CouponReceiveResponse>} 领取结果
 */
export const receiveCoupon = (couponId: string): Promise<CouponReceiveResponse> => {
  if (!couponId) {
    return Promise.reject(new Error('优惠券ID不能为空'))
  }
  return Request.get('/app/coupon/receiveCoupon', { 
    params: { couponId } 
  })
}

/**
 * 批量领取优惠券
 * @param {string[]} couponIds - 优惠券ID数组
 * @returns {Promise<BatchReceiveResult[]>} 批量领取结果
 */
export const batchReceiveCoupons = (couponIds: string[]): Promise<BatchReceiveResult[]> => {
  if (!Array.isArray(couponIds) || couponIds.length === 0) {
    return Promise.reject(new Error('优惠券ID列表不能为空'))
  }
  
  const requests = couponIds.map(id => receiveCoupon(id))
  return Promise.allSettled(requests)
}
