/**
 * 为pinia-plugin-persistedstate提供必要的polyfill
 */

// destr函数的polyfill
export function destr(val) {
  if (typeof val !== 'string') {
    return val;
  }
  
  try {
    return JSON.parse(val);
  } catch (err) {
    return val;
  }
}

// deepPickUnsafe函数的polyfill
export function deepPickUnsafe(obj, paths) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  const result = Array.isArray(obj) ? [] : {};
  
  for (const path of paths) {
    const parts = path.split('.');
    let current = obj;
    let target = result;
    let valid = true;
    
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      
      if (current === undefined || current === null) {
        valid = false;
        break;
      }
      
      if (i === parts.length - 1) {
        target[part] = current[part];
      } else {
        current = current[part];
        if (target[part] === undefined) {
          target[part] = Array.isArray(current) ? [] : {};
        }
        target = target[part];
      }
    }
  }
  
  return result;
}

// deepOmitUnsafe函数的polyfill
export function deepOmitUnsafe(obj, paths) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  const result = JSON.parse(JSON.stringify(obj));
  
  for (const path of paths) {
    const parts = path.split('.');
    let current = result;
    
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (current[part] === undefined || current[part] === null) {
        break;
      }
      current = current[part];
    }
    
    const lastPart = parts[parts.length - 1];
    if (current && typeof current === 'object') {
      delete current[lastPart];
    }
  }
  
  return result;
}

// 安装全局polyfill
export function installPolyfills() {
  // #ifdef H5
  if (typeof window !== 'undefined') {
    // 为全局对象添加polyfill
    window.destr = window.destr || destr;
    window.deepPickUnsafe = window.deepPickUnsafe || deepPickUnsafe;
    window.deepOmitUnsafe = window.deepOmitUnsafe || deepOmitUnsafe;

    console.log('已安装pinia-plugin-persistedstate所需的polyfill');
  }
  // #endif

  // #ifdef APP-PLUS
  // APP端不需要window对象的polyfill
  console.log('APP端环境，跳过window polyfill');
  // #endif

  // #ifdef MP
  // 小程序端不需要window对象的polyfill
  console.log('小程序端环境，跳过window polyfill');
  // #endif
}

// 默认导出安装函数
export default installPolyfills; 