<template>
    <view class="payment-password-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">Payment Password</text>
                </view>
            </view>
        </view>
        
        <!-- 密码修改区域 -->
        <view class="password-card">
            <!-- 当前密码 -->
            <view class="password-input-group">
                <text class="input-label">Current Payment Password</text>
                <view class="input-container">
                    <input
                        class="password-input"
                        :type="showCurrentPassword ? 'text' : 'password'"
                        v-model="currentPassword"
                        placeholder="Enter your current payment password"
                        maxlength="6"
                    />
                    <text
                        class="iconfont"
                        :class="showCurrentPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleCurrentPassword"
                    ></text>
                </view>
            </view>
            
            <!-- 新密码 -->
            <view class="password-input-group">
                <text class="input-label">New Payment Password</text>
                <view class="input-container">
                    <input
                        class="password-input"
                        :type="showNewPassword ? 'text' : 'password'"
                        v-model="newPassword"
                        placeholder="Enter your new payment password"
                        maxlength="6"
                    />
                    <text
                        class="iconfont"
                        :class="showNewPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleNewPassword"
                    ></text>
                </view>
                <text class="password-hint">Payment password must be 6 digits</text>
            </view>
            
            <!-- 确认新密码 -->
            <view class="password-input-group">
                <text class="input-label">Confirm New Payment Password</text>
                <view class="input-container">
                    <input
                        class="password-input"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        v-model="confirmPassword"
                        placeholder="Confirm your new payment password"
                        maxlength="6"
                    />
                    <text
                        class="iconfont"
                        :class="showConfirmPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleConfirmPassword"
                    ></text>
                </view>
            </view>
        </view>
        
        <!-- 保存按钮 -->
        <view class="save-btn" :class="{ disabled: !isFormValid }" @click="savePassword">
            <text>Save</text>
        </view>
        
        <!-- 忘记密码链接 -->
        <view class="forgot-password" @click="forgotPassword">
            <text>Forgot payment password?</text>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const statusBarHeight = ref(0)
const currentPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)

const isFormValid = computed(() => {
    return currentPassword.value.length >= 4 &&
           newPassword.value.length >= 4 &&
           confirmPassword.value === newPassword.value
})

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}

const toggleCurrentPassword = () => {
    showCurrentPassword.value = !showCurrentPassword.value
}

const toggleNewPassword = () => {
    showNewPassword.value = !showNewPassword.value
}

const toggleConfirmPassword = () => {
    showConfirmPassword.value = !showConfirmPassword.value
}

const savePassword = () => {
    if (!isFormValid.value) {
        if (currentPassword.value.length < 4) {
            uni.showToast({
                title: 'Please enter current payment password',
                icon: 'none',
                duration: 2000
            })
            return
        }

        if (newPassword.value.length < 4) {
            uni.showToast({
                title: 'New payment password must be at least 4 digits',
                icon: 'none',
                duration: 2000
            })
            return
        }

        if (confirmPassword.value !== newPassword.value) {
            uni.showToast({
                title: 'Passwords do not match',
                icon: 'none',
                duration: 2000
            })
            return
        }

        return
    }

    // 这里应该调用后端API来修改支付密码
    // 暂时显示成功消息
    uni.showToast({
        title: 'Payment password saved successfully',
        icon: 'success',
        duration: 2000,
        success: function() {
            setTimeout(() => {
                uni.navigateBack()
            }, 2000)
        }
    })
}

const forgotPassword = () => {
    uni.navigateTo({
        url: '/pages/personal-center/payment-password-reset'
    })
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.payment-password-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.password-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .password-input-group {
        margin-bottom: 32rpx;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        .input-label {
            font-size: 28rpx;
            color: #333;
            margin-bottom: 16rpx;
            display: block;
        }
        
        .input-container {
            display: flex;
            align-items: center;
            border-bottom: 1rpx solid #e0e0e0;
            padding-bottom: 12rpx;
            
            .password-input {
                flex: 1;
                height: 64rpx;
                font-size: 28rpx;
            }
            
            .iconfont {
                font-size: 40rpx;
                color: #999;
                padding: 0 16rpx;
            }
        }
        
        .password-hint {
            font-size: 24rpx;
            color: #999;
            margin-top: 8rpx;
            display: block;
        }
    }
}

.save-btn {
    margin: 64rpx 32rpx;
    background: #FF3B30;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);
    
    text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
    }
    
    &:active {
        opacity: 0.9;
    }
    
    &.disabled {
        background: #cccccc;
        box-shadow: none;
    }
}

.forgot-password {
    text-align: center;
    margin-top: 32rpx;
    
    text {
        font-size: 28rpx;
        color: #168CFA;
        text-decoration: underline;
    }
}
</style> 