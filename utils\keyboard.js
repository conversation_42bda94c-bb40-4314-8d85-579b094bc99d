/**
 * 数字键盘输入处理工具
 * 用于处理Android和iOS设备上的数字键盘输入
 */

// 设置数字键盘并聚焦输入框
export const setupNumericKeyboard = (inputRef) => {
  if (!inputRef) return;
  
  // 先隐藏当前键盘
  uni.hideKeyboard();
  
  // 延迟执行，确保键盘隐藏完成
  setTimeout(() => {
    try {
      // 获取实际DOM元素
      const inputEl = inputRef.$el || inputRef;
      
      // 设置多种属性强制数字键盘
      if (inputEl) {
        inputEl.setAttribute('type', 'tel');
        inputEl.setAttribute('pattern', '[0-9]*');
        inputEl.setAttribute('inputmode', 'numeric');
        inputEl.setAttribute('x-inputmode', 'numeric');
        inputEl.setAttribute('data-keyboard', 'number');
      }
      
      // 聚焦输入框
      if (typeof inputRef.focus === 'function') {
        inputRef.focus();
      }
      
      // 安卓特定处理
      // #ifdef APP-PLUS-ANDROID
      try {
        const context = plus.android.importClass("android.content.Context");
        const inputMethodManager = plus.android.importClass("android.view.inputmethod.InputMethodManager");
        const windowManager = plus.android.runtimeMainActivity().getWindowManager();
        const imm = plus.android.runtimeMainActivity().getSystemService(context.INPUT_METHOD_SERVICE);
        const view = plus.android.runtimeMainActivity().getWindow().getDecorView();
        
        // 强制显示数字键盘
        imm.showSoftInput(view, inputMethodManager.SHOW_FORCED);
        imm.toggleSoftInput(inputMethodManager.SHOW_FORCED, inputMethodManager.HIDE_IMPLICIT_ONLY);
        
        // 设置键盘类型为数字
        const webview = plus.webview.currentWebview();
        if (webview) {
          webview.setStyle({
            softinputMode: 'adjustResize',
            softinputNavBar: 'none'
          });
        }
      } catch (e) {
        console.error('Android keyboard error:', e);
      }
      // #endif
      
      // iOS特定处理
      // #ifdef APP-PLUS-IOS
      try {
        const webview = plus.webview.currentWebview();
        if (webview) {
          webview.setStyle({
            softinputMode: 'adjustResize'
          });
        }
        
        // 使用原生API设置键盘类型
        if (plus.os.name === 'iOS') {
          const UIKeyboardTypeNumberPad = 4;
          const UITextAutocapitalizationTypeNone = 0;
          
          const textField = plus.ios.invoke(inputEl, 'valueForKey:', 'textField');
          if (textField) {
            plus.ios.invoke(textField, 'setKeyboardType:', UIKeyboardTypeNumberPad);
            plus.ios.invoke(textField, 'setAutocapitalizationType:', UITextAutocapitalizationTypeNone);
            plus.ios.invoke(textField, 'setReturnKeyType:', 9); // Done
          }
        }
      } catch (e) {
        console.error('iOS keyboard error:', e);
      }
      // #endif
    } catch (e) {
      console.error('Error setting keyboard type:', e);
    }
  }, 300); // 增加延迟时间，确保DOM已经渲染完成
};

// 创建一个输入框配置对象，用于设置数字输入框的属性
export const numericInputProps = {
  type: 'tel',
  pattern: '[0-9]*',
  inputmode: 'numeric',
  'adjust-position': false,
  'cursor-spacing': 0,
  'hold-keyboard': true,
  'confirm-hold': true,
  'focus': false // 初始不聚焦
}; 