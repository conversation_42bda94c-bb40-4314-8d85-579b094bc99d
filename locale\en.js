/**
 * 英语语言包
 */
export default {
  // 通用
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    confirmCancel: 'Confirm Cancellation',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
    yes: 'Yes',
    no: 'No',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset',
    close: 'Close',
    open: 'Open',
    select: 'Select',
    clear: 'Clear',
    continue: 'Continue',
    refresh: 'Refresh',
    other: 'Other',
    go: 'GO',
    activate: 'Activate',
    notSet: 'Not set',
    processing: 'Processing...',
    connecting: 'Connecting...',
    connected: 'Connected',
    disconnected: 'Disconnected',
    failed: 'Failed',
    completed: 'Completed',
    unknown: 'Unknown',
    retry: 'Retry',
    featureNotAvailable: 'This feature is not available yet',
    networkError: 'Network Error',
    networkErrorMessage: 'Network connection failed. Please check your network settings and try again.'
  },

  // 弹窗和提示
  popup: {
    title: 'Title',
    message: 'Message',
    hint: 'Hint',
    notice: 'Notice',
    // 扫码弹窗相关
    scanSuccess: 'Scan Successful',
    startingCharging: 'Starting Charging',
    chargingFailed: 'Charging Failed',
    processingPayment: 'Processing Payment',
    connectingToPile: 'Connecting to Charging Pile',
    preparingCharging: 'Preparing Charging Session',
    chargingStarted: 'Charging Started Successfully',
    noActiveOrder: 'No Active Charging Order',
    noActiveOrderMessage: 'You currently have no active charging session',
    pleaseStartCharging: 'Please scan QR code to start charging',
    gotIt: 'Got It',
    // 充电步骤
    step1: 'Scanning QR Code',
    step2: 'Verifying Information',
    step3: 'Processing Payment',
    step4: 'Starting Charging',
    stepCompleted: 'Completed',
    // 设备连接
    connectingDevice: 'Connecting to device...',
    deviceConnecting: 'Device Connecting',
    connectionTimeout: 'Connection Timeout',
    connectionTimeoutMessage: 'Failed to connect to device',
    cancelOrder: 'Cancel Order',
    retryConnection: 'Retry Connection',
    clickAgainToCancel: 'Click again to cancel order',
    keepConnection: 'Please keep your phone connected to the charging pile',
    alert: 'Alert',
    prompt: 'Prompt',
    input: 'Input',
    pleaseInput: 'Please input',
    inputHint: 'Input hint',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    okText: 'OK',
    gotIt: 'Got it',
    retry: 'Retry',
    ignore: 'Ignore'
  },

  // 加载和状态
  loading: {
    default: 'Loading...',
    processing: 'Processing...',
    saving: 'Saving...',
    loading: 'Loading...',
    uploading: 'Uploading...',
    downloading: 'Downloading...',
    connecting: 'Connecting...',
    searching: 'Searching...',
    submitting: 'Submitting...',
    verifying: 'Verifying...',
    authenticating: 'Authenticating...',
    initializing: 'Initializing...',
    updating: 'Updating...',
    deleting: 'Deleting...',
    creating: 'Creating...',
    fetching: 'Fetching...',
    syncing: 'Syncing...',
    sending: 'Sending...',
    authenticating: 'Authenticating...'
  },

  // 错误和异常
  errors: {
    networkError: 'Network error',
    serverError: 'Server error',
    unknownError: 'Unknown error',
    timeout: 'Timeout',
    connectionFailed: 'Connection failed',
    loadFailed: 'Load failed',
    locationFailed: 'Failed to get location',
    saveFailed: 'Save failed',
    uploadFailed: 'Upload failed',
    downloadFailed: 'Download failed',
    deleteFailed: 'Delete failed',
    updateFailed: 'Update failed',
    createFailed: 'Create failed',
    authFailed: 'Authentication failed',
    permissionDenied: 'Permission denied',
    notFound: 'Not found',
    invalidData: 'Invalid data',
    operationFailed: 'Operation failed',
    processingFailed: 'Processing failed',
    locationFailed: 'Unable to get location information',
    networkError: 'Network error, please check your connection',
    cameraPermissionDenied: 'Camera permission denied',
    noCameraAvailable: 'No camera available',
    scanFailed: 'Scan failed',
    qrCodeRecognitionFailed: 'QR code recognition failed'
  },

  // 导航
  nav: {
    home: 'Home',
    stations: 'Stations',
    charging: 'Charging',
    account: 'Account',
    settings: 'Settings',
    help: 'Help',
    about: 'About',
    map: 'Map'
  },

  // 地图相关
  map: {
    realTimeLocationOn: 'Real-time location enabled',
    realTimeLocationOff: 'Real-time location disabled',
    myLocation: 'My Location',
    gettingLocation: 'Getting location...',
    locatingPosition: 'Locating your position...'
  },

  // 认证相关
  auth: {
    createAccount: 'Create Account',
    enterPhoneNumber: 'Enter your phone number',
    phoneNumberPlaceholder: '225 01 40 89 29 60',
    invalidPhoneFormat: 'Please enter a valid phone number (225 + 8-10 digits)',
    alreadyHaveAccount: 'Already have an account?',
    
    enterVerificationCode: 'Enter the verification code sent by SMS to',
    enterCode: 'Enter verification code',
    codeSentTo: 'We\'ve sent a verification code to',
    resendCodeIn: 'Resend code in',
    resendCode: 'Resend code',
    verifying: 'Verifying...',
    verificationSuccess: 'Verification successful!',
    verificationFailed: 'Verification failed',
    invalidCode: 'Invalid verification code',
    sending: 'Sending...',
    codeSent: 'Verification code sent',
    sendFailed: 'Failed to send verification code',
    
    enterFullName: 'Please enter your full legal name',
    nameWillBeUsedFor: 'This name will be used for your account',
    displayName: 'Display name',
    firstNameRequired: 'First name is required',
    lastNameRequired: 'Last name is required',
    nameOnlyLetters: 'Name can only contain letters',
    nameMinLength: 'Name must be at least 2 characters',
    
    setNewPassword: 'Set a new secret code',
    enter4DigitPassword: 'Enter your 4-digit password',
    passwordTips: 'Security Tips',
    passwordTip1: 'Use 4 digits for your payment password',
    passwordTip2: 'Avoid using simple sequences like 1234',
    passwordTip3: 'Don\'t reuse passwords from other accounts',
    passwordMust4Digits: 'Password must be exactly 4 digits',
    passwordTooSimple: 'Password is too simple, please choose another',
    biometricNotAvailable: 'Biometric authentication not available',
    
    confirmPassword: 'Confirm your password',
    reenterPassword: 'Re-enter your 4-digit password',
    passwordNotMatch: 'Passwords do not match',
    agreementPrefix: 'By checking, I accept the',
    and: 'and',
    
    registering: 'Registering...',
    registerSuccess: 'Registration successful!',
    registerFailed: 'Registration failed',
    autoLoggingIn: 'Auto logging in...',
    loginSuccess: 'Login successful!',
    registerSuccessLogin: 'Registration successful! Please login.'
  },

  // 用户相关
  user: {
    login: 'Login',
    logout: 'Logout',
    register: 'Register',
    profile: 'Personal Center',
    username: 'Username',
    password: 'Password',
    email: 'Email',
    phone: 'Phone Number',
    name: 'Account Name',
    avatar: 'Avatar',
    changePassword: 'Change Password',
    forgotPassword: 'Forgot Password',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    logoutConfirm: 'Are you sure you want to logout?',
    registerSuccess: 'Registration successful',
    updateSuccess: 'Update successful',
    verificationCode: 'Verification Code',
    send: 'Get Code',
    emailOrPhone: 'Email mobile number',
    noAccount: 'Don\'t have an account?',
    thirdPartyLogin: 'Third-party software authorized login',
    phoneRequired: 'Please enter your phone number',
    phoneFormatError: 'Please enter a valid phone number',
    passwordRequired: 'Please enter your password',
    verificationCodeRequired: 'Please enter the verification code',
    agreementText1: 'I understand and agree to the Arnio',
    agreementText2: 'and',
    termsOfService: 'Terms of Service',
    givenName: 'Given Name',
    familyName: 'Family Name',
    enterVerificationCode: 'Please enter your verification code',
    enterPassword: 'Enter your password',
    confirmPassword: 'Enter your password again',
    selectCountry: 'Select Country',
    postalCode: 'Postal Code',
    // PIN相关
    welcomeBack: 'Welcome Back',
    enterPin: 'Enter your 4-digit PIN',
    pinRequired: 'Please enter 4-digit PIN',
    pinLoginFailed: 'Invalid PIN. Please try again.',
    forgotPin: 'Forgot PIN?',
    switchAccount: 'Switch Account',
    biometricLogin: 'Use biometric',
    pinLogin: 'Quick Login',
    enableQuickLogin: 'Enable quick login with PIN for faster access',
    forgotPasswordDesc: 'Enter your email or mobile phone and we\'ll send you a link or a code to reset your password.',
    enterEmailOrPhone: 'Please enter your email or mobile number',
    resetByLink: 'Reset by a link',
    resetByCode: 'Reset by a code',
    resetLinkSent: 'Reset link sent',
    resetCodeSent: 'Reset code sent',
    verificationCode: 'Verification Code',
    verifyAccount: 'Verify Your Account',
    verifyAccountDesc: 'Enter the verification code we sent to your phone number.',
    didntReceiveCode: 'Didn\'t receive code?',
    resendCode: 'Resend Code',
    verify: 'Verify',
    setNewPassword: 'Please set your new password',
    newPassword: 'New Password',
    enterNewPassword: 'Enter new password',
    confirmNewPassword: 'Confirm new password',
    clickAvatarToModify: 'Click on the avatar to modify',
    selectPhoto: 'Select Photo',
    takePhoto: 'Take a photo',
    chooseFromAlbum: 'Choose from album',
    editAccountName: 'Edit account name',
    enterNewAccountName: 'Please enter a new account name',
    editPhoneNumber: 'Edit phone number',
    enterNewPhoneNumber: 'Please enter a new phone number',
    loginFailed: 'Login failed, please try again later'
  },

  // 充电站相关
  station: {
    title: 'Charging Stations',
    name: 'Station Name',
    address: 'Address',
    distance: 'Distance',
    available: 'Available',
    busy: 'Busy',
    offline: 'Offline',
    stop: 'Stop',
    maintenance: 'Maintenance',
    price: 'Price',
    power: 'Power',
    connector: 'Connector',
    status: 'Status',
    details: 'Details',
    navigate: 'Navigate',
    favorite: 'Favorite',
    unfavorite: 'Unfavorite',
    nearbyStations: 'Nearby Stations',
    searchStations: 'Search charging stations',
    noStationsFound: 'No stations found',
    freeParking: 'Free1 hour',
    noImageUploaded: 'No image uploaded',
    imageNotAvailable: 'Image not available',
    noChargingPiles: 'No charging piles available',
    // 充电桩状态标签
    all: 'All',
    idleStatus: 'Idle',
    fastCharging: 'Fast Charging',
    occupiedStatus: 'Occupied',
    // 充电桩列表
    chargingPileList: 'Charging Pile List',
    // 充电桩信息标签
    code: 'Code',
    power: 'Power'
  },
  customerService: {
    title: 'Customer Service',
    callPhone: 'Call customer service',
    onlineService: 'Online service (not available)',
    feedback: 'Feedback',
    callFailed: 'Call failed',
    onlineNotAvailable: 'Online service is not yet available',
    feedbackInDevelopment: 'Feedback function is under development',
    cancel: 'Cancel'
  },

  // 充电相关
  charging: {
    title: 'Charging',
    start: 'Start Charging',
    stop: 'Stop Charging',
    status: 'Charging Status',
    time: 'Charging Time',
    energy: 'Energy',
    cost: 'Cost',
    current: 'Current',
    voltage: 'Voltage',
    power: 'Power',
    progress: 'Progress',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    history: 'History',
    duration: 'Duration',
    amount: 'Amount',
    startTime: 'Start Time',
    endTime: 'End Time',
    chargingInProgress: 'Charging in progress',
    chargingCompleted: 'Charging completed',
    chargingFailed: 'Charging failed',
    gunCode: 'Charging Gun Code',
    updatedTime: 'Updated two minutes ago',
    currentPeriod: 'Current period',
    nextPeriod: 'Next time slot',
    chargingTimeText1: 'You have been charging for',
    chargingTimeText2: 'and it is expected to take another',
    stopChargingConfirm: 'Are you sure you want to stop charging?',
    cancelChargingConfirm: 'Are you sure you want to cancel the charging process?',
    hour: 'hour',
    minutes: 'minutes',
    and: 'and',
    billingRules: 'Billing Rules',
    estimatedCost: 'Estimated Cost',
    endCharging: 'End Charging',
    endChargingConfirm: 'Are you sure you want to end charging?',
    chargingEnded: 'Charging ended',
    chargingGun: 'Charging Gun',
    chargingVehicle: 'Charging Vehicle',
    billingRulesContent: 'Charging rate: 27.2 FCFA/kWh\nService fee: 0 FCFA\nParking fee: 0 FCFA/hour',
    starting: 'Starting charging...',
    stopping: 'Stopping charging...',
    startSuccess: 'Charging started successfully',
    startFailed: 'Failed to start charging',
    alreadyChargingConfirm: 'This charging pile is already charging, do you want to view the charging status?',
    unavailable: 'Charging pile unavailable',
    unavailableMessage: 'This charging pile is currently unavailable, please choose another charging pile',
    endCharging: 'End Charging',
    endChargingConfirm: 'Are you sure you want to end charging?',
    chargingEnded: 'Charging Ended'
  },

  // 账户相关
  account: {
    title: 'My Account',
    balance: 'Balance',
    recharge: 'Recharge Account',
    transactions: 'Transactions',
    noTransactions: 'No transactions found',
    vehicles: 'Vehicles',
    myCar: 'My car',
    addVehicle: 'Add Vehicle',
    vehicleModel: 'Vehicle Model',
    licensePlate: 'License Plate',
    batteryCapacity: 'Battery Capacity',
    chargingHistory: 'Charging History',
    paymentMethods: 'Payment Methods',
    addPaymentMethod: 'Add Payment Method',
    creditCard: 'Credit Card',
    paypal: 'PayPal',
    applePay: 'Apple Pay',
    googlePay: 'Google Pay',
    membership: 'Subscribe to the membership',
    addMoney: 'Add money',
    membershipSubtitle: 'and enjoy great gifts.',
    membershipPrice: 'Membership price',
    history: 'HISTORY',
    activateMembership: 'Activate Membership',
    membershipBenefits: 'There are more membership benefits to claim',
    membershipPackage: 'Membership package price',
    recommended: 'Recommended',
    annualCard: 'Annual card',
    quarterCard: 'Quarter card',
    drivingRange: 'Driving range of',
    addVehicleInfo: 'Add Vehicle Information',
    certifiedVehicle: 'Certified Vehicle',
    scanLicense: 'Scan the vehicle license for quick certification',
    vehicleType: 'Vehicle Type',
    smallVehicle: 'Small Vehicle',
    mediumVehicle: 'Medium Vehicle',
    largeVehicle: 'Large Vehicle',
    pleaseCompleteFields: 'Please complete all required fields',
    vehicleAdded: 'Vehicle added successfully!',
    vehicleUpdated: 'Vehicle updated successfully!',
    updating: 'Updating...',
    submitting: 'Submitting...',
    deleteVehicle: 'Delete Vehicle',
    deleteVehicleConfirm: 'Are you sure you want to delete this vehicle?',
    vehicleDeleted: 'Vehicle deleted successfully!',
    insufficientBalance: 'Insufficient balance',
    insufficientBalanceMessage: 'Account balance is insufficient, do you want to go to recharge?'
  },

  // 设置相关
  settings: {
    title: 'Settings',
    language: 'Language',
    notifications: 'Notifications',
    privacy: 'Privacy',
    security: 'Security',
    theme: 'Theme',
    units: 'Units',
    autoStart: 'Auto Start',
    pushNotifications: 'Push Notifications',
    emailNotifications: 'Email Notifications',
    smsNotifications: 'SMS Notifications',
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    systemMode: 'System Mode',
    metric: 'Metric',
    imperial: 'Imperial',
    languageChanged: 'Language changed successfully',
    settingsSaved: 'Settings saved',
    accountSecurity: 'Account Security',
    bindMobilePhone: 'Bind Mobile Phone',
    loginPassword: 'Login Password',
    thirdPartyServices: 'Third-Party Services'
  },

  // 消息设置相关
  messageSettings: {
    title: 'Message Settings',
    messageNotification: 'Message Notification',
    notificationType: 'Notification Type',
    chargingOrderReminder: 'Charging Order Reminder',
    marketingActivities: 'Marketing Activities',
    systemNotification: 'System Notification'
  },

  // 优惠券相关
  coupon: {
    title: 'Coupon - claiming Center',
    unused: 'Unused',
    used: 'Used',
    expired: 'Expired',
    claim: 'Claim',
    use: 'Use'
  },

  filter: {
    chargingMethod: 'Charging method',
    fastCharging: 'Fast charging',
    slowCharging: 'Slow charging',
    parkingFee: 'Parking fee',
    free1Hour: 'Free 1 hour',
    freeHalfHour: 'Free for half an hour',
    freeUntilEnd: 'Free until the end'
  },

  // 支付相关
  payment: {
    title: 'Payment',
    amount: 'Amount',
    method: 'Method',
    card: 'Card',
    cash: 'Cash',
    wallet: 'Wallet',
    processing: 'Processing...',
    success: 'Payment successful',
    failed: 'Payment failed',
    cancelled: 'Payment cancelled',
    receipt: 'Receipt',
    refund: 'Refund',
    invoice: 'Invoice',
    goToPay: 'Go to pay',
    pay: 'Pay',
    pendingOrder: 'Pending Payment',
    orderNumber: 'Order Number',
    hasPendingOrder: 'You have unpaid orders',
    expenseSettlement: 'Expense Settlement',
    paymentSuccessful: 'Payment successful',
    choosePaymentMethod: 'Choose a payment method:',
    viewOrders: 'View orders',
    returnToHomepage: 'Return to homepage',
    enterPaymentPassword: 'Please enter the payment password',
    orderDetails: 'Order Details',
    account: 'Account',
    orderPendingPayment: 'Order is pending payment',
    insufficientBalance: 'The Account balance is insufficient. Please recharge before making the payment',
    collapseDetails: 'Collapse Details',
    serviceFee: 'Service Fee',
    memberOffers: 'Member-Exclusive Offers',
    totalPaid: 'Total Paid:',
    chargedAmount: 'Charged Amount:',
    chargingInformation: 'Charging information',
    orderNumber: 'Order Number',
    startTime: 'Start Time',
    endTime: 'End Time',
    chargingPower: 'Charging Power',
    expandDetails: 'Expand Details',

    // 费用结算页面
    chargingAmount: 'Charging Amount',
    electricityBill: 'Electricity Bill',
    serviceFee: 'Service Fee',
    coupon: 'Coupon',
    refundAmount: 'Refund Amount',
    totalPaid: 'Total Paid',
    payNow: 'Pay Now',
    admissionTime: 'Admission time',
    chargingDuration: 'Charging Duration',
    chargingKilowattHours: 'Charging Kilowatt-hours',
    orderNumber: 'Order Number',
    startTime: 'Start Time',
    endTime: 'End Time',
    chargingGun: 'Charging Gun',
    orderStatus: 'Order Status',

    // 订单详情页面
    orderDetails: 'Order Details',
    orderCompleted: 'Order Completed',
    thankYouForUsing: 'Thank you for using',
    chargingVehicle: 'Charging Vehicle',
    chargedAmount: 'Charged Amount',
    chargingInformation: 'Charging information',
    deviceType: 'Device Type',
    dcFastCharger: 'DC Fast Charger',
    acCharger: 'AC Charger',

    // 加载状态
    loadingOrderDetails: 'Loading order details...',
    getOrderDetailsFailed: 'Failed to get order details',
    
    // 支付密码重置页面
    resetWithCurrentPassword: 'Reset with Current Password',
    enterCurrentPaymentPassword: 'Enter your current payment password',
    currentPaymentPassword: 'Current Payment Password',
    enterCurrent4DigitPassword: 'Enter current 4-digit password',
    newPaymentPassword: 'New Payment Password',
    enterNew4DigitPassword: 'Enter new 4-digit password',
    passwordHint: 'Payment password is the last 4 digits of your phone number',
    confirmNewPassword: 'Confirm New Password',
    confirmNew4DigitPassword: 'Confirm new 4-digit password',
    resetWithVerificationCode: 'Reset with Verification Code',
    sendCodeToPhone: 'We\'ll send a code to your phone',
    sendVerificationCodeNote: 'We\'ll send verification code to this number',
    securityTips: 'Security Tips',
    securityTip1: '• Use 4 digits for your payment password',
    securityTip2: '• Avoid using simple sequences like 1234',
    securityTip3: '• Don\'t reuse passwords from other accounts',
    backToSelection: 'Back to Selection',
    
    // 支付密码重置选择页面
    resetPaymentPassword: 'Reset Payment Password',
    doYouRememberPaymentPassword: 'Do you remember your payment password?',
    paymentPasswordLastFourDigits: 'Payment password is the last 4 digits of your phone number',
    iRemember: 'I Remember',
    iDontRemember: 'I Don\'t Remember',
    account: 'Account'
  },

  // 错误信息
  error: {
    networkError: 'Network error',
    serverError: 'Server error',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    notFound: 'Not found',
    timeout: 'Request timeout',
    unknown: 'Unknown error',
    tryAgain: 'Please try again',
    contactSupport: 'Contact support',
    deleteFailed: 'Failed to delete'
  },

  // 网络状态
  network: {
    disconnected: 'Network disconnected, please check your connection',
    reconnecting: 'Trying to reconnect...',
    reconnected: 'Network reconnected',
    checkConnection: 'Please check your network settings',
    refresh: 'Refresh',
    weakSignal: 'Weak network signal',
    wifiConnected: 'WiFi connected',
    '5gConnected': '5G connected',
    '4gConnected': '4G connected',
    '3gConnected': '3G connected',
    '2gConnected': '2G connected',
    noNetwork: 'No network available',
    unknownNetwork: 'Unknown network type',
    checkingNetwork: 'Checking network...',
    networkRestored: 'Network restored',
    stillDisconnected: 'Still disconnected'
  },

  // 成功信息
  success: {
    operationCompleted: 'Operation completed successfully',
    dataSaved: 'Data saved',
    dataUpdated: 'Data updated',
    dataDeleted: 'Data deleted',
    updateSuccess: 'Update successful'
  },

  // Toast消息
  toast: {
    // 通用消息
    copied: 'Copied',
    saved: 'Saved',
    deleted: 'Deleted',
    updated: 'Updated',
    cancelled: 'Cancelled',
    completed: 'Completed',

    // 登录相关
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed',
    logoutSuccess: 'Logout successful',
    codeSent: 'Code sent',
    codeSendFailed: 'Failed to send code',

    // 注册相关
    registerSuccess: 'Registration successful',
    agreeTermsRequired: 'Please agree to the terms and conditions',

    // 充电相关
    chargingStopped: 'Charging stopped',
    chargingCancelled: 'Charging cancelled',
    chargingCompleted: 'Charging completed',
    scanningCode: 'Please scan the charging pile QR code',
    scanSuccess: 'Scan successful',
    scanFailed: 'Scan failed',
    noScanContent: 'No scan content obtained',
    processing: 'Processing...',
    processingFailed: 'Failed to process scan result',
    invalidQRCode: 'Unable to extract valid charging pile ID from scan result',
    missingChargingPileId: 'Missing charging pile ID',
    unrecognizedQRFormat: 'Unrecognized QR code format',
    orderNumberNotFound: 'Order number not found',
    stopChargingFailed: 'Failed to stop charging',
    orderCancelled: 'Order cancelled',
    cancelOrderFailed: 'Failed to cancel order',
    chargingStoppedSuccess: 'Charging stopped successfully',
    stopChargingFailed: 'Failed to stop charging',
    refreshFailed: 'Refresh failed',
    orderNumberNotFound: 'Order number not found',

    // 支付相关
    paymentSuccess: 'Payment successful',
    paymentFailed: 'Payment failed',

    // 网络相关
    networkError: 'Network error, please check your connection',
    requestFailed: 'Network request failed',
    serverError: 'Server error, please try again later',

    // 操作相关
    processing: 'Processing...',
    loading: 'Loading...',
    sending: 'Sending...',
    recognizing: 'Recognizing...',

    // 客服相关
    customerService: 'Customer Service'
  },

  // 应用相关
  app: {
    currentVersion: 'Current version'
  },

  // 法律相关
  legal: {
    privacyPolicy: 'Privacy Policy',
    introduction: 'Introduction',
    introductionText: 'Welcome to ARNIO. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application and services. Please read this privacy policy carefully. If you do not agree with the terms of this privacy policy, please do not access the application.',
    termsOfUse: 'Terms of Use',
    acceptanceOfTerms: 'Acceptance of Terms',
    acceptanceOfTermsText: 'By accessing and using the ARNIO application, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this application.',
    tradingRules: 'Explanation of Trading Rules'
  },

  // 版本更新相关
  update: {
    title: 'Update Available',
    newVersion: 'New Version',
    description: 'Update Description',
    updateNow: 'Update Now',
    updating: 'Updating...',
    latestVersion: 'You have the latest version',
    checkFailed: 'Version check failed',
    installSuccess: 'Installation successful',
    installFailed: 'Installation failed',
    downloadFailed: 'Download failed',
    manualUpdate: 'Please update the app manually',
    checkVersion: 'Check Version'
  },

  // 导航
  navigation: {
    title: 'Navigate to Station',
    description: 'Open Google Maps to navigate to this charging station?',
    distance: 'Distance',
    navigate: 'Navigate',
    cancel: 'Cancel'
  }
}
