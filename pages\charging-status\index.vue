<template>
    <view class="charging-status-container">
        <!-- 固定头部区域 -->
        <view class="fixed-header">
            <!-- 状态栏占位 -->
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <!-- 导航栏 -->
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ safeT('charging.chargingPile', 'Borne de recharge') }}</text>
                </view>
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-scroll">
            <!-- Charging Gun Code + Connection Status -->
            <view class="charging-gun-code">
                <view class="gun-line">
                    <image src="/static/images/charging-pile.png" mode="aspectFit"></image>
                    <text>{{ safeT('charging.gunCode', 'Charging Gun Code') }}: {{ gunCode }}</text>
                </view>
                <view class="connection-status" :class="connectionStatusClass">
                    <text class="status-dot"></text>
                    <text class="status-text">{{ connectionStatusText }}</text>
                </view>
            </view>



            <!-- 调试信息显示（开发时可见） -->
            <!-- <view class="debug-info" v-if="connectionStatus.connected" style="margin: 20rpx; padding: 20rpx; background: rgba(0,0,0,0.1); border-radius: 10rpx; font-size: 24rpx;">
            <text style="color: #666;">调试信息:</text>
            <text style="color: #333;">订单号: {{ orderNumber }}</text>
            <text style="color: #333;">充电状态: {{ chargingStatus }}</text>
            <text style="color: #333;">连接状态: {{ connectionStatus.type }}</text>
        </view> -->
            <view class="car_info_area">
                <!-- 充电百分比显示 -->
                <view class="percentage-display">
                    <text class="percentage-number">{{ chargingPercentage }}</text>
                    <text class="percentage-symbol">%</text>
                </view>
                <view class="update-time">
                    <text v-if="chargingStatus === 'charging'">
                        {{ safeT('charging.updatedTime', 'Updated two minutes ago') }}
                    </text>
                    <text v-else-if="chargingStatus === 'completed'" class="completed-text">
                        {{ safeT('charging.chargingCompleted', '') }}
                    </text>
                    <text v-else-if="chargingStatus === 'paused'" class="paused-text">
                        {{ safeT('charging.chargingPaused', '') }}
                    </text>
                    <text v-else-if="chargingStatus === 'error'" class="error-text">
                        {{ safeT('charging.chargingError', '') }}
                    </text>
                </view>
                <view class="car_info_area_top">
                    <view class="charging_progress_num"> <span class="unit"></span></view>
                    <!--                <view class="charging_progress_num">{{ chargingProgress }} <span class="unit">%</span></view>-->
                    <view class="update_tips"></view>
                </view>
                <view id="charging_box" class="car_charging_progress">
                    <view id="water" class="water" :style="`height:${chargingPercentage}%;`">
                        <image class="water_wave water_wave_back" src="../../static/images/wave_back.svg" />
                        <image class="water_wave water_wave_front" src="../../static/images/wave_front.svg" />
                    </view>
                    <view class="car_plate"></view>
                </view>
            </view>
            <!-- 车牌号 -->
            <view class="car-plate">
                <text>{{ carId }}</text>
            </view>
            <view style="padding: 0rpx 30rpx;">
                <!-- 充电信息卡片 -->
                <view class="info-card">
                    <view class="charging-time">
                        <text class="time-text">
                            {{ safeT('charging.chargingTimeText1', 'You have been charging for') }}
                            <text class="highlight">{{ totalChargingMinutes }}</text>
                            {{ safeT('charging.minutes', 'minutes') }}.
                        </text>
                    </view>

                    <view class="charging-stats">
                        <view class="stat-item">
                            <text class="stat-value">{{ chargingStats.current }}</text>
                            <text class="stat-unit">A</text>
                            <text class="stat-label">{{ safeT('charging.current', 'Real-time Current') }}</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-value">{{ chargingStats.voltage }}</text>
                            <text class="stat-unit">V</text>
                            <text class="stat-label">{{ safeT('charging.voltage', 'Real-time Voltage') }}</text>
                        </view>
                        <view class="stat-item">
                            <text class="stat-value">{{ chargingStats.power }}</text>
                            <text class="stat-unit">kw</text>
                            <text class="stat-label">{{ safeT('charging.power', 'Real-time Power') }}</text>
                        </view>
                    </view>
                </view>

                <!-- 充电费用部分 -->
                <view class="fee-section">
                    <view class="section-header">
                        <text class="section-title">{{ safeT('charging.cost', 'Charging Fee') }}</text>
                        <text class="billing-rules" @click="showBillingRules">{{ safeT('charging.billingRules',
                            'BillingRules') }}</text>
                    </view>

                    <view class="fee-card">
                        <view class="fee-item">
                            <view class="fee-value-container">
                                <text class="fee-value">{{ chargingFee.kWh }}</text>
                                <text class="fee-unit">kWh</text>
                            </view>
                            <text class="fee-label">{{ safeT('charging.kilowattHours', 'Kilowatt-hours Charged')
                                }}</text>
                        </view>
                        <view class="fee-item">
                            <view class="fee-value-container">
                                <text class="fee-value">{{ chargingFee.cost }}</text>
                                <text class="fee-unit">F</text>
                            </view>
                            <text class="fee-label">{{ safeT('charging.estimatedCost', 'Estimated Cost') }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部操作栏 -->
        <!-- <view class="footer-bar"> -->
            <!-- <view class="cs-btn" @click="showCustomerService">
                <image src="/static/images/customer-service-2.png" class="cs-icon" />
            </view> -->
            <!-- 停止充电按钮 - 始终显示，不依赖数据状态 -->
            <!-- <view class="end-charging-btn" @click="endCharging">
                <image src="/static/images/end.png" class="end-icon" />
                <text>{{ safeT('charging.pressToEnd', 'Press and hold to end charging') }}</text>
            </view> -->
            <!-- <view v-else-if="chargingStatus === 'completed'" class="completed-btn" @click="goToSettlement">
                        <image src="/static/images/check.png" class="end-icon" />
                        <text>{{ $t('charging.viewSettlement') || '查看结算' }}</text>
                    </view>
                    <view v-else class="disabled-btn">
                        <image src="/static/images/end.png" class="end-icon" />
                        <text>{{ $t('charging.chargingUnavailable') || '充电不可用' }}</text>
                    </view> -->
        <!-- </view> -->

        <!-- 结束充电确认弹窗 -->
        <CustomDialog
        v-if="showEndChargingDialog"
            :visible="showEndChargingDialog"
            type="warning"
            title="Arrêter la charge"
            message="Êtes-vous sûr de vouloir arrêter la charge en cours ?"
            confirmText="Confirmer"
            cancelText="Annuler"
            @confirm="handleEndChargingConfirm"
            @cancel="handleEndChargingCancel"
        />

    </view>
    <!-- 充电结算Loading弹窗 -->
    <ChargingSettlementModal 
        :visible="showSettlementLoading"
        :message="settlementMessage"
    />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, inject } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useGlobalHud } from '@/composables/useHud'
const hud = useGlobalHud()

import { useI18n } from 'vue-i18n'

import chargingStatusManager from '@/utils/chargingStatusManager.js'
import { stopCharging, cancelOrder } from '@/api/modules/charging.ts'
import GlobalHUD from '@/components/common/GlobalHUD.vue'
import CustomDialog from '@/components/common/CustomDialog.vue'
import ChargingSettlementModal from '@/components/common/ChargingSettlementModal.vue'

import env from '@/config/env.ts'
import { useUserStore } from '@/store/user.js'

// 使用国际化
const { t } = useI18n()

// 安全的国际化函数，防止 t 未定义的错误
const safeT = (key, fallback = '') => {
    try {
        return t && typeof t === 'function' ? t(key) : fallback
    } catch (error) {
        console.warn('Translation function error:', error)
        return fallback
    }
}

// 使用用户store
const userStore = useUserStore()

// 注入Toast实例
const toast = inject('toast')

// 响应式数据
const statusBarHeight = ref(0)
const chargingPercentage = ref(0)
const chargingTimeElapsed = ref({
    hours: '--',
    minutes: '--'
})

// 计算总充电分钟数
const totalChargingMinutes = computed(() => {
    const hours = parseInt(chargingTimeElapsed.value.hours) || 0
    const minutes = parseInt(chargingTimeElapsed.value.minutes) || 0
    const total = hours * 60 + minutes
    return total > 0 ? total : '--'
})

// 移除 chargingTimeRemaining，不再显示预计剩余时间
const chargingStats = ref({
    current: '--',
    voltage: '--',
    power: '--'
})
const chargingFee = ref({
    kWh: '--',
    cost: '--'
})
const carId = ref('LDP4A9609035269')
const stationName = ref('Arnio charging 01 PIL')
const gunCode = ref('34541112241010')

// 缓存相关状态（保留用于内部逻辑，不显示UI提示）
const isLoadingFromCache = ref(false)
const cacheDataTimestamp = ref(null)

// 结束充电弹窗状态
const showEndChargingDialog = ref(false)

// 充电结算loading弹窗状态
const showSettlementLoading = ref(false)
const settlementMessage = ref('Calcul de la facture en cours, veuillez patienter...')

// WebSocket相关状态
const orderId = ref('')
const orderNumber = ref('') // 订单号，从路由参数获取
const chargingStatus = ref('charging') // charging, completed, error, paused
const connectionStatus = ref({
    connected: false,
    type: 'none' // websocket, polling, none, failed
})
// 标记是否为连接失败/断开，用于控制弹窗状态
const connectionFailed = ref(false)
const lastUpdateTime = ref(null)

// 设备连接弹窗状态
const showConnectingModal = ref(false)
const connectingModalRef = ref(null)

// 连接状态计算属性
const connectionStatusClass = computed(() => {
    const s = connectionStatus.value
    if (s.connected && s.type === 'websocket') return 'status-connected'
    if (s.type === 'reconnecting') return 'status-reconnecting'
    if (s.type === 'connecting') return 'status-connecting'
    if (s.type === 'disconnected' || !s.connected) return 'status-disconnected'
    return 'status-disconnected'
})
const connectionStatusText = computed(() => {
    const s = connectionStatus.value
    if (s.connected && s.type === 'websocket') return safeT('charging.connected', 'Connected')
    if (s.type === 'reconnecting') return safeT('charging.reconnecting', 'Reconnecting...')
    if (s.type === 'connecting') return safeT('charging.connecting', 'Connecting...')
    if (s.type === 'disconnected' || !s.connected) return safeT('charging.disconnected', 'Disconnected')
    return safeT('charging.disconnected', 'Disconnected')
})


// 使用 onLoad 生命周期钩子接收页面参数（uni-app 标准方式）
onLoad((options) => {
    console.log('=== onLoad 接收页面参数 ===')
    console.log('接收到的参数:', options)

    // 处理订单号参数
    if (options && options.orderNumber) {
        orderNumber.value = decodeURIComponent(options.orderNumber)
        console.log('✅ 从onLoad获取订单号:', orderNumber.value)
    } else {
        console.log('❌ onLoad中没有orderNumber参数')
    }

    // 处理订单ID参数
    if (options && options.orderId) {
        orderId.value = decodeURIComponent(options.orderId)
        console.log('✅ 从onLoad获取订单ID:', orderId.value)
    }

    // 处理充电桩名称参数
    if (options && options.pileName) {
        stationName.value = decodeURIComponent(options.pileName)
        console.log('✅ 从onLoad获取充电桩名称:', stationName.value)
    }

    // 处理充电枪编码参数
    if (options && options.gunCode) {
        gunCode.value = decodeURIComponent(options.gunCode)
        console.log('✅ 从onLoad获取充电枪编码:', gunCode.value)
    }

    // 检查是否有缓存数据（内部逻辑，不显示提示）
    const currentOrderId = orderId.value || orderNumber.value
    if (currentOrderId) {
        const hasCachedData = chargingStatusManager.hasCachedData(currentOrderId)
        if (hasCachedData) {
            isLoadingFromCache.value = true
        }
    }

    console.log('=== onLoad 参数处理完成 ===')
    console.log('最终订单号:', orderNumber.value)
    console.log('最终订单ID:', orderId.value)
})

// 生命周期钩子
onMounted(() => {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight

    // 设置CSS变量
    try {
        document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`)
    } catch (e) {
        console.log('设置CSS变量失败', e)
    }

    // 检查参数是否已通过 onLoad 获取到
    console.log('=== onMounted 检查参数状态 ===')
    console.log('当前订单号:', orderNumber.value)
    console.log('当前订单ID:', orderId.value)

    // 如果 onLoad 没有获取到订单号，尝试从本地存储获取作为备用方案
    if (!orderNumber.value) {
        console.log('onLoad未获取到订单号，尝试从本地存储获取...')
        try {
            const storedOrder = uni.getStorageSync('currentChargingOrder')
            if (storedOrder) {
                const orderInfo = JSON.parse(storedOrder)
                if (orderInfo.orderNumber) {
                    orderNumber.value = orderInfo.orderNumber
                    console.log('✅ 从本地存储获取订单号:', orderNumber.value)
                } else if (orderInfo.orderNum) {
                    orderNumber.value = orderInfo.orderNum
                    console.log('✅ 从本地存储获取订单号(orderNum字段):', orderNumber.value)
                }
            } else {
                console.log('❌ 本地存储中也没有订单信息')
            }
        } catch (error) {
            console.error('从本地存储获取订单信息失败:', error)
        }
    }

    console.log('=== 最终参数状态 ===')
    console.log('订单号:', orderNumber.value)
    console.log('订单ID:', orderId.value)
    console.log('充电桩名称:', stationName.value)
    console.log('充电枪编码:', gunCode.value)

    // 参数验证
    if (!orderNumber.value && !orderId.value) {
        console.error('Missing order info: orderNumber and orderId are empty')
        // Do not show modal/toast; just log and keep the page
        return
    }

    // 设置充电状态监控
    setupChargingStatusMonitoring()

    // 不再显示连接弹窗，直接显示连接状态
    showConnectingModal.value = false
    connectionFailed.value = false

    // 开始监控（包含连接逻辑）
    startChargingMonitoring()
})

// 页面卸载时清理资源
onUnmounted(() => {
    console.log('页面卸载，暂停监控但保持连接')
    // 只暂停监控，保持WebSocket连接和缓存
    stopChargingMonitoring(false)
})

// WebSocket事件处理
const setupChargingStatusMonitoring = () => {
    console.log('设置充电状态监控')

    // 监听缓存数据加载
    chargingStatusManager.on('cacheDataLoaded', (data) => {
        console.log('📦 缓存数据已加载:', data)
        isLoadingFromCache.value = false
        cacheDataTimestamp.value = data.timestamp
        // 移除缓存数据提示，只保留控制台日志
    })

    // 监听状态更新
    chargingStatusManager.on('statusUpdate', (data) => {
        console.log('收到充电状态更新:', data)
        updateChargingData(data)

        // 如果是实时数据，清除缓存标记
        if (!isLoadingFromCache.value) {
            cacheDataTimestamp.value = null
        }
    })

    // 监听连接状态变化（用于顶部状态条，不再使用弹窗）
    chargingStatusManager.on('connectionStatusChanged', (status) => {
        console.log('connection status changed:', status)
        connectionStatus.value = status
    })

    // 监听连接错误（仅日志，不弹窗/提示）
    chargingStatusManager.on('connectionError', (error) => {
        console.error('connection error:', error)
        connectionFailed.value = true
        // 不再显示弹窗，只更新连接状态
    })

    // 监听设备连接中事件（仅记录日志）
    chargingStatusManager.on('deviceConnecting', (data) => {
        console.log('设备连接中:', data)
        // 不再显示弹窗，只记录日志
    })

    // 监听订阅确认事件（记录日志，不做提示）
    chargingStatusManager.on('subscribeConfirmed', (data) => {
        console.log('subscribe confirmed:', data)
    })

    // 监听充电开始事件
    chargingStatusManager.on('chargingStarted', () => {
        console.log('充电已开始')
        // 不再需要隐藏连接弹窗，因为已经移除了弹窗
    })

    // 🆕 监听充电结束事件
    chargingStatusManager.on('chargingEnded', (data) => {
        console.log('充电已结束，直接跳转home页面:', data)
        handleChargingEndedInChargingPage(data)
    })
}

// 更新充电数据
const updateChargingData = (data) => {
    if (!data) return

    console.log('更新页面充电数据:', data)

    // 更新电池状态
    if (data.battery) {
        chargingPercentage.value = data.battery.current || chargingPercentage.value
        console.log('更新电池电量:', chargingPercentage.value + '%')
    }

    // 更新功率信息
    if (data.power) {
        chargingStats.value = {
            current: data.power.current ? Math.round(data.power.current * 10) / 10 : '--',
            voltage: data.power.voltage ? Math.round(data.power.voltage * 10) / 10 : '--',
            power: data.power.power ? Math.round(data.power.power * 10) / 10 : '--'
        }
        console.log('更新功率信息:', chargingStats.value)
    }

    // 更新时间信息（使用转换后的time.elapsed字段）
    const timeSource = data.time?.elapsed || data.alreadyFillTime
    if (timeSource !== undefined && timeSource !== null) {
        const totalMinutes = parseInt(timeSource)
        const hours = Math.floor(totalMinutes / 60)
        const minutes = totalMinutes % 60

        chargingTimeElapsed.value = {
            hours: hours || '--',
            minutes: minutes || '--'
        }

        console.log('更新时间信息 - 已充电:', chargingTimeElapsed.value, '总分钟数:', totalMinutes, '数据源:', timeSource)
    }

    // 更新费用信息
    if (data.cost) {
        chargingFee.value = {
            kWh: data.battery?.filled ? Math.round(data.battery.filled * 100) / 100 : '--',
            cost: data.cost.estimated ? data.cost.estimated : '--'
        }
        console.log('更新费用信息:', chargingFee.value)
    }

    // 更新订单信息
    if (data.order) {
        if (data.order.orderNum && !orderNumber.value) {
            orderNumber.value = data.order.orderNum
        }
        if (data.order.spearNum) {
            gunCode.value = data.order.spearNum
        }
        console.log('更新订单信息 - 订单号:', orderNumber.value, '充电枪:', gunCode.value)
    }

    // 更新充电状态
    if (data.status) {
        const oldStatus = chargingStatus.value
        chargingStatus.value = data.status
        console.log('充电状态变化:', oldStatus, '->', chargingStatus.value)


    }

    // 更新最后更新时间
    lastUpdateTime.value = new Date().toLocaleTimeString()
    console.log('数据更新完成，最后更新时间:', lastUpdateTime.value)
}

// 开始监控充电状态
const startChargingMonitoring = () => {
    console.log('开始监控充电状态, 订单ID:', orderId.value)

    // 如果没有订单ID，尝试使用订单号作为订单ID
    if (!orderId.value && orderNumber.value) {
        orderId.value = orderNumber.value
        console.log('使用订单号作为订单ID:', orderId.value)
    }

    // 检查必要参数
    if (!orderId.value && !orderNumber.value) {
        console.error('Missing order info: orderId and orderNumber are empty')
        // no toast/modal, just log
        return
    }

    // 获取token（优先本地，其次store）
    let token = uni.getStorageSync('token')
    if (!token && userStore && userStore.accessToken) {
        token = userStore.accessToken
    }

    // 获取clientId，从用户信息或store中获取（兼容 client_id / clientId）
    let clientId = ''

    // 尝试从用户信息中获取
    const userInfo = uni.getStorageSync('userInfo')
    if (userInfo) {
        try {
            const userInfoObj = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
            const fromStorage = userInfoObj && (userInfoObj.client_id || userInfoObj.clientId)
            if (fromStorage) {
                clientId = userInfoObj.client_id || userInfoObj.clientId
                console.log('从用户信息中获取clientId:', clientId)
            }
        } catch (e) {
            console.error('解析用户信息失败:', e)
        }
    }

    // 如果用户信息中没有，尝试从store中获取
    if (!clientId && userStore && userStore.userInfo && (userStore.userInfo.client_id || userStore.userInfo.clientId)) {
        clientId = userStore.userInfo.client_id || userStore.userInfo.clientId
        console.log('从store中获取clientId:', clientId)
    }

    // 如果还是没有clientId，显示错误
    if (!clientId) {
        console.error('无法获取clientId，WebSocket连接可能失败')
        uni.showToast({
            title: safeT('user.loginFailed', 'Informations utilisateur anormales, veuillez vous reconnecter'),
            icon: 'error',
            duration: 2000
        })
        return
    }

    // 构建WebSocket URL，包含Authorization和clientid参数（必须URL编码，避免APP端空格导致连接失败）
    const authParam = encodeURIComponent(`Bearer ${token || ''}`)
    const wsUrl = `${env.WS_URL}?Authorization=${authParam}&clientid=${encodeURIComponent(clientId)}`

    console.log('=== WebSocket连接调试信息 ===')
    console.log('环境配置 env.WS_URL:', env.WS_URL)
    console.log('WebSocket连接URL:', wsUrl)
    console.log('使用的token:', token)
    console.log('使用的clientId:', clientId)
    console.log('订单ID:', orderId.value)
    console.log('订单号:', orderNumber.value)

    // 检测运行环境
    // #ifdef H5
    console.log('当前运行环境: H5')
    // #endif
    // #ifdef APP-PLUS
    console.log('当前运行环境: APP')
    // #endif

    console.log('🚀 启动充电状态监控...')
    chargingStatusManager.startMonitoring(orderId.value, {
        websocketUrl: wsUrl,
        token: token,
        orderNumber: orderNumber.value // 传递订单号
    })

    console.log('✅ 监控启动完成，等待WebSocket连接和数据...')
}

// 停止监控充电状态
const stopChargingMonitoring = (clearCache = false) => {
    console.log('停止监控充电状态')
    chargingStatusManager.stopMonitoring(clearCache)
}

// 移除了手动刷新和格式化缓存时间的函数，简化页面逻辑

// 方法
const goBack = () => {
    uni.navigateBack()
}

// 跳转到结算页面
const goToSettlement = () => {
    uni.navigateTo({
        url: '/pages/payment/expense-settlement/index'
    })
}

const endCharging = () => {
    // 显示封装的确认弹窗
    showEndChargingDialog.value = true
}

// 处理确认结束充电
const handleEndChargingConfirm = async () => {
    // 关闭弹窗
    showEndChargingDialog.value = false

    // 执行原有的结束充电逻辑
    try {
        if (!orderNumber.value) {
            console.warn('orderNumber is empty, skip stopCharging')
            return
        }

        try {
            // HUD 加载
            hud.loading(safeT('charging.stopping', 'Arrêt de la charge...'))

            // 调用停止充电API
            console.log('调用停止充电API, 订单号:', orderNumber.value)
            const response = await stopCharging(orderNumber.value)

            hud.done()

            if (response && response.code === 200) {
                // API调用成功
                console.log('停止充电API调用成功:', response)

                // 显示充电结束提示
                hud.success(response.msg || safeT('toast.chargingStoppedSuccess', 'Charge arrêtée'))

                // 延迟跳转到结算页面，传递订单号参数
                setTimeout(() => {
                    let url = '/pages/payment/expense-settlement/index'
                    if (orderNumber.value) {
                        url += `?orderNumber=${encodeURIComponent(orderNumber.value)}`
                        console.log('跳转到结算页面，URL:', url)
                    }
                    uni.navigateTo({ url })
                }, 2000)
            } else {
                throw new Error(response?.msg || safeT('toast.stopChargingFailed', "Échec de l'arrêt de la charge"))
            }
        } catch (apiError) {
            console.error('停止充电API调用失败:', apiError)
            hud.done()
            hud.error(apiError.message || safeT('toast.stopChargingFailed', "Échec de l'arrêt de la charge"))
        }
    } catch (error) {
        // 用户取消操作，不做任何处理
        console.log('End charging cancelled')
    }
}

// 处理取消结束充电
const handleEndChargingCancel = () => {
    showEndChargingDialog.value = false
    console.log('用户取消结束充电')
}

// 🆕 处理在充电页面收到充电结束消息
const handleChargingEndedInChargingPage = (data) => {
    console.log('在充电页面收到充电结束消息，显示结算弹窗:', data)
    
    // 🚨 强制隐藏任何可能显示的HUD提示
    hud.hide()
    // 额外确保HUD完全隐藏
    setTimeout(() => hud.hide(), 100)
    setTimeout(() => hud.hide(), 500)
    
    // 1. 显示结算loading弹窗
    showSettlementLoading.value = true
    
    // 2. 3秒后更新弹窗消息为socket收到的消息
    setTimeout(() => {
        settlementMessage.value = data.message
    }, 3000)
    
    // 3. 再等2秒后跳转到首页
    setTimeout(() => {
        showSettlementLoading.value = false
        
        // 跳转到首页
        uni.switchTab({
            url: '/pages/home/<USER>',
            success: () => {
                console.log('跳转首页成功')
            },
            fail: (error) => {
                console.error('跳转首页失败:', error)
                // 如果switchTab失败，尝试navigateTo
                uni.navigateTo({
                    url: '/pages/home/<USER>'
                })
            }
        })
    }, 5000)
}

const showBillingRules = () => {
    return
    // 使用Toast显示计费规则信息
    toast.confirm({
        title: t('charging.billingRules'),
        message: t('charging.billingRulesContent'),
        confirmText: t('common.confirm'),
        onConfirm: () => {
            // 用户点击确认，关闭弹窗
        },
        onCancel: () => {
            // 用户点击取消，关闭弹窗
        }
    })
}

const showCustomerService = () => {
    handleStopChargingFromModal()
}

// 处理连接超时
const handleConnectionTimeout = () => {
    console.log('设备连接超时')
    // 这里可以添加超时后的处理逻辑，比如记录日志等
}

// 处理重试连接
const handleRetryConnection = () => {
    console.log('重试连接设备')
    // 重置为连接中状态（不是失败态）
    connectionFailed.value = false
    // 重新开始监控充电状态
    startChargingMonitoring()
}

// 处理取消订单
const handleCancelOrder = () => {
    console.log('确认取消订单')
    // 直接执行取消订单操作
    performCancelOrder()
}

// 执行取消订单操作
const performCancelOrder = async () => {
    try {
        console.log('开始取消订单，订单号:', orderNumber.value)

        if (!orderNumber.value) {
            uni.showToast({
                title: safeT('toast.orderNumberNotFound', 'Numéro de commande introuvable'),
                icon: 'error',
                duration: 2000
            })
            return
        }

        // 显示加载提示
        uni.showLoading({
            title: safeT('common.loading', 'Traitement...')
        })

        // 调用取消订单API
        const response = await cancelOrder(orderNumber.value)

        // 隐藏加载提示
        uni.hideLoading()

        if (response.code === 200) {
            // 取消成功
            console.log('订单取消成功')

            // 隐藏连接弹窗
            showConnectingModal.value = false

            // 显示成功提示
            uni.showToast({
                title: safeT('toast.orderCancelled', 'Commande annulée'),
                icon: 'success',
                duration: 2000
            })

            // 返回上一页或首页
            setTimeout(() => {
                uni.navigateBack({
                    fail: () => {
                        // 如果无法返回，则跳转到首页
                        uni.switchTab({
                            url: '/pages/home/<USER>'
                        })
                    }
                })
            }, 2000)
        } else {
            // 取消失败
            console.error('订单取消失败:', response.message)
            uni.showToast({
                title: response.message || safeT('toast.cancelOrderFailed', 'Échec de l\'annulation de la commande'),
                icon: 'error',
                duration: 2000
            })
        }
    } catch (error) {
        // 隐藏加载提示
        uni.hideLoading()

        console.error('取消订单异常:', error)
        uni.showToast({
            title: safeT('toast.cancelOrderFailed', 'Échec de l\'annulation de la commande'),
            icon: 'error',
            duration: 2000
        })
    }
}

// 处理从弹窗停止充电
const handleStopChargingFromModal = async () => {
    try {
        console.log('从连接弹窗停止充电')

        // 隐藏连接弹窗
        showConnectingModal.value = false

        // 检查是否有订单号
        if (!orderNumber.value) {
            console.warn('没有订单号，无法停止充电')
            uni.showToast({
                title: safeT('toast.orderNumberNotFound', 'Numéro de commande introuvable'),
                icon: 'error',
                duration: 2000
            })
            return
        }

        // 显示加载提示
        hud.loading(safeT('charging.stopping', 'Arrêt de la charge...'))

        // 调用停止充电API
        const response = await stopCharging(orderNumber.value)

        // 结束HUD
        hud.done()

        if (response && response.code === 200) {
            // 停止充电成功
            console.log('停止充电成功')

            // 显示成功提示
            hud.success(safeT('toast.chargingStoppedSuccess', 'Charge arrêtée'))

            // 延迟跳转到订单支付详情页面
            setTimeout(() => {
                const url = `/pages/payment/expense-settlement/index?orderNumber=${encodeURIComponent(orderNumber.value)}`
                console.log('跳转到订单支付详情页面:', url)

                uni.navigateTo({
                    url,
                    success: () => {
                        console.log('跳转订单支付详情页面成功')
                    },
                    fail: (error) => {
                        console.error('跳转订单支付详情页面失败:', error)
                        // 如果跳转失败，回到首页
                        uni.switchTab({
                            url: '/pages/home/<USER>'
                        })
                    }
                })
            }, 2000)
        } else {
            // 停止充电失败
            console.error('停止充电失败:', response?.message)
            hud.error(response?.message || safeT('toast.stopChargingFailed', 'Échec de l\'arrêt de la charge'))
        }
    } catch (error) {
        // 隐藏加载提示
        uni.hideLoading()

        console.error('停止充电异常:', error)
        uni.showToast({
            title: safeT('toast.stopChargingFailed', 'Échec de l\'arrêt de la charge'),
            icon: 'error',
            duration: 2000
        })
    }
}


</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.charging-status-container {
    min-height: 100vh;
    background-color: #f8f9fa;
    display: flex;
    flex-direction: column;
    position: relative;
    padding-bottom: 120rpx;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12px rgba(0, 0, 0, 0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;

    .header-content {
        height: 88rpx;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;

        .back-btn {
            width: 88rpx;
            height: 88rpx;
            display: flex;
            align-items: center;

            .iconfont {
                font-size: 40rpx;
                color: #333;
            }

            &:active {
                opacity: 0.7;
            }
        }

        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
            pointer-events: none;
        }
    }
}

.content-scroll {
    position: relative;
    flex: 1;
    padding: 20rpx 0rpx;
    margin-top: calc(var(--status-bar-height, 0px) + 88rpx);

    .car_info_area {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 450rpx;
        background: url('https://pic.yuanmajiazu.com/chargingStation/car_charging_bg.png') no-repeat;
        background-size: cover;
        background-position: 0rpx 40rpx;
        position: relative;

        .car_charging_progress {
            width: 100%;
            height: 42%;
            position: absolute;
            background: transparent;
            mask-image: url('https://pic.yuanmajiazu.com/chargingStation/car_charging_progress.png');
            mask-repeat: no-repeat;
            mask-size: contain;
            left: 50%;
            transform: translate(calc(-33% - -5rpx), calc(-50% + 90rpx));

            .water {
                position: absolute;
                left: 0;
                bottom: 0;
                z-index: 2;
                width: 100%;
                height: 0%;
                background: #00ffad;
                transition: all 0.3s;

                .water_wave {
                    width: 200%;
                    height: 30rpx;
                    position: absolute;
                    bottom: 100%;
                }

                .water_wave_back {
                    right: 0;
                    animation: wave-back 3.5s infinite linear;
                }

                .water_wave_front {
                    left: 0;
                    margin-bottom: -12rpx;
                    //animation: wave-front 3.5s infinite linear;
                }

                @keyframes wave-front {
                    100% {
                        transform: translate(-50%, 0);
                    }
                }

                @keyframes wave-back {
                    100% {
                        transform: translate(50%, 0);
                    }
                }
            }

            .car_plate {
                color: #333333;
                font-size: 26rpx;
                font-weight: bold;
                letter-spacing: 2rpx;
                position: absolute;
                left: 50%;
                bottom: 0;
                z-index: 10;
                transform: translate(-50%, 100%);
            }
        }
    }
}

.charging-gun-code {
    display: flex;
    flex-direction: column;
    margin: 20rpx 0 40rpx 0;
    /* 增加底部间距 */
    padding: 0rpx 20rpx;
    gap: 16rpx;
    /* 增加行间距 */

    .gun-line {
        display: flex;
        align-items: center;
        gap: 10rpx;
    }

    image {
        width: 36rpx;
        height: 36rpx;
    }

    text {
        color: #4A5B72;
        font-size: 28rpx;
    }
}

/* 连接状态指示器 */
.connection-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 500;
    align-self: center;
    /* 水平居中 */
}

.status-dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    display: inline-block;
}

.status-connected {
    background-color: rgba(52, 199, 89, 0.1);
    color: #34c759;
}

.status-connected .status-dot {
    background-color: #34c759;
    animation: pulse 2s infinite;
}

.status-connecting {
    background-color: rgba(255, 149, 0, 0.1);
    color: #ff9500;
}

.status-connecting .status-dot {
    background-color: #ff9500;
    animation: blink 1s infinite;
}

.status-disconnected {
    background-color: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
}

.status-disconnected .status-dot {
    background-color: #ff3b30;
}

.status-reconnecting {
    background-color: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

.status-reconnecting .status-dot {
    background-color: #007aff;
    animation: blink 1.2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

@keyframes blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0.3;
    }
}

.percentage-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-top: 20rpx;

    .percentage-number {
        font-size: 120rpx;
        font-weight: bold;
        color: #333;
        line-height: 1;
    }

    .percentage-symbol {
        font-size: 60rpx;
        color: #333;
        margin-left: 8rpx;
    }
}

.update-time {
    text-align: center;
    margin-top: 8rpx;
    margin-bottom: 40rpx;

    text {
        font-size: 24rpx;
        color: #999;
    }

    .completed-text {
        color: #00C851;
        font-weight: 500;
    }

    .paused-text {
        color: #FF8800;
        font-weight: 500;
    }

    .error-text {
        color: #ff4a4a;
        font-weight: 500;
    }
}

/* 汽车充电可视化 */
.car-visualization {
    position: relative;
    width: 100%;
    height: 200rpx;
    margin-bottom: 20rpx;

    .car-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .battery-overlay {
        position: absolute;
        left: 16%;
        top: 38%;
        height: 24%;
        background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
        opacity: 0.7;
        border-radius: 8rpx;
        transition: width 0.3s;
        z-index: 2;
    }
}



.car-plate {
    text-align: center;
    margin: 20rpx 0;

    text {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
    }
}

/* 信息卡片 */
.info-card {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 32rpx;
    margin: 20rpx 0;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

}

.charging-time {
    padding-bottom: 24rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .time-text {
        font-size: 28rpx;
        line-height: 1.5;
        color: #333;
    }

    .highlight {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }

    .orange-highlight {
        font-size: 32rpx;
        font-weight: bold;
        color: #ff6b00;
    }
}

.charging-stats {
    display: flex;
    justify-content: space-between;
    padding-top: 24rpx;

    .stat-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        &:not(:last-child):after {
            content: '';
            position: absolute;
            right: 0;
            top: 10%;
            height: 80%;
            width: 1rpx;
            background-color: #f0f0f0;
        }

        .stat-value {
            font-size: 48rpx;
            font-weight: bold;
            color: #333;
            line-height: 1;
        }

        .stat-unit {
            font-size: 24rpx;
            color: #666;
            margin-left: 4rpx;
        }

        .stat-label {
            font-size: 24rpx;
            color: #999;
            margin-top: 12rpx;
        }
    }
}

/* 费用部分 */
.fee-section {
    margin: 20rpx 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }

    .billing-rules {
        font-size: 24rpx;
        color: #666;
    }
}

.fee-card {
    background-color: #f8f9fa;
    border-radius: 20rpx;
    padding: 32rpx;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);

    .fee-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        .fee-value-container {
            display: flex;
            align-items: baseline;
            margin-bottom: 8rpx;

            .fee-value {
                font-size: 48rpx;
                font-weight: bold;
                color: #333;
            }

            .fee-unit {
                font-size: 24rpx;
                color: #666;
                margin-left: 8rpx;
            }
        }

        .fee-label {
            font-size: 24rpx;
            color: #999;
        }
    }
}

/* 底部操作栏 */
.footer-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
    background: #fff;
    z-index: 100;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

    .cs-btn {
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
        background: #f5f7fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;

        .cs-icon {
            width: 44rpx;
            height: 44rpx;
        }
    }

    .end-charging-btn {
        flex: 1;
        height: 96rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
        border-radius: 48rpx;
        box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        &:active {
            transform: scale(0.96);
            box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.4);

            &::before {
                left: 100%;
            }
        }

        .end-icon {
            width: 40rpx;
            height: 40rpx;
            margin-right: 20rpx;
            filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.15));
        }

        text {
            font-size: 34rpx;
            color: #fff;
            font-weight: 600;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.15);
            letter-spacing: 1rpx;
        }
    }

    .completed-btn {
        flex: 1;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #00C851;
        border-radius: 44rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 200, 81, 0.2);

        .end-icon {
            width: 36rpx;
            height: 36rpx;
            margin-right: 16rpx;
        }

        text {
            font-size: 32rpx;
            color: #fff;
            font-weight: 500;
        }
    }

    .disabled-btn {
        flex: 1;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ccc;
        border-radius: 44rpx;
        opacity: 0.6;

        .end-icon {
            width: 36rpx;
            height: 36rpx;
            margin-right: 16rpx;
        }

        text {
            font-size: 32rpx;
            color: #fff;
            font-weight: 500;
        }
    }
}
</style>