/**
 * 实时性能监控器
 * 监控扫码功能的性能指标，帮助识别卡顿原因
 */

/**
 * 性能监控器类
 */
class PerformanceMonitor {
  constructor() {
    this.isMonitoring = false;
    this.metrics = {
      scannerClicks: 0,
      scannerResponseTimes: [],
      frameDrops: 0,
      memoryUsage: [],
      cpuUsage: [],
      renderTimes: []
    };
    this.observers = [];
    this.startTime = null;
  }

  /**
   * 开始监控
   */
  startMonitoring() {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.startTime = Date.now();
    
    console.log('🔍 开始性能监控');
    
    // 启动各种监控
    this.startFrameRateMonitoring();
    this.startMemoryMonitoring();
    this.startRenderTimeMonitoring();
    
    // 监控用户交互
    this.startInteractionMonitoring();
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    // 清理观察者
    this.observers.forEach(observer => {
      if (observer && observer.disconnect) {
        observer.disconnect();
      }
    });
    this.observers = [];
    
    console.log('⏹️ 性能监控已停止');
  }

  /**
   * 开始帧率监控
   */
  startFrameRateMonitoring() {
    let lastTime = performance.now();
    let frameCount = 0;
    let droppedFrames = 0;

    const checkFrame = (currentTime) => {
      if (!this.isMonitoring) return;

      frameCount++;
      const deltaTime = currentTime - lastTime;
      
      // 检测掉帧（超过16.67ms表示低于60fps）
      if (deltaTime > 16.67 * 2) { // 超过33ms认为是掉帧
        droppedFrames++;
        this.metrics.frameDrops = droppedFrames;
      }

      // 每秒统计一次
      if (deltaTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / deltaTime);
        this.recordMetric('fps', fps);
        
        if (fps < 30) {
          console.warn(`⚠️ 低帧率检测: ${fps} FPS`);
        }
        
        frameCount = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(checkFrame);
    };

    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(checkFrame);
    }
  }

  /**
   * 开始内存监控
   */
  startMemoryMonitoring() {
    const checkMemory = () => {
      if (!this.isMonitoring) return;

      let memoryInfo = null;

      // #ifdef H5
      if (typeof performance !== 'undefined' && performance.memory) {
        memoryInfo = {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024), // MB
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        };
      }
      // #endif

      if (memoryInfo) {
        this.metrics.memoryUsage.push({
          timestamp: Date.now(),
          ...memoryInfo
        });

        // 保持最近100个记录
        if (this.metrics.memoryUsage.length > 100) {
          this.metrics.memoryUsage.shift();
        }

        // 检测内存泄漏
        if (this.metrics.memoryUsage.length > 10) {
          const recent = this.metrics.memoryUsage.slice(-10);
          const trend = this.calculateTrend(recent.map(m => m.used));
          
          if (trend > 5) { // 内存增长超过5MB
            console.warn(`⚠️ 可能的内存泄漏检测: 增长趋势 ${trend.toFixed(2)} MB`);
          }
        }
      }

      setTimeout(checkMemory, 2000); // 每2秒检查一次
    };

    checkMemory();
  }

  /**
   * 开始渲染时间监控
   */
  startRenderTimeMonitoring() {
    // #ifdef H5
    if (typeof PerformanceObserver !== 'undefined') {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.entryType === 'measure' || entry.entryType === 'navigation') {
              this.metrics.renderTimes.push({
                name: entry.name,
                duration: entry.duration,
                timestamp: Date.now()
              });
            }
          });
        });

        observer.observe({ entryTypes: ['measure', 'navigation'] });
        this.observers.push(observer);
      } catch (error) {
        console.warn('渲染时间监控启动失败:', error);
      }
    }
    // #endif
  }

  /**
   * 开始交互监控
   */
  startInteractionMonitoring() {
    // 监控扫码按钮点击
    uni.$on('scannerButtonClick', (data) => {
      this.recordScannerClick(data);
    });

    // 监控扫码响应时间
    uni.$on('scannerResponse', (data) => {
      this.recordScannerResponse(data);
    });
  }

  /**
   * 记录扫码按钮点击
   * @param {Object} data 点击数据
   */
  recordScannerClick(data) {
    this.metrics.scannerClicks++;
    console.log(`📱 扫码按钮点击 #${this.metrics.scannerClicks}`);
  }

  /**
   * 记录扫码响应时间
   * @param {Object} data 响应数据
   */
  recordScannerResponse(data) {
    const responseTime = data.responseTime || 0;
    this.metrics.scannerResponseTimes.push({
      time: responseTime,
      timestamp: Date.now(),
      success: data.success || false
    });

    // 保持最近50个记录
    if (this.metrics.scannerResponseTimes.length > 50) {
      this.metrics.scannerResponseTimes.shift();
    }

    if (responseTime > 3000) {
      console.warn(`⚠️ 扫码响应时间过长: ${responseTime}ms`);
    }

    console.log(`⏱️ 扫码响应时间: ${responseTime}ms`);
  }

  /**
   * 记录通用指标
   * @param {string} name 指标名称
   * @param {*} value 指标值
   */
  recordMetric(name, value) {
    if (!this.metrics[name]) {
      this.metrics[name] = [];
    }

    if (Array.isArray(this.metrics[name])) {
      this.metrics[name].push({
        value,
        timestamp: Date.now()
      });

      // 限制数组大小
      if (this.metrics[name].length > 100) {
        this.metrics[name].shift();
      }
    } else {
      this.metrics[name] = value;
    }
  }

  /**
   * 计算数据趋势
   * @param {Array} data 数据数组
   * @returns {number} 趋势值
   */
  calculateTrend(data) {
    if (data.length < 2) return 0;

    const first = data[0];
    const last = data[data.length - 1];
    return last - first;
  }

  /**
   * 获取性能报告
   * @returns {Object} 性能报告
   */
  getPerformanceReport() {
    const now = Date.now();
    const duration = this.startTime ? now - this.startTime : 0;

    // 计算平均响应时间
    const avgResponseTime = this.metrics.scannerResponseTimes.length > 0
      ? this.metrics.scannerResponseTimes.reduce((sum, item) => sum + item.time, 0) / this.metrics.scannerResponseTimes.length
      : 0;

    // 计算成功率
    const successRate = this.metrics.scannerResponseTimes.length > 0
      ? (this.metrics.scannerResponseTimes.filter(item => item.success).length / this.metrics.scannerResponseTimes.length) * 100
      : 0;

    // 获取最新内存使用
    const latestMemory = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1]
      : null;

    return {
      duration: Math.round(duration / 1000), // 秒
      scannerMetrics: {
        totalClicks: this.metrics.scannerClicks,
        averageResponseTime: Math.round(avgResponseTime),
        successRate: Math.round(successRate * 100) / 100,
        totalFrameDrops: this.metrics.frameDrops
      },
      memoryMetrics: latestMemory ? {
        currentUsage: latestMemory.used,
        totalAvailable: latestMemory.total,
        usagePercentage: Math.round((latestMemory.used / latestMemory.total) * 100)
      } : null,
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 生成性能建议
   * @returns {Array} 建议列表
   */
  generateRecommendations() {
    const recommendations = [];
    const report = this.getPerformanceReport();

    if (report.scannerMetrics.averageResponseTime > 3000) {
      recommendations.push('扫码响应时间过长，建议检查设备性能或网络状况');
    }

    if (report.scannerMetrics.totalFrameDrops > 10) {
      recommendations.push('检测到频繁掉帧，建议启用低端设备优化模式');
    }

    if (report.memoryMetrics && report.memoryMetrics.usagePercentage > 80) {
      recommendations.push('内存使用率较高，建议清理不必要的资源');
    }

    if (report.scannerMetrics.successRate < 80) {
      recommendations.push('扫码成功率较低，建议检查相机权限或光线条件');
    }

    return recommendations;
  }

  /**
   * 重置监控数据
   */
  reset() {
    this.metrics = {
      scannerClicks: 0,
      scannerResponseTimes: [],
      frameDrops: 0,
      memoryUsage: [],
      cpuUsage: [],
      renderTimes: []
    };
    this.startTime = Date.now();
    console.log('🔄 性能监控数据已重置');
  }

  /**
   * 导出监控数据
   * @returns {string} JSON格式的监控数据
   */
  exportData() {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      metrics: this.metrics,
      report: this.getPerformanceReport()
    }, null, 2);
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor();

/**
 * 开始性能监控
 */
export function startPerformanceMonitoring() {
  performanceMonitor.startMonitoring();
}

/**
 * 停止性能监控
 */
export function stopPerformanceMonitoring() {
  performanceMonitor.stopMonitoring();
}

/**
 * 获取性能报告
 * @returns {Object} 性能报告
 */
export function getPerformanceReport() {
  return performanceMonitor.getPerformanceReport();
}

/**
 * 记录扫码点击
 * @param {Object} data 点击数据
 */
export function recordScannerClick(data = {}) {
  uni.$emit('scannerButtonClick', data);
}

/**
 * 记录扫码响应
 * @param {Object} data 响应数据
 */
export function recordScannerResponse(data) {
  uni.$emit('scannerResponse', data);
}

/**
 * 重置监控数据
 */
export function resetPerformanceMonitoring() {
  performanceMonitor.reset();
}

export default performanceMonitor;
