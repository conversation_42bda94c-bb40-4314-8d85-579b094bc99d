/**
 * 短信相关API接口
 */
import Request from '../../utils/request'

/**
 * 发送短信验证码
 * @param {string} phone - 手机号码
 * @returns {Promise<any>} - 返回Promise对象
 */
export function sendSmsCode(phone: string): Promise<any> {
  console.log('发送短信验证码，手机号:', phone);
  try {
    return Request.post('/sms/records/send', { phone })
      .then(response => {
        console.log('短信验证码发送成功:', response);
        return response;
      })
      .catch(error => {
        console.error('短信验证码发送失败:', error);
        throw error;
      });
  } catch (err) {
    console.error('发送短信验证码异常:', err);
    throw err;
  }
}

/**
 * 验证码登录
 * @param {string} phonenumber - 手机号码
 * @param {string} smsCode - 验证码
 * @returns {Promise<any>} - 返回Promise对象
 */
export function loginBySmsCode(phonenumber: string, smsCode: string): Promise<any> {
  console.log('验证码登录，手机号:', phonenumber, '验证码:', smsCode);
  try {
    return Request.post('/auth/login', {
      clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
      grantType: "sms",
      phonenumber,
      rememberMe: false,
      tenantId: "000000",
      smsCode
    })
    .then(response => {
      console.log('验证码登录成功:', response);
      return response;
    })
    .catch(error => {
      console.error('验证码登录失败:', error);
      throw error;
    });
  } catch (err) {
    console.error('验证码登录异常:', err);
    throw err;
  }
}


