/**
 * WebSocket管理器
 * 用于充电状态实时数据推送
 */
class WebSocketManager {
  constructor() {
    this.socket = null
    this.url = ''
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.heartbeatInterval = 15000
    this.heartbeatTimer = null
    this.reconnectTimer = null
    this.listeners = new Map()
    this.isManualClose = false

    // 页面可见性检测
    this.isPageVisible = true
    this.setupVisibilityListener()
  }

  /**
   * 连接WebSocket
   * @param {string} url WebSocket地址
   * @param {object} options 连接选项
   */
  connect(url, options = {}) {
    this.url = url
    this.isManualClose = false

    console.log('=== WebSocket连接尝试 ===')
    console.log('连接URL:', url)
    console.log('连接选项:', options)

    // 检测运行环境
    // #ifdef H5
    console.log('WebSocket运行环境: H5')
    // #endif
    // #ifdef APP-PLUS
    console.log('WebSocket运行环境: APP')
    // #endif

    this.socket = uni.connectSocket({
      url: url,
      header: options.header || {},
      protocols: options.protocols || [],
      success: () => {
        console.log('✅ WebSocket连接请求发送成功')
      },
      fail: (error) => {
        console.error('❌ WebSocket连接请求失败:', error)
        this.handleConnectionError()
      }
    })

    this.setupSocketEvents()
  }

  /**
   * 设置Socket事件监听
   */
  setupSocketEvents() {
    // 连接打开
    this.socket.onOpen(() => {
      console.log('🎉 WebSocket连接已建立')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.startHeartbeat()
      this.emit('connected')
    })

    // 接收消息
    this.socket.onMessage((res) => {
      try {
        const data = JSON.parse(res.data)
        console.log('📨 [全局WebSocket] 收到消息:', data)
        console.log('📨 [全局WebSocket] 接收时间:', new Date().toLocaleTimeString())

        // 处理心跳响应
        if (data.type === 'pong') {
          console.log('💓 [全局WebSocket] 收到心跳响应')
          return
        }

        // 如果是充电数据，额外打印
        if (data.method === 'ChargingReply' || data.type === 'charging_reply') {
          console.log('🔋 [全局WebSocket] 收到充电数据推送!')
          console.log('🔋 [全局WebSocket] 数据内容:', JSON.stringify(data, null, 2))
        }

        this.emit('message', data)
      } catch (error) {
        console.error('❌ 解析WebSocket消息失败:', error)
        console.error('原始消息数据:', res.data)
      }
    })

    // 连接关闭
    this.socket.onClose((res) => {
      console.log('🔌 WebSocket连接已关闭:', res)
      console.log('关闭代码:', res.code)
      console.log('关闭原因:', res.reason)
      this.isConnected = false
      this.stopHeartbeat()
      this.emit('disconnected', res)

      // 如果不是手动关闭，尝试重连
      if (!this.isManualClose) {
        console.log('🔄 准备自动重连...')
        this.handleReconnect()
      }
    })

    // 连接错误
    this.socket.onError((error) => {
      console.error('❌ WebSocket连接错误:', error)
      console.error('错误详情:', JSON.stringify(error, null, 2))
      this.isConnected = false
      this.emit('error', error)
      this.handleConnectionError()
    })
  }

  /**
   * 发送消息
   * @param {object} data 要发送的数据
   */
  send(data) {
    if (!this.isConnected || !this.socket) {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }

    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data)
      this.socket.send({
        data: message,
        success: () => {
          console.log('WebSocket消息发送成功:', data)
        },
        fail: (error) => {
          console.error('WebSocket消息发送失败:', error)
        }
      })
      return true
    } catch (error) {
      console.error('WebSocket发送消息异常:', error)
      return false
    }
  }

  /**
   * 关闭连接
   */
  close() {
    this.isManualClose = true
    this.stopHeartbeat()
    this.clearReconnectTimer()

    if (this.socket) {
      this.socket.close({
        code: 1000,
        reason: t('common.manualClose') || 'Fermeture manuelle'
      })
    }

    this.isConnected = false
    this.socket = null
    console.log('WebSocket连接已手动关闭')
  }

  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.isPageVisible) {
        this.send({
          type: 'ping',
          timestamp: Date.now()
        })
      }
    }, this.heartbeatInterval)
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连')
      this.emit('reconnectFailed')
      return
    }

    this.reconnectAttempts++
    console.log(`WebSocket准备第${this.reconnectAttempts}次重连...`)
    // 通知外部：进入重连中状态
    this.emit('reconnecting', { attempt: this.reconnectAttempts })

    this.reconnectTimer = setTimeout(() => {
      console.log(`执行第${this.reconnectAttempts}次重连`)
      this.connect(this.url)
    }, this.reconnectInterval)
  }

  /**
   * 处理连接错误
   */
  handleConnectionError() {
    this.isConnected = false
    if (!this.isManualClose) {
      this.handleReconnect()
    }
  }

  /**
   * 清除重连定时器
   */
  clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 设置页面可见性监听
   */
  setupVisibilityListener() {
    // 监听页面显示/隐藏
    uni.onAppShow(() => {
      this.isPageVisible = true
      console.log('应用进入前台，WebSocket恢复活跃状态')
    })

    uni.onAppHide(() => {
      this.isPageVisible = false
      console.log('应用进入后台，WebSocket进入休眠状态')
    })
  }

  /**
   * 添加事件监听器
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event 事件名称
   * @param {any} data 事件数据
   */
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`WebSocket事件回调执行失败 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      isPageVisible: this.isPageVisible,
      url: this.url
    }
  }
}

// 创建单例实例
const websocketManager = new WebSocketManager()

export default websocketManager
