<template>
  <!-- 网络重连提示 -->
  <view v-if="networkStore.showReconnectToast && networkStore.isConnected" class="network-reconnect-toast">
    <view class="reconnect-content">
      <view class="reconnect-icon">
        <image src="/static/images/success.png" mode="aspectFit" class="icon-image"></image>
      </view>
      <text class="reconnect-text">{{ $t('network.reconnected') || '网络已恢复连接' }}</text>
    </view>
  </view>

  <!-- 弱网络提示 -->
  <view v-if="networkStore.isConnected && networkStore.isWeakNetwork" class="weak-network-toast">
    <view class="weak-content">
      <view class="weak-icon">
        <image src="/static/images/tip.png" mode="aspectFit" class="icon-image"></image>
      </view>
      <text class="weak-text">{{ $t('network.weakSignal') || '当前网络信号较弱' }}</text>
    </view>
  </view>
</template>

<script setup>
import { useNetworkStore } from '../../store/network'
import { computed, onMounted } from 'vue'

const networkStore = useNetworkStore()

onMounted(() => {
  console.log('🔍 NetworkStatusToast 组件已挂载')
  console.log('🔍 当前网络状态:', {
    isConnected: networkStore.isConnected,
    networkType: networkStore.networkType,
    showReconnectToast: networkStore.showReconnectToast
  })
})
</script>

<style lang="less" scoped>
// 网络重连提示
.network-reconnect-toast {
  position: fixed;
  top: 88rpx; // 状态栏高度
  left: 0;
  right: 0;
  z-index: 9999;
  background: linear-gradient(180deg, rgba(52, 199, 89, 0.95) 0%, rgba(48, 209, 88, 0.95) 100%);
  backdrop-filter: blur(10px);
  padding: 16rpx 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: slideDown 0.3s ease-out;
  box-shadow: 0 4rpx 16rpx rgba(52, 199, 89, 0.3);

  .reconnect-content {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .reconnect-icon {
      width: 40rpx;
      height: 40rpx;
      
      .icon-image {
        width: 100%;
        height: 100%;
      }
    }

    .reconnect-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #ffffff;
      line-height: 1.4;
    }
  }
}

// 弱网络提示
.weak-network-toast {
  position: fixed;
  top: 88rpx; // 避免与状态栏重叠
  left: 32rpx;
  right: 32rpx;
  z-index: 9998;
  background: linear-gradient(180deg, rgba(255, 149, 0, 0.95) 0%, rgba(255, 170, 0, 0.95) 100%);
  backdrop-filter: blur(10px);
  padding: 12rpx 24rpx;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
  box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.3);

  .weak-content {
    display: flex;
    align-items: center;
    gap: 12rpx;

    .weak-icon {
      width: 32rpx;
      height: 32rpx;
      
      .icon-image {
        width: 100%;
        height: 100%;
      }
    }

    .weak-text {
      font-size: 24rpx;
      font-weight: 500;
      color: #ffffff;
      line-height: 1.3;
    }
  }
}

// 动画效果
@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>