<template>
  <view class="expense-settlement">
    <!-- 固定头部区域 -->
    <view class="fixed-header">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <!-- 导航栏 -->
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back"></text>
          </view>
          <text class="title">Expense Settlement</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-scroll">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ t('payment.loadingOrderDetails') }}</text>
      </view>

      <!-- 内容区域 -->
      <template v-else>
        <view class="station-name">
          <text>{{ orderData?.plotName || t('charging.stationName') }}</text>
        </view>

        <view class="charging-info-card">
          <view class="car-id">
            <text>{{ orderData?.carNum || orderData?.carId || t('payment.chargingVehicle') }}</text>
          </view>
          <view class="admission-time">
            <text>{{ t('payment.admissionTime') }}: {{ formatDateTime(orderData?.startTime) }}</text>
          </view>
          <view class="charging-stats">
            <view class="stat-item">
              <view class="value-container">
                <text class="value">{{ orderData?.realMinute || '0' }}</text>
                <text class="unit">min</text>
              </view>
              <text class="label">{{ t('payment.chargingDuration') }}</text>
            </view>
            <view class="stat-item">
              <view class="value-container">
                <text class="value">{{ orderData?.degree || '0' }}</text>
                <text class="unit">kWh</text>
              </view>
              <text class="label">{{ t('payment.chargedAmount') }}</text>
            </view>
          </view>
        </view>

        <view class="expense-details">
          <view class="expense-item">
            <text class="label">{{ t('payment.chargingAmount') }}</text>
            <text class="value">{{ orderData?.orderPrice || '0.00' }} F</text>
          </view>
          <view class="expense-item">
            <text class="label">{{ t('payment.electricityBill') }}</text>
            <text class="value">{{ orderData?.chargeFee || '0.00' }} F</text>
          </view>
          <view class="expense-item">
            <text class="label">{{ t('payment.serviceFee') }}</text>
            <text class="value">{{ orderData?.serviceFee || '0.00' }} F</text>
          </view>
          <view class="expense-item member-offer" v-if="orderData?.couponPrice">
            <text class="label">{{ t('payment.coupon') }}</text>
            <text class="value">-{{ orderData?.couponPrice }} F</text>
          </view>
          <view class="expense-item" v-if="orderData?.refundAmount && parseFloat(orderData.refundAmount) > 0">
            <text class="label">{{ t('payment.refundAmount') }}</text>
            <text class="value">{{ orderData?.refundAmount }} F</text>
          </view>
          <view class="divider"></view>
          <view class="total-paid">
            <text class="label">{{ t('payment.totalPaid') }}:</text>
            <text class="value">{{ orderData?.actualPaymentPrice || orderData?.orderPrice || '0.00' }} <text
                class="currency">F</text></text>
          </view>
        </view>

        <view class="order-info">
          <view class="info-item clickable">
            <text class="label">{{ t('payment.orderNumber') }}</text>
            <text class="value">{{ orderData?.orderNumber || orderNumber }}</text>
          </view>
          <view class="info-item">
            <text class="label">{{ t('payment.startTime') }}</text>
            <text class="value">{{ orderData?.startTime || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="label">{{ t('payment.endTime') }}</text>
            <text class="value">{{ orderData?.endTime || orderData?.realEndTime || '--' }}</text>
          </view>
          <view class="info-item" v-if="orderData?.spearName">
            <text class="label">{{ t('payment.chargingGun') }}</text>
            <text class="value">{{ orderData?.spearName }}</text>
          </view>
          <view class="info-item" v-if="orderData?.orderState">
            <text class="label">{{ t('payment.orderStatus') }}</text>
            <text class="value">{{ orderData?.orderState }}</text>
          </view>
        </view>
      </template>
    </view>

    <!-- 底部支付按钮 -->
    <view class="footer-bar">
      <button class="pay-btn" @click="handlePay">Pay Now</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getOrderDetail } from '@/api/modules/charging'
import { useI18n } from '@/composables/useI18n.js'

// 国际化
const { t, formatDate } = useI18n()

// 响应式数据
const statusBarHeight = ref(0)
const loading = ref(false)
const orderData = ref(null)
const orderNumber = ref('')

// 使用 onLoad 接收页面参数
onLoad((options) => {
  console.log('=== expense-settlement onLoad 接收参数 ===')
  console.log('接收到的参数:', options)

  if (options && options.orderNumber) {
    orderNumber.value = decodeURIComponent(options.orderNumber)
    console.log('✅ 从onLoad获取订单号:', orderNumber.value)
    // 获取订单详情
    fetchOrderDetail()
  } else {
    console.log('❌ onLoad中没有orderNumber参数，使用默认数据')
  }
})

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!orderNumber.value) {
    console.error('订单号为空，无法获取订单详情')
    return
  }

  try {
    loading.value = true
    console.log('开始获取订单详情，订单号:', orderNumber.value)

    const response = await getOrderDetail(orderNumber.value)
    console.log('订单详情API响应:', response)

    if (response && response.code === 200 && response.data) {
      orderData.value = response.data
      updatePageData(response.data)
      console.log('✅ 订单详情获取成功')
    } else {
      throw new Error(response?.msg || '获取订单详情失败')
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    uni.showToast({
      title: t('payment.getOrderDetailsFailed') || 'Échec de l\'obtention des détails de la commande',
      icon: 'error',
      duration: 2000
    })
  } finally {
    loading.value = false
  }
}

// 更新页面数据
const updatePageData = (data) => {
  console.log('更新页面数据:', data)
  orderData.value = data
  console.log('页面数据更新完成')
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''

  try {
    const date = new Date(dateTimeStr)
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }
    return date.toLocaleDateString('en-US', options)
  } catch (error) {
    console.error('日期格式化失败:', error)
    return dateTimeStr
  }
}

// // 计算充电时长
// const getChargingDuration = () => {
//   if (!orderData.value) return '0'
//   return minutes = parseInt(orderData.value.realMinute) || 0

// }

// 生命周期
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight

  // 设置CSS变量
  document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`)
})

// 方法
const goBack = () => {
  uni.navigateTo({
    url: '/pages/more/index'
  })
}

// // 跳转到订单详情页面
// const goToOrderDetail = () => {
//   // 优先使用 orderId，如果没有则使用 orderNumber
//   const oid = orderData.value?.orderId || ''
//   const onum = orderData.value?.orderNumber || orderNumber.value || ''

//   console.log('跳转到订单详情页面，orderId:', oid, 'orderNumber:', onum)

//   if (oid) {
//     // 如果有 orderId，传递 orderId
//     uni.navigateTo({
//       url: `/pages/payment/order-detail?orderId=${encodeURIComponent(oid)}`
//     })
//   } else if (onum) {
//     // 如果只有 orderNumber，传递 orderNumber
//     uni.navigateTo({
//       url: `/pages/payment/order-detail?orderNumber=${encodeURIComponent(onum)}`
//     })
//   } else {
//     console.error('没有可用的订单ID或订单号')
//     uni.showToast({
//       title: '订单信息不完整',
//       icon: 'error'
//     })
//   }
// }

const handlePay = () => {
  // 跳转到支付密码页面，携带orderId/orderNumber和prepaidFees供后续结算页与成功页使用
  const oid = orderData.value?.orderId || ''
  const onum = orderData.value?.orderNumber || ''
  const prepaidFees = orderData.value?.actualPaymentPrice || orderData.value?.orderPrice || '0'

  uni.navigateTo({
    url: `/pages/payment/password?orderId=${encodeURIComponent(oid)}&orderNumber=${encodeURIComponent(onum)}&prepaidFees=${encodeURIComponent(prepaidFees)}`
  })
}
</script>

<style lang="scss">
@import '@/static/iconfont/iconfont.css';

.expense-settlement {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0, 0, 0, 0.04);
}

.status-bar {
  background-color: #fff;
  width: 100%;
}

.header {
  background: #fff;
  width: 100%;

  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .back-btn {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;

      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 20rpx;
  margin-top: calc(var(--status-bar-height, 0px) + 88rpx);
  padding-bottom: 120rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #6c5ce7;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.station-name {
  padding: 20rpx 32rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  background-color: #f8f9fa;
}

.charging-info-card {
  margin: 0 10rpx 20rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .car-id {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
  }

  .admission-time {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 32rpx;
  }

  .charging-stats {
    display: flex;
    justify-content: space-between;

    .stat-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;

      .value-container {
        display: flex;
        align-items: flex-end;
      }

      .value {
        font-size: 64rpx;
        font-weight: bold;
        color: #333;
        line-height: 1;
      }

      .unit {
        font-size: 28rpx;
        color: #666;
        margin-left: 8rpx;
        margin-bottom: 8rpx;
      }

      .label {
        font-size: 26rpx;
        color: #999;
        margin-top: 16rpx;
        text-align: center;
      }
    }
  }
}

.expense-details {
  margin: 0 10rpx 20rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .expense-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .label {
      font-size: 28rpx;
      color: #333;
    }

    .value {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }

    &.member-offer,
    &.coupon {
      .value {
        color: #ff4d4f;
      }
    }
  }

  .divider {
    height: 1rpx;
    background-color: #f0f0f0;
    margin: 24rpx 0;
  }

  .total-paid {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .label {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .value {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;

      .currency {
        font-size: 28rpx;
        font-weight: normal;
        margin-left: 8rpx;
      }
    }
  }
}

.order-info {
  margin: 0 10rpx 20rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

  .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-size: 28rpx;
      color: #666;
    }

    .value {
      font-size: 28rpx;
      color: #333;
    }

    .arrow {
      font-size: 24rpx;
      color: #999;
      margin-left: 8rpx;
    }

    // 可点击的订单号样式
    &.clickable {
      cursor: pointer;
      transition: background-color 0.2s;
      padding: 8rpx;
      margin: -8rpx;
      border-radius: 8rpx;

      &:active {
        background-color: #f5f5f5;
      }

      .value {
        color: #1890ff;
      }

      .arrow {
        color: #1890ff;
      }
    }
  }
}

.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 32rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .pay-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    background: #ff4d4f;
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 44rpx;
    border: none;
  }
}
</style>