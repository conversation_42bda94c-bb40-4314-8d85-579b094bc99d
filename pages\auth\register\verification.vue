<template>
  <view class="verification-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
        <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- Logo -->
      <view class="logo-section">
        <image class="app-logo" src="/static/images/login_logo.png" mode="aspectFit"></image>
      </view>

      <!-- 标题文本 -->
      <view class="title-section">
        <text class="main-title">{{ $t('auth.enterCode') }}</text>
        <text class="subtitle">{{ maskedPhone }}</text>
      </view>

      <!-- 验证码输入 -->
      <view class="code-input-section">
        <view class="code-inputs">
          <input 
            v-for="(digit, index) in codeDigits" 
            :key="index"
            :id="'codeInput' + index"
            type="text"
            inputmode="numeric"
            class="code-input"
            :class="{ active: activeIndex === index, filled: digit !== '', error: hasError }"
            v-model="codeDigits[index]"
            @input="onCodeInput(index, $event)"
            @focus="onInputFocus(index)"
            @blur="onInputBlur"
            @keydown="onKeyDown(index, $event)"
            @paste="onPaste(index, $event)"
            maxlength="1"
          />
        </view>
      </view>

      <!-- 错误提示 -->
      <view v-if="errorMessage" class="error-section">
        <text class="error-text">{{ errorMessage }}</text>
      </view>

      <!-- 重新发送 -->
      <view class="resend-section">
        <text v-if="countdown > 0" class="countdown-text">
          {{ $t('auth.resendCodeIn') }} {{ countdown }}s
        </text>
        <text v-else class="resend-link" @click="resendCode">
          {{ $t('auth.resendCode') }}
        </text>
      </view>

      <!-- 继续按钮 -->
      <view class="continue-section">
        <button 
          class="continue-btn" 
          :class="{ active: isCodeComplete, disabled: !isCodeComplete }"
          :disabled="!isCodeComplete"
          @click="verifyCode"
        >
          {{ $t('common.continue') }}
        </button>
      </view>
    </view>

    <!-- 全局HUD -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { useGlobalHud } from '@/composables/useHud'
import GlobalHUD from '@/components/common/GlobalHUD.vue'
import { sendSmsCode, verifySmsCode } from '@/api'

// 国际化
const { t: $t } = useI18n()

// 全局HUD
const hud = useGlobalHud()

// 状态栏高度
const statusBarHeight = ref(0)

// 注册数据
const registerData = ref({})
const phoneNumber = ref('')

// 验证码输入
const codeDigits = ref(['', '', '', '', '', ''])
const activeIndex = ref(-1)
const errorMessage = ref('')
const hasError = ref(false)

// 重新发送倒计时
const countdown = ref(0)
const isSending = ref(false)
let countdownTimer = null

// 验证码是否完整
const isCodeComplete = computed(() => {
  return codeDigits.value.every(digit => digit !== '')
})

// 脱敏手机号
const maskedPhone = computed(() => {
  if (!phoneNumber.value) return ''
  const phone = phoneNumber.value
  if (phone.length <= 4) return phone
  return phone.substring(0, phone.length - 4) + '****'
})

// 验证码输入处理
const onCodeInput = (index, event) => {
  let value = event.detail.value
  const oldValue = codeDigits.value[index]
  
  // 清除错误状态
  clearError()
  
  // 只保留数字，并取最后一位
  value = value.replace(/\D/g, '').slice(-1)
  
  // 更新当前输入框的值
  codeDigits.value[index] = value
  
  // 处理正常输入
  if (value && value !== oldValue) {
    // 自动跳转到下一个输入框
    if (index < 5) {
      setTimeout(() => {
        uni.createSelectorQuery()
          .select(`#codeInput${index + 1}`)
          .fields({ node: true })
          .exec((res) => {
            if (res[0] && res[0].node && res[0].node.focus) {
              res[0].node.focus()
            }
          })
      }, 50)
    } else {
      // 最后一位输入完成，自动验证
      setTimeout(() => {
        if (isCodeComplete.value) {
          verifyCode()
        }
      }, 100)
    }
  }
}

// 处理粘贴验证码
const handlePasteCode = (pastedValue, startIndex) => {
  // 清除错误状态
  clearError()
  
  // 只取前6位数字
  const digits = pastedValue.slice(0, 6).split('')
  
  // 先清空所有输入框
  codeDigits.value = ['', '', '', '', '', '']
  
  // 使用nextTick确保DOM更新后再填充
  nextTick(() => {
    // 填充验证码数组
    for (let i = 0; i < Math.min(digits.length, 6); i++) {
      if (digits[i] && /^\d$/.test(digits[i])) {
        codeDigits.value[i] = digits[i]
      }
    }
    
    // 强制更新视图
    nextTick(() => {
      // 如果粘贴的验证码完整，自动验证
      if (digits.length >= 6 && isCodeComplete.value) {
        setTimeout(() => {
          verifyCode()
        }, 200)
      } else {
        // 焦点移动到最后一个填充的位置
        const lastIndex = Math.min(digits.length - 1, 5)
        if (lastIndex >= 0) {
          setTimeout(() => {
            uni.createSelectorQuery()
              .select(`#codeInput${lastIndex}`)
              .fields({ node: true })
              .exec((res) => {
                if (res[0] && res[0].node && res[0].node.focus) {
                  res[0].node.focus()
                }
              })
          }, 100)
        }
      }
    })
  })
}

// 粘贴事件处理
const onPaste = (index, event) => {
  event.preventDefault()
  
  // 获取粘贴的内容
  let pasteData = ''
  if (event.clipboardData) {
    pasteData = event.clipboardData.getData('text')
  } else if (window.clipboardData) {
    pasteData = window.clipboardData.getData('Text')
  }
  
  // 只保留数字
  const digits = pasteData.replace(/\D/g, '')
  
  if (digits.length > 0) {
    handlePasteCode(digits, index)
  }
}

// 键盘按键事件处理
const onKeyDown = (index, event) => {
  const keyCode = event.keyCode || event.which
  
  // 处理删除键（Backspace: 8）
  if (keyCode === 8) {
    // 如果当前输入框有内容，删除内容（让默认行为处理）
    if (codeDigits.value[index]) {
      // 不阻止默认行为，让输入框自然删除
      return true
    } else if (index > 0) {
      // 如果当前输入框为空，跳转到上一个输入框并删除其内容
      event.preventDefault()
      codeDigits.value[index - 1] = ''
      nextTick(() => {
        uni.createSelectorQuery()
          .select(`#codeInput${index - 1}`)
          .fields({ node: true })
          .exec((res) => {
            if (res[0] && res[0].node && res[0].node.focus) {
              res[0].node.focus()
            }
          })
      })
      return false
    }
  }
}

// 输入框获得焦点
const onInputFocus = (index) => {
  activeIndex.value = index
  clearError()
}

// 输入框失去焦点
const onInputBlur = () => {
  activeIndex.value = -1
}

// 清除错误状态
const clearError = () => {
  errorMessage.value = ''
  hasError.value = false
}

// 验证验证码
const verifyCode = async () => {
  if (!isCodeComplete.value) {
    return
  }

  const code = codeDigits.value.join('')
  
  try {
    hud.loading($t('auth.verifying'))

    const response = await verifySmsCode({
      phonenumber: phoneNumber.value,
      smsCode: code,
      clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
      grantType: "sms"
    })

    if (response && (response.code === 200 || response.code === "200")) {
      hud.success($t('auth.verificationSuccess'), 1000)
      
      // 更新注册数据
      const updatedData = {
        ...registerData.value,
        verificationCode: code,
        step: 2
      }
      uni.setStorageSync('registerData', JSON.stringify(updatedData))
      
      // 跳转到姓名输入页面
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/auth/register/name'
        })
      }, 1000)
    } else {
      throw new Error(response?.message || $t('auth.invalidCode'))
    }
  } catch (error) {
    console.error('验证码验证失败:', error)
    
    hasError.value = true
    let errorText = $t('auth.verificationFailed')
    if (error.response?.data?.message) {
      errorText = error.response.data.message
    } else if (error.message) {
      errorText = error.message
    }
    
    errorMessage.value = errorText
    hud.error(errorText)
    
    // 清空验证码
    codeDigits.value = ['', '', '', '', '', '']
    setTimeout(() => {
      uni.createSelectorQuery()
        .select('#codeInput0')
        .fields({ node: true })
        .exec((res) => {
          if (res[0] && res[0].node && res[0].node.focus) {
            res[0].node.focus()
          }
        })
    }, 100)
  }
}

// 重新发送验证码
const resendCode = async () => {
  if (isSending.value || countdown.value > 0) {
    return
  }

  try {
    isSending.value = true
    hud.loading($t('auth.sending'))

    const response = await sendSmsCode(phoneNumber.value)
    
    if (response) {
      hud.success($t('auth.codeSent'), 1500)
      startCountdown()
    }
  } catch (error) {
    console.error('重新发送验证码失败:', error)
    hud.error($t('auth.sendFailed'))
  } finally {
    isSending.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
  
  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 组件挂载
onMounted(() => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })

  // 获取注册数据
  try {
    const data = uni.getStorageSync('registerData')
    if (data) {
      registerData.value = JSON.parse(data)
      phoneNumber.value = registerData.value.phoneNumber || ''
    }
  } catch (error) {
    console.error('获取注册数据失败:', error)
    // 如果没有注册数据，返回手机号页面
    uni.navigateBack()
    return
  }

  // 自动发送验证码
  if (phoneNumber.value) {
    nextTick(() => {
      resendCode()
    })
  }

  // 自动聚焦第一个输入框
  setTimeout(() => {
    uni.createSelectorQuery()
      .select('#codeInput0')
      .fields({ node: true })
      .exec((res) => {
        if (res[0] && res[0].node && res[0].node.focus) {
          res[0].node.focus()
        }
      })
  }, 200)
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})
</script>

<style lang="less">
.verification-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 40rpx;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    height: 88rpx;

    .back-icon {
      font-size: 32rpx;
      color: #000;
      font-weight: bold;
      padding: 20rpx;
      margin-left: -20rpx;
    }
  }

  .main-content {
    flex: 1;
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;

    .logo-section {
      text-align: center;
      margin: 40rpx 0 60rpx;

      .app-logo {
        width: 120rpx;
        height: 120rpx;
      }
    }

    .title-section {
      text-align: center;
      margin: 0 0 60rpx;

      .main-title {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
        line-height: 1.3;
      }

      .subtitle {
        display: block;
        font-size: 32rpx;
        color: #666;
        line-height: 1.4;
      }
    }

    .code-input-section {
      margin-bottom: 40rpx;

      .code-inputs {
        display: flex;
        justify-content: center;
        gap: 24rpx;

        .code-input {
          width: 72rpx;
          height: 80rpx;
          background: transparent;
          border: none;
          border-bottom: 4rpx solid #E5E5E5;
          text-align: center;
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
          box-sizing: border-box;
          transition: all 0.3s ease;
          outline: none;

          &.active {
            border-bottom-color: #f23030;
          }

          &.filled {
            border-bottom-color: #f23030;
          }

          &.error {
            border-bottom-color: #f23030;
          }
        }
      }
    }

    .error-section {
      margin-bottom: 40rpx;

      .error-text {
        color: #f23030;
        font-size: 28rpx;
        text-align: center;
      }
    }

    .resend-section {
      text-align: center;
      margin-bottom: 60rpx;

      .countdown-text {
        font-size: 28rpx;
        color: #999;
      }

      .resend-link {
        font-size: 28rpx;
        color: #f23030;
        font-weight: 500;
      }
    }

    .continue-section {
      margin-bottom: 60rpx;

      .continue-btn {
        width: 100%;
        height: 88rpx;
        background: #ccc;
        color: #999;
        border-radius: 56rpx;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;

        &.active {
          background: #f23030;
          color: #fff;
        }

        &.disabled {
          opacity: 0.6;
        }
      }
    }
  }
}
</style>
