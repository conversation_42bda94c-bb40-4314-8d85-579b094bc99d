<template>
    <scroll-view
        class="home-container"
        scroll-y="true"
    >
        <view class="scroll-content">
            <!-- 顶部状态栏 -->
        <view class="status-bar">
            <view class="wallet">
                <text class="label">{{ $t('account.balance') }}</text>
                <view class="amount-row" @click="goToMore">
                    <text class="amount">{{ userBalance }}F</text>
                    <text class="iconfont icon-righttop"></text>
                </view>
            </view>
            <image src="/static/images/miall_logo.png" mode="aspectFit" class="logo"></image>
        </view>
        <!-- 会员订阅轮播图 -->
        <swiper class="membership-swiper" circular autoplay interval="3000" duration="500">
            <swiper-item>
                <view class="membership-card">
                    <image src="/static/images/banner-bg-1.png" mode="aspectFill" class="banner-image"></image>
                    <view class="card-content">
                        <text class="title">{{ $t('account.membership') || 'Subscribe to the membership' }}</text>
                        <text class="subtitle">{{ $t('account.membershipSubtitle') || 'and enjoy great gifts.' }}</text>
                    </view>
                    <view class="go-btn">{{ $t('common.go') || 'GO' }}</view>
                </view>
            </swiper-item>
            <!-- 可以添加更多轮播项 -->
        </swiper>

        <!-- 待支付订单提示 -->
        <view v-if="hasPendingPayment" class="pending-payment-alert">
            <view class="alert-container">
                <!-- 左侧图标 -->
                <view class="alert-icon">
                    <view class="credit-card-icon">
                        <view class="card-body"></view>
                        <view class="card-stripe"></view>
                        <view class="card-chip"></view>
                    </view>
                </view>

                <!-- 中间内容 -->
                <view class="alert-content">
                    <view class="alert-title">{{ $t('payment.pendingOrder') || 'Pending Payment' }}</view>
                    <view class="alert-subtitle">
                        <text class="order-info" v-if="pendingOrderNumber">{{ $t('payment.orderNumber') || 'Order' }}: {{ pendingOrderNumber }}</text>
                        <text class="order-info" v-else>{{ $t('payment.hasPendingOrder') || 'You have unpaid orders' }}</text>
                        <text class="amount-info" v-if="pendingAmount">{{ $t('payment.amount') || 'Amount' }}: {{ pendingAmount }}F</text>
                    </view>
                </view>

                <!-- 右侧支付按钮 -->
                <view class="alert-actions">
                    <view class="pay-btn" @click="goToPendingPayment">
                        <text class="pay-text">{{ $t('payment.pay') || 'Pay' }}</text>
                        <view class="pay-arrow">→</view>
                    </view>
                </view>
            </view>

            <!-- 底部关闭按钮 -->
            <view class="alert-close-wrapper">
                <view class="close-btn" @click="closePendingAlert">
                    <view class="close-handle"></view>
                </view>
            </view>
        </view>

        <!-- 充电桩状态 -->
        <view class="charger-status">
            <text class="status-tag"  :class="{ 'charging': isCharging }"
                @click="handleStopCharging">
                {{ isCharging ? $t('station.stop') : $t('station.available') }}
            </text>
        </view>

        <!-- 充电桩区域 -->
        <view class="charger-section">
            <!-- 左侧状态图标 -->
            <view class="status-icons" :class="{ 'full-spacing': isCharging && chargingPercentage === 100 }">
                <!-- 充电状态显示 -->
                <view v-if="isCharging" class="charging-status">
                    <view class="percentage" @click="goToChargingStatus">{{ chargingPercentage }}%</view>
                    <view class="time">{{ chargingTime }} min</view>
                    <image src="/static/images/progree-out.png" mode="aspectFit" class="charging-icon"></image>
                    <image src="/static/images/unlock.png" mode="aspectFit" class="charging-icon unlock_icon"></image>
                </view>

                <!-- 非充电状态显示 -->
                <template v-else>
                    <view class="icon-item">
                        <image src="/static/images/lock.png" mode="aspectFit" class="icon"></image>
                    </view>
                    <view class="icon-item progress-icon">
                        <image src="/static/images/progress-bar.png" mode="aspectFit" class="icon"></image>
                    </view>
                </template>

                <!-- 充电桩扫码组件 - 始终存在，通过disabled控制 -->
                <ChargingPileScanner :disabled="isCharging" @scan-success="handleScannerSuccess"
                    @charging-start="handleChargingStart" @charging-test="handleChargingTest"
                    @scan-fail="handleScannerFail" @charging-error="handleChargingError"
                    @loading-change="handleLoadingChange" @step-change="handleStepChange"
                    @model-type-change="handleModelTypeChange" />
            </view>

            <!-- 充电桩图片 -->
            <view class="charger-container">
                <!-- #ifdef APP-PLUS -->
                <ChargingPile3DAppNew ref="chargingPile3DRef" class="charger-3d" :modelType="currentModelType" @loaded="onCharging3DLoaded">
                </ChargingPile3DAppNew>
                <!-- #endif -->

                <!-- #ifndef APP-PLUS -->
                <ChargingPile3D ref="chargingPile3DRef" class="charger-3d" :modelType="currentModelType" @loaded="onCharging3DLoaded">
                </ChargingPile3D>
                <!-- #endif -->

                <!-- 充电桩下方的loading效果 -->
                <!-- <view class="charging-loading" v-if="showChargingLoading && chargingStep < 2">
                    <view class="loading-spinner"></view>
                    <text class="loading-text">{{ $t('station.startCharging') || 'Start Charging' }}</text>
                </view> -->
            </view>

            <!-- 充电状态弹窗 - 高级版本 -->
            <view class="charging-popup-premium" v-if="chargingStep > 0">
                <view class="popup-backdrop-premium"></view>
                <view class="popup-content-premium">
                    <!-- 顶部装饰条 -->
                    <view class="popup-handle-premium"></view>

                    <!-- 状态图标区域 -->
                    <view class="status-icon-area-premium">
                        <view class="status-icon-container-premium" :class="{ 'error': chargeError, 'success': chargingStep >= 4 && !chargeError }">
                            <!-- App Logo图标 -->
                            <view class="app-logo-container">
                                <image src="/static/images/ARNIO.png" mode="aspectFit" class="app-logo" v-if="!chargeError && chargingStep < 4"></image>
                                <text class="status-icon-text" v-if="chargeError">✕</text>
                                <text class="status-icon-text success" v-else-if="chargingStep >= 4">✓</text>
                            </view>
                            <!-- 加载动画环 -->
                            <view class="loading-ring" v-if="!chargeError && chargingStep < 4"></view>
                        </view>
                        <view class="status-glow-premium" :class="{ 'error': chargeError, 'success': chargingStep >= 4 && !chargeError }"></view>
                    </view>

                    <!-- 标题区域 -->
                    <view class="popup-header-premium">
                        <text class="header-title-premium" :class="{ 'error': chargeError }">
                            {{ getPopupTitle() }}
                        </text>
                        <text class="header-subtitle-premium" v-if="!chargeError">
                            {{ getPopupSubtitle() }}
                        </text>
                        <text class="error-message-premium" v-if="chargeError">
                            {{ errorMessage }}
                        </text>
                    </view>

                    <!-- 充电步骤状态列表 -->
                    <view class="charging-steps-premium" v-if="!chargeError">
                        <!-- 进度条 -->
                        <view class="progress-track-premium">
                            <view class="progress-fill-premium" :style="{ width: getProgressWidth() }"></view>
                        </view>

                        <!-- 步骤列表 -->
                        <view class="steps-container-premium">
                            <!-- 步骤1: 扫码识别 -->
                            <view class="step-item-premium" :class="getStepClass(1)">
                                <view class="step-icon-premium">
                                    <text class="step-number" v-if="chargingStep < 1">1</text>
                                    <text class="step-check" v-else-if="chargingStep > 1">✓</text>
                                    <view class="step-loading" v-else></view>
                                </view>
                                <view class="step-content-premium">
                                    <text class="step-text-premium">{{ $t('popup.step1') || 'Scanning QR Code' }}</text>
                                    <text class="step-desc-premium" v-if="chargingStep === 1">{{ $t('popup.connectingToPile') || 'Connecting to charging pile' }}</text>
                                </view>
                            </view>

                            <!-- 步骤2: 验证信息 -->
                            <view class="step-item-premium" :class="getStepClass(2)">
                                <view class="step-icon-premium">
                                    <text class="step-number" v-if="chargingStep < 2">2</text>
                                    <text class="step-check" v-else-if="chargingStep > 2">✓</text>
                                    <view class="step-loading" v-else></view>
                                </view>
                                <view class="step-content-premium">
                                    <text class="step-text-premium">{{ $t('popup.step2') || 'Verifying Information' }}</text>
                                    <text class="step-desc-premium" v-if="chargingStep === 2">{{ $t('popup.preparingCharging') || 'Preparing charging session' }}</text>
                                </view>
                            </view>

                            <!-- 步骤3: 处理支付 -->
                            <view class="step-item-premium" :class="getStepClass(3)">
                                <view class="step-icon-premium">
                                    <text class="step-number" v-if="chargingStep < 3">3</text>
                                    <text class="step-check" v-else-if="chargingStep > 3">✓</text>
                                    <view class="step-loading" v-else></view>
                                </view>
                                <view class="step-content-premium">
                                    <text class="step-text-premium">{{ $t('popup.step3') || 'Processing Payment' }}</text>
                                    <text class="step-desc-premium" v-if="chargingStep === 3">{{ $t('popup.processingPayment') || 'Processing payment' }}</text>
                                </view>
                            </view>

                            <!-- 步骤4: 启动充电 -->
                            <view class="step-item-premium" :class="getStepClass(4)">
                                <view class="step-icon-premium">
                                    <text class="step-number" v-if="chargingStep < 4">4</text>
                                    <text class="step-check" v-else-if="chargingStep >= 4">✓</text>
                                    <view class="step-loading" v-else></view>
                                </view>
                                <view class="step-content-premium">
                                    <text class="step-text-premium">{{ $t('popup.step4') || 'Starting Charging' }}</text>
                                    <text class="step-desc-premium" v-if="chargingStep === 4">{{ $t('popup.chargingStarted') || 'Charging started successfully' }}</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 底部操作区域 -->
                    <view class="popup-footer-premium">
                        <!-- 错误时显示关闭按钮 -->
                        <view class="error-actions-premium" v-if="chargeError">
                            <!-- 如果是待支付订单，显示两个按钮 -->
                            <template v-if="isPendingPayment">
                                <view class="payment-button-premium" @click="handleGoToPay">
                                    <text class="button-text-premium">{{ $t('payment.goToPay') }}</text>
                                </view>
                                <view class="close-button-premium" @click="handleCancelCharging">
                                    <text class="button-text-premium">{{ $t('common.close') || 'Close' }}</text>
                                </view>
                            </template>
                            <!-- 普通错误只显示关闭按钮 -->
                            <template v-else>
                                <view class="close-button-premium" @click="handleCancelCharging">
                                    <text class="button-text-premium">{{ $t('common.close') || 'Close' }}</text>
                                </view>
                            </template>
                        </view>

                        <!-- 底部提示信息 -->
                        <view class="bottom-tips-premium" v-if="!chargeError">
                            <view class="tip-icon-premium">💡</view>
                            <text class="tip-text-premium">{{ $t('popup.keepConnection') || 'Please keep your phone connected to the charging pile' }}</text>
                        </view>

                        <!-- 错误提示信息 -->
                        <view class="error-tips-premium" v-if="chargeError">
                            <view class="error-icon-tip-premium">⚠️</view>
                            <text class="error-tip-text-premium">{{ errorMessage || $t('popup.chargingFailed') || 'Charging process failed' }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 右侧闪电图标 -->
            <view class="lightning-icon" :class="{ 'disabled': isCharging }" >
                <image src="/static/images/switch.png" @click="toggleModel" mode="aspectFit"></image>
            </view>


        </view>

        <!-- 地图区域 -->
        <view class="map-container" @click="goToMap">
            <image src="/static/images/Carte_statique.jpg" mode="aspectFit" class="map-image"></image>
        </view>

        <!-- 现代化提示弹窗 -->
        <view v-if="showCustomModal" class="modern-modal-overlay" @click="closeCustomModal">
            <view class="modern-modal-container" @click.stop>
                <!-- 顶部图标区域 -->
                <view class="modern-modal-icon">
                    <view class="icon-circle">
                        <image src="/static/images/scan-to-charge.png" mode="aspectFit" class="modal-icon-img"></image>
                    </view>
                </view>

                <!-- 内容区域 -->
                <view class="modern-modal-content">
                    <text class="modern-modal-title">{{ $t('popup.noActiveOrder') || 'No Active Charging' }}</text>
                    <text class="modern-modal-message">{{ $t('popup.noActiveOrderMessage') || 'You currently have no active charging session. Please scan the QR code on a charging pile to start.' }}</text>
                </view>

                <!-- 操作步骤 -->
                <view class="charging-steps-guide">
                    <view class="step-guide-item">
                        <view class="step-number">1</view>
                        <text class="step-text">Find a charging pile</text>
                    </view>
                    <view class="step-guide-item">
                        <view class="step-number">2</view>
                        <text class="step-text">Scan the QR code</text>
                    </view>
                    <view class="step-guide-item">
                        <view class="step-number">3</view>
                        <text class="step-text">Start charging</text>
                    </view>
                </view>

                <!-- 底部按钮 -->
                <view class="modern-modal-footer">
                    <view class="modern-modal-button" @click="closeCustomModal">
                        <text class="modern-button-text">{{ $t('popup.gotIt') || 'Got It' }}</text>
                    </view>
                </view>
            </view>
        </view>

  
            <!-- Toast容器 -->
            <ToastContainer />
            
            
          </view>
    </scroll-view>

    <!-- 全局HUD -->
    <GlobalHUD />

    <!-- 更新弹窗 -->
    <UpdateDialog
      :visible="updateDialogVisible"
      :version-num="updateInfo?.versionNum || ''"
      :version-description="updateInfo?.versionDescription || ''"
      :download-url="updateInfo?.programPackageUrl || ''"
      :is-mandatory-update="updateInfo?.isMandatoryUpdate || false"
      @close="closeUpdateDialog"
      @update="performUpdate"
    />
    
    <!-- 充电结算Loading弹窗 -->
    <ChargingSettlementModal 
        :visible="showSettlementLoading"
        :message="settlementMessage"
    />
    

</template>

<script setup>
import ChargingPile3D from '@/components/ChargingPile3D.vue';
// #ifdef APP-PLUS
import ChargingPile3DAppNew from '@/components/ChargingPile3D/ChargingPile3DApp_new.vue';
// #endif
import ChargingPileScanner from '@/components/ChargingPileScanner.vue';
import { ref, inject, onMounted, onUnmounted, nextTick } from 'vue';
import { onShow } from '@dcloudio/uni-app';
import { useI18n } from 'vue-i18n';
import ToastContainer from '@/components/common/ToastContainer.vue';
import { getUserInfo, getUserChargingPileInfo, stopCharging } from '@/api';
import { useUserStore } from '@/store/user';
import { useModel3DStore } from '@/store/model3d';
import { useNetworkStore } from '@/store/network';
import UpdateDialog from '../../components/common/UpdateDialog.vue'
import { useVersionUpdate } from '../../composables/useVersionUpdate'
import GlobalHUD from '@/components/common/GlobalHUD.vue'

import { useGlobalHud } from '@/composables/useHud'
import chargingStatusManager from '@/utils/chargingStatusManager.js'
import env from '@/config/env'
import ChargingSettlementModal from '@/components/common/ChargingSettlementModal.vue'
// import networkDebug from '@/pages/network-test-new/network-debug.js'

console.log('Home页面: 导入ChargingPile3D组件');

// 使用国际化
// HUD
const hud = useGlobalHud()

const { t } = useI18n();

// 注入Toast实例
const toast = inject('toast');

// 使用用户状态store
const userStore = useUserStore();

// 使用3D模型store
const model3DStore = useModel3DStore();
console.log('Home页面: 初始化3D模型store');

// 使用网络状态store
const networkStore = useNetworkStore();

// 当前显示的模型类型
const currentModelType = ref('default');
console.log('Home页面: 设置currentModelType =', currentModelType.value);

// 3D组件引用
const chargingPile3DRef = ref(null);

// 3D模型加载完成回调
const onCharging3DLoaded = () => {
    console.log('Home页面: ChargingPile3D组件加载完成事件被触发');
};



// 扫描成功状态
const scanSuccess = ref(false);

// 充电状态
const isCharging = ref(false);
const chargingPercentage = ref(0);
const chargingTime = ref('0');
const showChargingLoading = ref(false);
const chargingStep = ref(0); // 0: 未开始, 1: 查询站点信息, 2: 创建订单, 3: 开始充电, 4: 充电完成

// 错误状态
const chargeError = ref(false);
const errorMessage = ref('');
const isPendingPayment = ref(false);
const pendingOrderNumber = ref('');

// 用户余额
const userBalance = ref('0.00');

// 当前充电站信息
const currentStationInfo = ref(null);

// 支付错误状态
const paymentError = ref(false);

// 自定义模态框状态
const showCustomModal = ref(false);

// 待支付订单状态
const hasPendingPayment = ref(false);
const pendingAmount = ref('0.00');

// 使用版本更新功能
const {
  updateDialogVisible,
  updateInfo,
  checkVersionUpdate,
  closeUpdateDialog,
  performUpdate
} = useVersionUpdate()

onMounted(() => {
  console.log('Home页面: onMounted被调用');

  // 确保Toast状态是干净的（防止退出登录后状态残留）
  if (toast && toast.clearToastState) {
      // 检查是否有残留的弹窗状态
      if (toast.toastState.visible) {
          console.log('检测到残留的Toast状态，正在清理...');
          toast.clearToastState();
      }
  }

  // 获取充电桩信息
  getInfo()

  // 设置WebSocket监听，实时更新充电状态
  setupChargingStatusListener()

  // 页面加载时自动检测版本更新
  checkVersionUpdate(false)

  // 检查网络状态（调试用）
  console.log('🌐 [首页] 当前网络状态:', {
    isConnected: networkStore.isConnected,
    networkType: networkStore.networkType
  })
  
  // 添加额外的网络状态监听
  console.log('🌐 [首页] 设置额外的网络状态监听')
  
  // 不再使用轮询检测，完全依赖request.ts的拦截器
  console.log('🌐 [首页] 网络状态检测完全依赖request.ts拦截器，不启动轮询')
})

// 页面显示时调用接口
onShow(() => {
  console.log('🏠 [首页] onShow被调用 - 路由发生变化，刷新页面数据');

  // 每次进入页面时调用接口获取最新数据
  getInfo()

  // 🔥 重新设置WebSocket监听器（防止页面切换后监听器丢失）
  setupChargingStatusListener()
  
  // 🚨 暂时禁用自动重连检查，避免死循环
  console.log('🔍 [首页] 已禁用自动重连检查，避免重连死循环')

  })

// 页面卸载时清理WebSocket监听器
onUnmounted(() => {
    console.log('🏠 [首页] 页面卸载，清理WebSocket监听器')
    // 移除事件监听器，避免内存泄漏
    chargingStatusManager.off('statusUpdate', statusUpdateHandler)
    chargingStatusManager.off('connectionStatusChanged', connectionStatusHandler)
    chargingStatusManager.off('chargingEnded', chargingEndedHandler)

    // 💪 不再停止监控，保持WebSocket连接活跃
    // 这样即使切换页面，也能继续接收充电结束等重要消息
    console.log('🏠 [首页] 页面卸载，但保持WebSocket连接和监控')
    
    // 停止连接健康检查（避免内存泄漏）
    stopConnectionHealthCheck()
})

// 获取充电桩信息
const getInfo = async () => {
    try {
        // 显示加载提示
        uni.showLoading({
            title: t('common.loading'),
            mask: true
        });

        // 从存储中获取用户信息
        const userInfoStr = uni.getStorageSync('userInfo');
        let openId = '';

        if (userInfoStr) {
            try {
                const userInfoObj = JSON.parse(userInfoStr);
                // 获取openId，注意大小写
                openId = userInfoObj.openId || userInfoObj.openid || '';
                console.log('从存储获取的openId:', openId);
            } catch (e) {
                console.error('解析用户信息失败:', e);
            }
        }

        if (!openId) {
            console.warn('未找到openId，无法获取用户信息');
            uni.hideLoading();
            return;
        }

        // 调用获取用户信息接口，传入openId参数
        const result = await getUserInfo({ openId });
        console.log('1111111111111111111111111111111111111111111111111111111111');
        
        // 检查响应数据
        if (result.code == 200 && result.data) {
            // 更新用户信息到store
            userStore.setUserInfo(result.data);
            let { regularRechargeAccount, accountBalance, isCharging: userIsCharging, isUnsettled } = result.data

            // 处理充电状态
            if (userIsCharging) {
                getUserChargingPileInfoList()
            }else{
                console.log('22222222222222222222222',userIsCharging);
                
                isCharging.value = userIsCharging
                console.log(' isCharging.value', isCharging.value);
            }
            // 处理待支付订单状态
            if (isUnsettled === true) {
                console.log('用户有待支付订单，显示提示');
                // hasPendingPayment.value = true;
                // 这里可以根据实际需要设置订单号和金额，暂时使用默认值
                // pendingOrderNumber.value = result.data.pendingOrderNumber || 'Unknown';
                // pendingAmount.value = result.data.pendingAmount || '0.00';
            } else {
                console.log('用户没有待支付订单，隐藏提示');
                hasPendingPayment.value = false;
            }

            // 更新余额显示
            if (regularRechargeAccount) {
                userBalance.value = regularRechargeAccount;
            } else if (accountBalance) {
                userBalance.value = accountBalance;
            }

        } else {
            console.warn('获取用户信息响应格式不符合预期:', result);
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
        // 不要显示错误提示，避免影响用户体验
        // toast.error(t('error.networkError'));
    } finally {
        // 隐藏加载提示
        uni.hideLoading();
    }
}
const orderNum = ref('')
// 充电结算弹窗状态
const showSettlementLoading = ref(false)
const settlementMessage = ref('Calcul de la facture en cours, veuillez patienter...')

// 💪 WebSocket连接健康检查定时器
let connectionHealthCheckTimer = null

//获取用户充电桩信息
const getUserChargingPileInfoList = async () => {
    try {
        const result = await getUserChargingPileInfo()
        if (result.code == 200 && result.data) {
            console.log('🏠 [首页] 获取用户充电桩信息成功:', result.data);
            // 根据接口返回的数据更新充电状态
            // 更新充电状态
            isCharging.value = true;
            orderNum.value = result.data.orderNum

            // 更新充电百分比（从接口获取 currentBatteryLevel）
            if (result.data.currentBatteryLevel !== undefined && result.data.currentBatteryLevel !== null) {
                chargingPercentage.value = result.data.currentBatteryLevel;
                console.log('🏠 [首页] 更新充电百分比:', result.data.currentBatteryLevel);
            }

            // 更新充电时间（使用alreadyFillTime字段）
            if (result.data.alreadyFillTime) {
                chargingTime.value = result.data.alreadyFillTime;
                console.log('🏠 [首页] 更新充电时间:', result.data.alreadyFillTime);
            }

            // 🔥 关键修复：启动充电状态管理器监控，接收实时数据推送
            console.log('🏠 [首页] 🔥 启动充电状态管理器监控，订单号:', result.data.orderNum);
            chargingStatusManager.startMonitoring(result.data.orderNum, {
                orderNumber: result.data.orderNum,
                // 使用项目配置的WebSocket URL
                websocketUrl: env.WS_URL
            });

        } else {
            console.warn('🏠 [首页] 获取用户充电桩信息失败:', result);
        }
    } catch (error) {
        console.error('🏠 [首页] 调用充电桩信息接口失败:', error);
    }
}

// 处理扫码组件的扫描成功事件
const handleScannerSuccess = (data) => {
    console.log('扫码成功:', data);
    scanSuccess.value = true;
};

// 处理充电测试事件
const handleChargingTest = (data) => {
    console.log('=== 收到charging-test事件 ===');
    console.log('测试数据:', data);
};

// 处理模型类型变更事件
const handleModelTypeChange = (data) => {
    console.log('=== 收到model-type-change事件 ===');
    console.log('模型类型变更数据:', data);

    const { power, modelType } = data;
    console.log(`根据power值${power}切换到模型类型: ${modelType}`);

    // 更新当前模型类型
    currentModelType.value = modelType;

    // 使用Pinia store进行全局状态管理
    model3DStore.switchModelType(modelType);

    // 直接通过组件ref调用切换方法
    if (chargingPile3DRef.value && chargingPile3DRef.value.reloadModel) {
        console.log('✅ 通过ref调用组件的reloadModel方法');
        chargingPile3DRef.value.reloadModel(modelType);
    } else {
        console.log('❌ 组件ref不可用或没有reloadModel方法');
    }
};

// 处理扫码组件的开始充电事件
const handleChargingStart = (data) => {
    console.log('=== 收到charging-start事件 ===');
    console.log('事件数据:', data);
    const { pileInfo, orderInfo, orderNumber } = data;
    console.log('解析出的数据 - orderNumber:', orderNumber, 'orderInfo:', orderInfo, 'pileInfo:', pileInfo);

    // 更新充电状态
    isCharging.value = true;

    // 设置步骤为充电中
    chargingStep.value = 3;

    // 保存订单信息到本地存储，以便其他页面使用
    if (orderInfo) {
        uni.setStorageSync('currentChargingOrder', JSON.stringify(orderInfo));
    }

    // 延迟500毫秒再跳转，让用户看到成功提示
    console.log('设置500ms延迟跳转定时器');
    setTimeout(() => {
        console.log('=== 开始执行跳转逻辑 ===');

        // 在跳转前关闭弹窗
        console.log('跳转前关闭弹窗');
        chargingStep.value = 0; // 重置步骤，这会关闭弹窗

        // 跳转到充电状态页面，传递订单号和订单ID
        let url = '/pages/charging-status/index';
        const params = [];

        if (orderNumber) {
            console.log('添加orderNumber参数:', orderNumber);
            params.push(`orderNumber=${encodeURIComponent(orderNumber)}`);
        }

        if (orderInfo && orderInfo.orderId) {
            console.log('添加orderId参数:', orderInfo.orderId);
            params.push(`orderId=${encodeURIComponent(orderInfo.orderId)}`);
        }

        if (pileInfo && pileInfo.id) {
            console.log('添加pileId参数:', pileInfo.id);
            params.push(`pileId=${encodeURIComponent(pileInfo.id)}`);
        }

        if (pileInfo && pileInfo.name) {
            console.log('添加pileName参数:', pileInfo.name);
            params.push(`pileName=${encodeURIComponent(pileInfo.name)}`);
        }

        if (pileInfo && pileInfo.location) {
            console.log('添加location参数:', pileInfo.location);
            params.push(`location=${encodeURIComponent(pileInfo.location)}`);
        }

        if (pileInfo && pileInfo.plotId) {
            console.log('添加plotId参数:', pileInfo.plotId);
            params.push(`plotId=${encodeURIComponent(pileInfo.plotId)}`);
        }

        if (params.length > 0) {
            url += '?' + params.join('&');
        }

        console.log('=== 准备跳转到充电状态页面 ===');
        console.log('跳转URL:', url);

        // 稍微延迟一下跳转，确保弹窗关闭动画完成
        setTimeout(() => {
            uni.navigateTo({
                url,
                success: () => {
                    console.log('跳转成功');
                },
                fail: (error) => {
                    console.error('跳转失败:', error);
                }
            });
        }, 100);
    }, 500);
};

// 处理扫码组件的扫描失败事件
const handleScannerFail = (data) => {
    console.log('Scan failed:', data);
    scanSuccess.value = false;
};

// 处理扫码组件的充电错误事件
const handleChargingError = (data) => {
    console.log('Charging error:', data);

    // 检查是否是待支付订单错误
    if (data.isPendingPayment && data.orderNumber) {
        console.log('=== 检测到待支付订单错误，设置支付状态 ===');
        isPendingPayment.value = true;
        pendingOrderNumber.value = data.orderNumber;
    } else {
        isPendingPayment.value = false;
        pendingOrderNumber.value = '';
    }

    // 默认设置错误状态，保持弹窗打开，让用户手动关闭
    chargeError.value = true;
    errorMessage.value = data.errorMessage || t('charging.failed');
    scanSuccess.value = false;

    // 不重置chargingStep，保持弹窗打开
    console.log('Charging failed, keep popup open, waiting for user to close manually:', errorMessage.value);
    console.log('是否为待支付订单:', isPendingPayment.value, '订单号:', pendingOrderNumber.value);
};

// 处理扫码组件的loading-change事件
const handleLoadingChange = (loading) => {
    console.log('Scan component loading state change:', loading);
    showChargingLoading.value = loading;
};

// 处理扫码组件的step-change事件
const handleStepChange = (step) => {
    console.log('=== 扫码组件步骤变化 ===');
    console.log('新步骤:', step);
    console.log('当前isCharging状态:', isCharging.value);
    chargingStep.value = step;

    // 根据步骤更新UI状态
    switch (step) {
        case 0: // 未开始
            isCharging.value = false;
            showChargingLoading.value = false;
            scanSuccess.value = false;
            break;

        case 1: // 查询站点信息
            isCharging.value = false;
            showChargingLoading.value = true;
            break;

        case 2: // 创建订单
            isCharging.value = false;
            showChargingLoading.value = true;
            break;

        case 3: // 开始充电
            isCharging.value = true;
            showChargingLoading.value = false;
            // 模拟充电百分比和时间
            chargingPercentage.value = 0;
            chargingTime.value = '0';
            // 不在这里显示toast，避免干扰弹窗流程
            // toast.success('充电已开始');
            break;

        case 4: // 充电完成
            isCharging.value = true;
            showChargingLoading.value = false;
            chargingPercentage.value = 0;
            chargingTime.value = '0';
            // 不在这里显示toast，避免干扰弹窗流程
            // toast.success('充电已完成');
            break;
    }
};

// 停止充电
const handleStopCharging = () => {
    console.log('点击停止充电');
    if(!isCharging.value) return
    // 使用Toast确认对话框
    toast.confirm({
        title: t('common.confirm'),
        message: t('charging.stopChargingConfirm'),
        confirmText: t('common.yes'),
        cancelText: t('common.no'),
        onConfirm: async () => {
            try {
                // HUD加载
                hud.loading(t('charging.stopping') || 'Arrêt de la charge...')

                // 执行停止充电API调用
                const result = await stopCharging(orderNum.value)

                // 结束HUD loading
                hud.done()

                if (result.code == 200) {
                    // 重置所有状态到初始状态
                    isCharging.value = false;
                    scanSuccess.value = false;
                    chargingStep.value = 0; // 重置步骤

                    // 使用HUD提示成功
                    hud.success(t('toast.chargingStoppedSuccess') || 'Charge arrêtée')

                    // 延迟跳转到订单支付详情页面
                    setTimeout(() => {
                        try {
                            // 跳转到订单支付详情页面
                            const url = `/pages/payment/expense-settlement/index?orderNumber=${encodeURIComponent(orderNum.value)}`
                            console.log('跳转到订单支付详情页面:', url)

                            uni.navigateTo({
                                url,
                                success: () => {
                                    console.log('跳转订单支付详情页面成功')
                                },
                                fail: (error) => {
                                    console.error('跳转订单支付详情页面失败:', error)
                                    // 如果跳转失败，刷新首页数据
                                    refreshHomeData()
                                }
                            })
                        } catch (error) {
                            console.error('停止充电后处理失败:', error)
                            // 出现异常时刷新首页数据
                            refreshHomeData()
                        }
                    }, 1000)
                } else {
                    // API调用失败
                    hud.error(result.msg || t('toast.stopChargingFailed') || 'Échec de l\'arrêt de la charge')
                }
            } catch (error) {
                // 结束HUD loading
                hud.done()

                console.error('停止充电异常:', error)
                hud.error(t('toast.stopChargingFailed') || 'Échec de l\'arrêt de la charge')
            }
        },
        onCancel: () => {
            // 用户取消，不做任何操作
            console.log('用户取消停止充电');
        }
    });
};

// 刷新首页数据
const refreshHomeData = async () => {
    try {
        console.log('开始刷新首页数据...')

        // 显示加载提示
        uni.showLoading({
                            title: t('common.loading') || 'Chargement...',
            mask: true
        })

        // 获取openId
        const userInfoStr = uni.getStorageSync('userInfo');
        let openId = '';

        if (userInfoStr) {
            try {
                const userInfoObj = JSON.parse(userInfoStr);
                openId = userInfoObj.openId || userInfoObj.openid || '';
            } catch (e) {
                console.error('解析用户信息失败:', e);
            }
        }

        if (!openId) {
            console.warn('未找到openId，无法刷新用户信息');
            uni.hideLoading();
            return;
        }

        // 刷新用户信息
        await getUserInfo({ openId })
        console.log('用户信息刷新完成')

        // 如果用户正在充电，刷新充电桩信息
        if (isCharging.value) {
            await getUserChargingPileInfoList()
            console.log('充电桩信息刷新完成')
        }

        // 隐藏加载提示
        uni.hideLoading()

        console.log('首页数据刷新完成')
    } catch (error) {
        // 隐藏加载提示
        uni.hideLoading()

        console.error('刷新首页数据失败:', error)
        uni.showToast({
                            title: t('toast.refreshFailed') || 'Échec de l\'actualisation',
            icon: 'error',
            duration: 2000
        })
    }
}

// 切换模型类型
const toggleModel = () => {
    // 如果是充电状态就不能点击切换充电桩
    if (isCharging.value) return;

    // 在'default'和'arnio'之间切换
    const newType = currentModelType.value === 'default' ? 'arnio' : 'default';
    console.log('🔄 切换模型类型:', newType);

    // 更新本地状态
    currentModelType.value = newType;

    // 使用Pinia store进行全局状态管理
    model3DStore.switchModelType(newType);

    // 直接通过组件ref调用（最简单可靠的方式）
    if (chargingPile3DRef.value && chargingPile3DRef.value.reloadModel) {
        console.log('✅ 通过组件ref切换模型');
        chargingPile3DRef.value.reloadModel(newType);
    } else {
        console.log('❌ 组件ref不可用');
    }

    // 添加震动反馈
    if (uni.vibrateShort) {
        uni.vibrateShort({
            success: () => console.log('震动成功')
        });
    }
};

// 可以在这里添加需要的逻辑
const goToMap = () => {
    uni.navigateTo({
        url: '/pages/map/index'
    })
}

const goToChargingStatus = async () => {
    console.log('=== 点击百分比跳转到充电状态页面 ===')
    console.log('当前充电状态:', isCharging.value)
    console.log('当前订单号:', orderNum.value)

    // 如果当前没有在充电，先检查用户是否有正在进行的充电订单
    if (!isCharging.value || !orderNum.value) {
        console.log('当前没有充电状态或订单号为空，尝试获取用户充电信息...')
        try {
            // 获取openId
            const userInfoStr = uni.getStorageSync('userInfo');
            let openId = '';

            if (userInfoStr) {
                try {
                    const userInfoObj = JSON.parse(userInfoStr);
                    openId = userInfoObj.openId || userInfoObj.openid || '';
                    console.log('从存储获取的openId:', openId);
                } catch (e) {
                    console.error('解析用户信息失败:', e);
                }
            }

            if (!openId) {
                console.warn('未找到openId，无法获取用户信息');
                uni.showToast({
                    title: t('user.loginFailed') || 'Informations utilisateur anormales, veuillez vous reconnecter',
                    icon: 'error',
                    duration: 2000
                });
                return;
            }

            // 先获取用户信息，检查是否有正在进行的充电
            await getUserInfo({ openId })

            // 如果用户信息显示正在充电，获取充电桩信息
            if (isCharging.value) {
                await getUserChargingPileInfoList()
                console.log('重新获取后的订单号:', orderNum.value)
            }
        } catch (error) {
            console.error('获取用户充电信息失败:', error)
        }
    }

    // 如果仍然没有订单号，直接返回，不显示弹窗
    if (!orderNum.value) {
        console.log('用户当前没有正在进行的充电订单，不执行跳转')
        return
    }

    let url = '/pages/charging-status/index'
    const params = []

    // 传递订单号参数
    if (orderNum.value) {
        console.log('添加orderNumber参数:', orderNum.value)
        params.push(`orderNumber=${encodeURIComponent(orderNum.value)}`)
    }

    if (params.length > 0) {
        url += '?' + params.join('&')
    }

    console.log('跳转URL:', url)

    uni.navigateTo({
        url,
        success: () => {
            console.log('跳转充电状态页面成功')
        },
        fail: (error) => {
            console.error('跳转充电状态页面失败:', error)
        }
    })
}

// 修改goToAccount函数，改为goToMore并更新跳转路径
const goToMore = () => {
    uni.navigateTo({
        url: '/pages/more/index',
        animationType: 'slide-in-left',
        animationDuration: 300
    })
}

// 关闭自定义模态框
const closeCustomModal = () => {
    showCustomModal.value = false
}

// 跳转到网络调试页面
const goToDebugPage = () => {
    uni.navigateTo({
        url: '/pages/network-debug/index'
    })
}





// 关闭待支付提示
const closePendingAlert = () => {
    hasPendingPayment.value = false
}


// 跳转到待支付订单页面
const goToPendingPayment = () => {
    // 构建跳转URL - 改为跳转到order-detail页面并添加fromPayment参数
    let url = '/pages/payment/order-detail';

    // 如果有订单号，则传递订单号参数和fromPayment参数
    if (pendingOrderNumber.value) {
        url += `?orderNumber=${encodeURIComponent(pendingOrderNumber.value)}&fromPayment=true`;
    } else {
        url += '?fromPayment=true';
    }

    uni.navigateTo({
        url: url,
        success: () => {
            console.log('跳转到待支付订单页面成功')
            // 跳转成功后隐藏提示
            hasPendingPayment.value = false
        },
        fail: (error) => {
            console.error('跳转到待支付订单页面失败:', error)
            // 如果跳转失败，可以尝试跳转到订单列表页面
            uni.navigateTo({
                url: '/pages/more/index',
                success: () => {
                    console.log('跳转到更多页面成功')
                    hasPendingPayment.value = false
                }
            })
        }
    })
}

// 获取弹窗标题
const getPopupTitle = () => {
    if (chargeError.value) {
        return t('popup.chargingFailed') || 'Charging Failed'
    }

    switch (chargingStep.value) {
        case 1:
            return t('popup.step1') || 'Scanning QR Code'
        case 2:
            return t('popup.step2') || 'Verifying Information'
        case 3:
            return t('popup.step3') || 'Processing Payment'
        case 4:
            return t('popup.chargingStarted') || 'Charging Started Successfully'
        default:
            return t('popup.startingCharging') || 'Starting Charging'
    }
}

// 获取弹窗副标题
const getPopupSubtitle = () => {
    switch (chargingStep.value) {
        case 1:
            return t('popup.connectingToPile') || 'Connecting to charging pile'
        case 2:
            return t('popup.preparingCharging') || 'Preparing charging session'
        case 3:
            return t('popup.processingPayment') || 'Processing payment'
        case 4:
            return t('popup.stepCompleted') || 'Completed'
        default:
            return ''
    }
}



// 获取进度条宽度
const getProgressWidth = () => {
    const progress = Math.min(chargingStep.value / 4 * 100, 100)
    return `${progress}%`
}

// 获取步骤样式类
const getStepClass = (stepNumber) => {
    const classes = []

    if (chargingStep.value > stepNumber) {
        classes.push('completed')
    } else if (chargingStep.value === stepNumber) {
        if (chargeError.value) {
            classes.push('error')
        } else {
            classes.push('current')
        }
    } else {
        classes.push('pending')
    }

    return classes.join(' ')
}

// 重试充电
const handleRetryCharging = () => {
    // 重置错误状态
    chargeError.value = false
    errorMessage.value = ''

    // 重新开始充电流程
    chargingStep.value = 1

    // 这里可以添加重新扫码或重新开始充电的逻辑
    console.log('重试充电')
}
// 去支付按钮点击事件
const handleGoToPay = () => {
    console.log('点击去支付按钮，订单号:', pendingOrderNumber.value);
    
    if (pendingOrderNumber.value) {
        // 跳转到订单详情页
        uni.navigateTo({
            url: `/pages/payment/order-detail?orderId=${pendingOrderNumber.value}&fromPayment=true`,
            success: () => {
                console.log('成功跳转到订单详情页进行支付');
                // 重置状态
                chargeError.value = false;
                errorMessage.value = '';
                isPendingPayment.value = false;
                pendingOrderNumber.value = '';
                chargingStep.value = 0;
                showChargingLoading.value = false;
            },
            fail: (error) => {
                console.error('跳转到订单详情页失败:', error);
                uni.showToast({
                    title: '跳转失败，请稍后重试',
                    icon: 'none',
                    duration: 2000
                });
            }
        });
    } else {
        console.error('订单号为空，无法跳转');
        uni.showToast({
            title: '订单信息异常，请重试',
            icon: 'none',
            duration: 2000
        });
    }
};

// 取消充电
const handleCancelCharging = () => {
    console.log('点击取消充电');
    toast.confirm({
        title: t('common.confirmCancel'),
        message: t('charging.cancelChargingConfirm'),
        confirmText: t('common.confirm'),
        cancelText: t('common.back'),
        onConfirm: () => {
            // 重置所有状态
            isCharging.value = false;
            scanSuccess.value = false;
            chargingStep.value = 0;
            showChargingLoading.value = false;
            chargeError.value = false; // 重置错误状态
            errorMessage.value = ''; // 清空错误消息
            isPendingPayment.value = false; // 重置待支付状态
            pendingOrderNumber.value = ''; // 清空待支付订单号

            // 不显示取消成功提示，直接关闭
        },
        onCancel: () => {
            // 用户取消，不做任何操作
            console.log('用户取消操作');
        }
    });
};

// 定义监听器函数，避免重复添加
const statusUpdateHandler = (data) => {
    // 更新充电百分比
    let batteryLevel = null
    if (data.battery?.current !== undefined) {
        batteryLevel = parseInt(data.battery.current)
    } else if (data.battery?.filled !== undefined) {
        batteryLevel = parseInt(data.battery.filled)
    } else if (data.currentBatteryLevel !== undefined) {
        batteryLevel = parseInt(data.currentBatteryLevel)
    } else if (data.alreadyFillDegree !== undefined) {
        batteryLevel = parseInt(data.alreadyFillDegree)
    } else if (data.percentage !== undefined) {
        batteryLevel = parseInt(data.percentage)
    }

    if (batteryLevel !== null && batteryLevel >= 0) {
        chargingPercentage.value = batteryLevel
    }

    // 更新充电时间（使用转换后的time.elapsed字段）
    let timeInfo = data.time?.elapsed || data.alreadyFillTime

    if (timeInfo !== undefined && timeInfo !== null && timeInfo !== '') {
        chargingTime.value = String(timeInfo)
        console.log('🏠 [首页] ✅ 更新充电时间:', timeInfo)
    }

    // 确保充电状态为true
    if (!isCharging.value && batteryLevel > 0) {
        isCharging.value = true
    }
}

// 防止重复日志的计数器
let lastConnectionStatus = null
let connectionLogCount = 0
const MAX_CONNECTION_LOGS = 3

const connectionStatusHandler = (status) => {
    // 检查是否是重复的状态
    const statusKey = `${status.connected}-${status.type}`
    if (lastConnectionStatus === statusKey) {
        connectionLogCount++
        if (connectionLogCount > MAX_CONNECTION_LOGS) {
            // 超过最大日志数量，只在特殊情况下打印
            if (connectionLogCount === MAX_CONNECTION_LOGS + 1) {
                console.log('🔇 [首页] 连接状态重复，后续相同日志将被抑制...')
            }
            return
        }
    } else {
        // 状态发生变化，重置计数器
        lastConnectionStatus = statusKey
        connectionLogCount = 1
    }
    
    console.log('🔗 [首页] 连接状态变化:', status)
    
    // 根据连接状态更新UI提示
    if (status.connected) {
        console.log('✅ [首页] WebSocket连接已建立')
        // 连接成功时重置健康检查计数器
        healthCheckAttempts = 0
        // 可以在这里添加连接成功的提示
    } else {
        console.log('❌ [首页] WebSocket连接断开:', status.type)
        
        // 根据断开类型显示不同提示
        switch (status.type) {
            case 'connecting':
                console.log('🔄 [首页] 正在连接中...')
                break
            case 'reconnecting':
                console.log('🔄 [首页] 正在重连中...', status.attempt ? `第${status.attempt}次` : '')
                break
            case 'disconnected':
                console.log('⚠️ [首页] 连接已断开')
                break
            case 'failed':
                console.log('❌ [首页] 连接失败')
                break
        }
    }
}

// 充电结束事件处理
const chargingEndedHandler = (data) => {
    console.log('🏠 [首页] 收到充电结束事件:', data)
    
    // 🚨 强制隐藏任何可能显示的HUD提示
    hud.hide()
    // 额外确保HUD完全隐藏
    setTimeout(() => hud.hide(), 100)
    setTimeout(() => hud.hide(), 500)
    
    // 1. 显示结算loading弹窗
    showSettlementLoading.value = true
    
    // 2. 3秒后更新弹窗消息为socket收到的消息
    setTimeout(() => {
        settlementMessage.value = data.message
    }, 3000)
    
    // 3. 再等2秒后关闭弹窗（不跳转页面）
    setTimeout(() => {
        showSettlementLoading.value = false
    }, 5000)
}

// 💪 启动WebSocket连接健康检查
let lastHealthCheckTime = 0
let healthCheckAttempts = 0
const MAX_HEALTH_CHECK_ATTEMPTS = 2

const startConnectionHealthCheck = () => {
    // 清除之前的定时器
    if (connectionHealthCheckTimer) {
        clearInterval(connectionHealthCheckTimer)
    }
    
    // 智能健康检查：只在合适的时候尝试重连
    connectionHealthCheckTimer = setInterval(() => {
        const now = Date.now()
        const connectionStatus = chargingStatusManager.getConnectionStatus()
        
        // 只在有订单且监控中但连接断开时才考虑重连
        if (orderNum.value && connectionStatus.isMonitoring && !connectionStatus.websocket.isConnected) {
            
            // 防止频繁重连：每次重连至少间隔5分钟
            if (now - lastHealthCheckTime < 5 * 60 * 1000) {
                return
            }
            
            // 限制健康检查的重连次数
            if (healthCheckAttempts >= MAX_HEALTH_CHECK_ATTEMPTS) {
                console.log('🏥 [健康检查] 已达到最大重连尝试次数，停止自动重连')
                stopConnectionHealthCheck()
                return
            }
            
            console.log('🏥 [健康检查] 长时间断线，尝试重新建立连接')
            healthCheckAttempts++
            lastHealthCheckTime = now
            
            chargingStatusManager.startMonitoring(orderNum.value, {
                orderNumber: orderNum.value,
                websocketUrl: env.WS_URL
            })
        }
    }, 5 * 60 * 1000) // 每5分钟检查一次
    
    console.log('🏥 [健康检查] 已启动智能健康检查（每5分钟）')
}

// 停止WebSocket连接健康检查
const stopConnectionHealthCheck = () => {
    if (connectionHealthCheckTimer) {
        clearInterval(connectionHealthCheckTimer)
        connectionHealthCheckTimer = null
        console.log('🏥 [健康检查] 已停止连接健康检查')
    }
}

// 设置WebSocket监听，实时更新充电状态
const setupChargingStatusListener = () => {

    // 先移除旧的监听器，避免重复添加
    chargingStatusManager.off('statusUpdate', statusUpdateHandler)
    chargingStatusManager.off('connectionStatusChanged', connectionStatusHandler)
    chargingStatusManager.off('chargingEnded', chargingEndedHandler)

    // 添加新的监听器
    chargingStatusManager.on('statusUpdate', statusUpdateHandler)
    chargingStatusManager.on('connectionStatusChanged', connectionStatusHandler)
    chargingStatusManager.on('chargingEnded', chargingEndedHandler)
    
    // 💪 启动连接健康检查
    startConnectionHealthCheck()
}






</script>

<style lang="less">
.home-container {
    height: 100vh;
    background-color: #fff;
    position: relative;

    .scroll-content {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        background-color: #fff;
    }

    .status-bar {
        padding: 88rpx 32rpx 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        position: relative;
        z-index: 10;

        .account-link {
            position: absolute;
            left: 0;
            top: -60rpx;
            font-size: 32rpx;
            color: #222;
            font-weight: bold;
            z-index: 20;
            padding: 0 16rpx;
        }

        .wallet {
            display: flex;
            flex-direction: column;
            gap: 8rpx;
            position: relative;
            padding: 4rpx 0;

            .label {
                font-size: 32rpx;
                color: #333;
                line-height: 1;
                font-weight: 600;
                letter-spacing: 0.5rpx;
                text-transform: capitalize;
                font-family: 'Helvetica Neue', Arial, sans-serif;
            }

            .amount-row {
                display: flex;
                align-items: center;
                gap: 8rpx;

                .amount {
                    font-size: 28rpx;
                    color: #1a1a1a;
                    font-weight: 600;
                    line-height: 1;
                    font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                }

                .iconfont {
                    font-size: 24rpx;
                    color: #666;
                    line-height: 1;
                    transition: transform 0.2s ease;
                    margin-top: 2rpx;
                }
            }

            &:active {
                .amount-row {
                    .amount {
                        color: #007AFF;
                    }

                    .iconfont {
                        transform: translateX(4rpx);
                        color: #007AFF;
                    }
                }
            }
        }

        .logo {
            width: 120rpx;
            height: 66rpx;
            margin-top: 4rpx;
            filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.05));
        }
    }

    .membership-swiper {
        width: 100%;
        height: 120rpx;
        margin: 10rpx 0;
        padding: 0 5rpx;
        box-sizing: border-box;
        border-radius: 16rpx;
    }

    .membership-card {
        margin: 0;
        position: relative;
        height: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        border-radius: 16rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

        .banner-image {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 20rpx;
        }

        .card-content {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 6rpx;
            margin-left: 180rpx;

            .title {
                font-size: 28rpx;
                color: #1a1a1a;
                font-weight: 600;
                line-height: 1.2;
                letter-spacing: 0.3rpx;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            }

            .subtitle {
                font-size: 24rpx;
                color: #666;
                line-height: 1.2;
                letter-spacing: 0.2rpx;
                font-family: 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
            }
        }

        .go-btn {
            position: absolute;
            right: 20rpx;
            top: 50%;
            transform: translateY(-50%);
            z-index: 2;
            background-color: #FFE4CC;
            color: #FF6B35;
            padding: 8rpx 28rpx;
            border-radius: 24rpx;
            font-size: 26rpx;
            font-weight: 600;
            box-shadow: 0 2rpx 6rpx rgba(255, 107, 53, 0.2);
            transition: all 0.2s ease;
            font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;

            &:active {
                transform: translateY(-50%) scale(0.95);
                background-color: #FFD6B3;
            }
        }
    }

    // 待支付订单提示样式
    .pending-payment-alert {
        margin: 16rpx 32rpx;
        animation: slideInDown 0.3s ease-out;

        .alert-close-wrapper {
            animation: slideInDown 0.4s ease-out 0.1s both;
        }

        .alert-container {
            background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 50%, #FFA726 100%);
            border-radius: 20rpx;
            padding: 24rpx;
            display: flex;
            align-items: center;
            gap: 16rpx;
            box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.3);
            position: relative;
            overflow: hidden;
            min-height: 100rpx;
            backdrop-filter: blur(10rpx);

            // 背景装饰
            &::before {
                content: '';
                position: absolute;
                top: -30rpx;
                right: -30rpx;
                width: 120rpx;
                height: 120rpx;
                background: rgba(255, 255, 255, 0.08);
                border-radius: 50%;
                transform: rotate(45deg);
            }

            &::after {
                content: '';
                position: absolute;
                bottom: -20rpx;
                left: -20rpx;
                width: 80rpx;
                height: 80rpx;
                background: rgba(255, 255, 255, 0.06);
                border-radius: 50%;
            }
        }

        .alert-icon {
            width: 48rpx;
            height: 48rpx;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .credit-card-icon {
                width: 32rpx;
                height: 24rpx;
                position: relative;

                .card-body {
                    width: 100%;
                    height: 100%;
                    background: #fff;
                    border-radius: 4rpx;
                    position: relative;
                    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
                }

                .card-stripe {
                    position: absolute;
                    top: 6rpx;
                    left: 0;
                    right: 0;
                    height: 4rpx;
                    background: #333;
                }

                .card-chip {
                    position: absolute;
                    bottom: 4rpx;
                    left: 4rpx;
                    width: 6rpx;
                    height: 4rpx;
                    background: #FFD700;
                    border-radius: 1rpx;
                }
            }
        }

        .alert-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8rpx;
            min-width: 0; // 防止内容溢出

            .alert-title {
                font-size: 30rpx;
                font-weight: 700;
                color: #fff;
                line-height: 1.2;
                font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
            }

            .alert-subtitle {
                display: flex;
                flex-direction: column;
                gap: 4rpx;

                .order-info,
                .amount-info {
                    font-size: 24rpx;
                    color: rgba(255, 255, 255, 0.95);
                    line-height: 1.3;
                    font-family: 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
                }

                .amount-info {
                    font-weight: 600;
                }
            }
        }

        .alert-actions {
            display: flex;
            align-items: center;
            flex-shrink: 0;

            .pay-btn {
                background: rgba(255, 255, 255, 0.95);
                color: #FF6B35;
                padding: 12rpx 24rpx;
                border-radius: 24rpx;
                transition: all 0.2s ease;
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
                min-width: 88rpx;
                display: flex;
                align-items: center;
                gap: 6rpx;
                backdrop-filter: blur(10rpx);

                .pay-text {
                    font-size: 26rpx;
                    font-weight: 600;
                    line-height: 1;
                    font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                }

                .pay-arrow {
                    font-size: 20rpx;
                    color: #FF6B35;
                    line-height: 1;
                    opacity: 0.8;
                }

                &:active {
                    transform: scale(0.95);
                    background: rgba(255, 255, 255, 1);

                    .pay-arrow {
                        transform: translateX(2rpx);
                    }
                }
            }
        }

        // 底部关闭按钮区域
        .alert-close-wrapper {
            display: flex;
            justify-content: center;
            margin-top: 16rpx;
            padding: 8rpx 0;

            .close-btn {
                width: 80rpx;
                height: 36rpx;
                background: rgba(255, 255, 255, 0.95);
                border-radius: 18rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.12);
                backdrop-filter: blur(15rpx);
                border: 1rpx solid rgba(255, 255, 255, 0.3);
                position: relative;

                // 添加一个微妙的内阴影
                &::before {
                    content: '';
                    position: absolute;
                    inset: 1rpx;
                    border-radius: 17rpx;
                    background: linear-gradient(145deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
                    z-index: -1;
                }

                .close-handle {
                    width: 36rpx;
                    height: 5rpx;
                    background: linear-gradient(90deg, #bbb, #999, #bbb);
                    border-radius: 3rpx;
                    transition: all 0.2s ease;
                    box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
                }

                &:active {
                    transform: scale(0.92) translateY(1rpx);
                    background: rgba(255, 255, 255, 1);
                    box-shadow: 0 3rpx 8rpx rgba(0, 0, 0, 0.15);

                    .close-handle {
                        background: linear-gradient(90deg, #999, #777, #999);
                        width: 32rpx;
                        height: 4rpx;
                    }
                }
            }
        }
    }

    .charger-status {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        box-sizing: border-box;
        padding: 60rpx 30rpx 0 0;
        box-sizing: border-box;
        z-index: 99;

        .status-tag {
            display: inline-block;
            background-color: #E0E0E0;
            color: #333333;
            padding: 6rpx 20rpx;
            border-radius: 0;
            font-size: 22rpx;
            font-weight: normal;
            border: none;
            border-left: 6rpx solid #333333;
            transition: all 0.3s ease;

            // 充电状态样式
            &.charging {
                background-color: #FFE4E1;
                color: #FF6B6B;
                border-left-color: #FF6B6B;
                cursor: pointer;

                &:hover {
                    background-color: #FFD6D6;
                }

                &:active {
                    transform: scale(0.95);
                    background-color: #FFC8C8;
                }
            }
        }

        .test-btn {
            position: absolute;
            right: 20rpx;
            top: 50%;
            transform: translateY(-50%);
            z-index: 2;
            background-color: #FFE4CC;
            color: #FF6B35;
            padding: 8rpx 28rpx;
            border-radius: 24rpx;
            font-size: 26rpx;
            font-weight: 600;
            box-shadow: 0 2rpx 6rpx rgba(255, 107, 53, 0.2);
            transition: all 0.2s ease;
            font-family: 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;

            &:active {
                transform: translateY(-50%) scale(0.95);
                background-color: #FFD6B3;
            }
        }
    }

    .charger-section {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        flex: 1;
        padding: 0 0 50rpx;

        .status-icons {
            position: absolute;
            left: 50rpx;
            top: 45%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 50rpx;
            z-index: 100;
            padding: 28rpx 20rpx;
            border-radius: 18rpx;
            min-width: 100rpx;

            &.full-spacing {
                left: 60rpx; /* 电量100%时与充电桩拉开一点距离 */
            }

            // 充电状态显示
            .charging-status {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 12rpx;
                width: 100%;

                .percentage {
                    font-size: 52rpx;
                    font-weight: bold;
                    color: #1a1a1a;
                    line-height: 1;
                    text-align: center;
                    margin-bottom: 4rpx;
                    background-color: #f1f1f1;
                    padding: 20rpx;
                    border-radius: 18rpx;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &:hover {
                        background-color: #e8e8e8;
                        transform: scale(1.02);
                    }

                    &:active {
                        background-color: #ddd;
                        transform: scale(0.98);
                    }
                }

                .time {
                    font-size: 26rpx;
                    color: #000;
                    font-weight: 700;
                    text-align: center;
                    margin-bottom: 10rpx;
                }

                .charging-icon {
                    width: 100rpx;
                    height: 28rpx;
                }

                .unlock_icon {
                    margin-left: 50rpx;
                }
            }

            .icon-item {
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 1;
                background-color: transparent;
                transition: all 0.3s ease;
                cursor: pointer;

                transition: all 0.2s ease;

                &:nth-child(1) {
                    width: 48rpx;
                    height: 48rpx;

                    .icon {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }

                &:nth-child(2) {
                    margin-left: 0;
                    width: 100%;

                    .icon {
                        width: 120rpx;
                        height: 36rpx;
                    }
                }

                &:active {
                    transform: scale(0.95);
                }
            }
        }

        .charger-container {
            width: 800rpx;
            height: 600rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin-top: -40rpx;
            margin-bottom: -20rpx;
            background-color: transparent;
            overflow: visible;
            z-index: 1;

            .charger-3d {
                width: 100%;
                height: 100%;
                transition: all 0.3s ease;
                z-index: 10;
                background-color: transparent;
            }

            // 充电桩下方loading效果
            .charging-loading {
                position: absolute;
                bottom: 30rpx;
                left: 50%;
                transform: translateX(-50%);
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 16rpx;
                z-index: 11;

                .loading-spinner {
                    width: 48rpx;
                    height: 48rpx;
                    border: 4rpx solid rgba(0, 200, 81, 0.2);
                    border-top: 4rpx solid #00C851;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                .loading-text {
                    font-size: 28rpx;
                    color: #00C851;
                    font-weight: 500;
                    text-align: center;
                }
            }

            &:after {
                content: '';
                position: absolute;
                bottom: 30rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 200rpx;
                height: 20rpx;
                background: radial-gradient(ellipse at center, rgba(0, 0, 0, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
                border-radius: 50%;
                opacity: 0.7;
                z-index: 5;
            }
        }

        .lightning-icon {
            position: absolute;
            right: 130rpx;
            top: 20%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            z-index: 100;
            cursor: pointer;
            transition: all 0.3s ease;

            image {
                width: 48rpx;
                height: 48rpx;
            }

            &:active {
                transform: translateY(-50%) scale(0.9);
                opacity: 0.8;
            }

            // 禁用状态（充电时）
            &.disabled {
                opacity: 0.4;
                filter: grayscale(1);
                cursor: not-allowed;

                &:active {
                    transform: translateY(-50%);
                    opacity: 0.4;
                }
            }
        }
    }

    .map-container {
        position: relative;
        width: 100%;
        height: 290rpx;
        overflow: hidden;
        cursor: pointer;

        .map-image {
            width: 400rpx;
            height: 100%;
            object-fit: cover;
        }

        .location-tag {
            position: absolute;
            left: 32rpx;
            bottom: 32rpx;
            background-color: #007AFF;
            color: #fff;
            font-size: 26rpx;
            padding: 12rpx 24rpx;
            border-radius: 8rpx;
            font-weight: 500;
            box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
            text-transform: uppercase;
            letter-spacing: 1rpx;
        }
    }

    .charging-popup {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        z-index: 1000;
        box-sizing: border-box;
        height: 30vh;
        /* 稍微调整高度 */

        &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(5rpx);
            z-index: -1;
        }

        .popup-content {
            position: relative;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            border-radius: 24rpx 24rpx 0 0;
            padding: 24rpx 32rpx;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.1);
            overflow: hidden;

            .popup-header {
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24rpx;

                .header-title {
                    font-size: 36rpx;
                    font-weight: 600;
                    color: #FF8800;
                    flex: 1;
                    text-align: center;

                    &.error {
                        color: #FF6B6B;
                    }
                }

                .close-btn {
                    width: 48rpx;
                    height: 48rpx;
                    border-radius: 50%;
                    background-color: #f7f7f7;
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    .close-icon {
                        font-size: 32rpx;
                        color: #666;
                        line-height: 1;
                    }
                }
            }

            .charging-steps {
                flex: 1;
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 20rpx;

                .step-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20rpx 24rpx;
                    border-radius: 12rpx;
                    background-color: #f9f9f9;
                    transition: all 0.3s ease;

                    &.completed {
                        background-color: #f0f9f1;
                        border-left: 6rpx solid #00C851;

                        .step-text {
                            color: #00C851;
                            font-weight: 500;
                        }

                        .step-check {
                            color: #00C851;
                        }
                    }

                    &.current {
                        background-color: #fff8f0;
                        border-left: 6rpx solid #FF8800;

                        .step-text {
                            color: #FF8800;
                            font-weight: 500;
                        }

                        .step-loading {
                            border-top-color: #FF8800;
                        }
                    }

                    &.disabled {
                        opacity: 0.5;
                        background-color: #f2f2f2;
                        border-left: 6rpx solid #cccccc;

                        .step-text {
                            color: #999999;
                        }
                    }

                    &.error {
                        background-color: #fff0f0;
                        border-left: 6rpx solid #FF6B6B;

                        .step-text {
                            color: #FF6B6B;
                            font-weight: 500;
                        }

                        .step-loading {
                            border-top-color: #FF6B6B;
                        }
                    }

                    .step-text {
                        font-size: 30rpx;
                        color: #333;
                    }

                    .step-loading {
                        width: 24rpx;
                        height: 24rpx;
                        border: 3rpx solid rgba(255, 136, 0, 0.2);
                        border-top: 3rpx solid #FF8800;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                    }

                    .step-check {
                        font-size: 28rpx;
                        font-weight: bold;
                    }

                    .step-error {
                        font-size: 28rpx;
                        font-weight: bold;
                        color: #FF6B6B;
                    }
                }
            }

            // 错误信息显示区域
            .error-message {
                display: flex;
                align-items: center;
                gap: 10rpx;
                padding: 16rpx 24rpx;
                background-color: #fff0f0;
                border-radius: 12rpx;
                margin-top: 20rpx;
                margin-bottom: 20rpx;
                border: 1rpx solid #FF6B6B;

                .error-icon {
                    width: 48rpx;
                    height: 48rpx;
                    background-color: #FF6B6B;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 32rpx;
                    color: #fff;
                }

                .error-text {
                    font-size: 28rpx;
                    color: #FF6B6B;
                    font-weight: 500;
                }
            }
        }
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* 自定义美化模态框样式 */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.custom-modal {
    width: 600rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 32rpx;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
    overflow: hidden;
    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-header {
    padding: 60rpx 40rpx 40rpx;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
}

.modal-icon {
    width: 120rpx;
    height: 120rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30rpx;
    backdrop-filter: blur(10px);
    border: 2rpx solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
}

.modal-icon .icon-text {
    font-size: 60rpx;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.modal-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.modal-content {
    padding: 50rpx 40rpx;
    text-align: center;
}

.modal-message {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: #2c3e50;
    line-height: 1.5;
    margin-bottom: 20rpx;
}

.modal-submessage {
    display: block;
    font-size: 28rpx;
    color: #7f8c8d;
    line-height: 1.4;
}

.modal-footer {
    padding: 0 40rpx 50rpx;
}

.modal-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.modal-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.modal-button:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.modal-button:active::before {
    left: 100%;
}

.button-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50rpx);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 高级充电弹窗样式 - 使用App Logo */
.charging-popup-premium {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1500;
    display: flex;
    align-items: flex-end;
    animation: fadeIn 0.3s ease-out;
}

.popup-backdrop-premium {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(12rpx);
}

.popup-content-premium {
    position: relative;
    width: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafb 100%);
    border-radius: 32rpx 32rpx 0 0;
    padding: 0;
    box-shadow: 0 -20rpx 60rpx rgba(0, 0, 0, 0.15);
    animation: slideUpPremium 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
}

.popup-handle-premium {
    width: 80rpx;
    height: 8rpx;
    background: linear-gradient(90deg, #e0e0e0 0%, #f0f0f0 100%);
    border-radius: 4rpx;
    margin: 20rpx auto 0;
}

.status-icon-area-premium {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 50rpx 0 30rpx;
}

.status-icon-container-premium {
    position: relative;
    z-index: 2;
    width: 140rpx;
    height: 140rpx;
    border-radius: 50%;
    background: transparent;  /* 去掉蓝色背景 */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;  /* 去掉阴影 */
    transition: all 0.3s ease;

    &.error {
        background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%);
        box-shadow: 0 12rpx 40rpx rgba(255, 107, 107, 0.3);
    }

    &.success {
        background: linear-gradient(135deg, #51CF66 0%, #40C057 100%);
        box-shadow: 0 12rpx 40rpx rgba(81, 207, 102, 0.3);
    }
}

.app-logo-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.app-logo {
    width: 100rpx;  /* 增大logo尺寸 */
    height: 100rpx;
    border-radius: 20rpx;
    /* 添加轻微的阴影让logo更突出 */
    filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.15));
}

.status-icon-text {
    font-size: 60rpx;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);

    &.success {
        color: #ffffff;
    }
}

.loading-ring {
    position: absolute;
    top: -8rpx;
    left: -8rpx;
    width: 156rpx;
    height: 156rpx;
    border: 4rpx solid transparent;
    border-top: 4rpx solid #4A90E2;  /* 改为蓝色，更明显 */
    border-right: 4rpx solid rgba(74, 144, 226, 0.3);  /* 添加渐变效果 */
    border-radius: 50%;
    animation: spinRing 1.5s linear infinite;
}

.status-glow-premium {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 220rpx;
    height: 220rpx;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(74, 144, 226, 0.15) 0%, transparent 70%);
    animation: pulsePremium 2s ease-in-out infinite;

    &.error {
        background: radial-gradient(circle, rgba(255, 107, 107, 0.15) 0%, transparent 70%);
    }

    &.success {
        background: radial-gradient(circle, rgba(81, 207, 102, 0.15) 0%, transparent 70%);
    }
}

.popup-header-premium {
    text-align: center;
    padding: 0 40rpx 30rpx;
}

.header-title-premium {
    display: block;
    font-size: 40rpx;
    font-weight: 700;
    color: #2C3E50;
    margin-bottom: 12rpx;
    line-height: 1.3;

    &.error {
        color: #E74C3C;
    }
}

.header-subtitle-premium {
    display: block;
    font-size: 28rpx;
    color: #7F8C8D;
    line-height: 1.4;
}

.error-message-premium {
    display: block;
    font-size: 28rpx;
    color: #E74C3C;
    line-height: 1.4;
    margin-top: 12rpx;
    font-weight: 500;
}

.charging-steps-premium {
    padding: 0 40rpx 30rpx;
}

.progress-track-premium {
    width: 100%;
    height: 6rpx;
    background: rgba(0, 0, 0, 0.08);
    border-radius: 3rpx;
    margin-bottom: 40rpx;
    overflow: hidden;
}

.progress-fill-premium {
    height: 100%;
    background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
    border-radius: 3rpx;
    transition: width 0.5s ease;
}

.steps-container-premium {
    display: flex;
    flex-direction: column;
    gap: 30rpx;
}

.step-item-premium {
    display: flex;
    align-items: center;
    gap: 24rpx;

    &.pending {
        opacity: 0.5;
    }

    &.current {
        .step-icon-premium {
            background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
            box-shadow: 0 6rpx 20rpx rgba(74, 144, 226, 0.3);
        }
    }

    &.completed {
        .step-icon-premium {
            background: linear-gradient(135deg, #51CF66 0%, #40C057 100%);
            box-shadow: 0 6rpx 20rpx rgba(81, 207, 102, 0.3);
        }
    }

    &.error {
        .step-icon-premium {
            background: linear-gradient(135deg, #FF6B6B 0%, #EE5A52 100%);
            box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.3);
        }
    }
}

.step-icon-premium {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.step-number, .step-check {
    font-size: 24rpx;
    font-weight: bold;
    color: #ffffff;
}

.step-loading {
    width: 30rpx;
    height: 30rpx;
    border: 3rpx solid rgba(255, 255, 255, 0.3);
    border-top: 3rpx solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.step-content-premium {
    flex: 1;
}

.step-text-premium {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: #2C3E50;
    margin-bottom: 4rpx;
}

.step-desc-premium {
    display: block;
    font-size: 26rpx;
    color: #7F8C8D;
    line-height: 1.3;
}

.popup-footer-premium {
    padding: 30rpx 40rpx 50rpx;
}

.error-actions-premium {
    display: flex;
    justify-content: center;
    gap: 20rpx;
    margin-bottom: 30rpx;
}

.close-button-premium {
    width: 200rpx;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%);
    color: #ffffff;
    box-shadow: 0 6rpx 20rpx rgba(231, 76, 60, 0.3);
}

.payment-button-premium {
    width: 200rpx;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #3498DB 0%, #2980B9 100%);
    color: #ffffff;
    box-shadow: 0 6rpx 20rpx rgba(52, 152, 219, 0.3);
}

.close-button-premium:active,
.payment-button-premium:active {
    transform: scale(0.98);
}

.button-text-premium {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
}

.bottom-tips-premium {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 20rpx;
    background: rgba(74, 144, 226, 0.08);
    border-radius: 16rpx;
    border-left: 6rpx solid #4A90E2;
}

.tip-icon-premium {
    font-size: 32rpx;
}

.tip-text-premium {
    flex: 1;
    font-size: 26rpx;
    color: #4A90E2;
    line-height: 1.4;
}

.error-tips-premium {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 20rpx;
    background: rgba(231, 76, 60, 0.08);
    border-radius: 16rpx;
    border-left: 6rpx solid #E74C3C;
}

.error-icon-tip-premium {
    font-size: 32rpx;
}

.error-tip-text-premium {
    flex: 1;
    font-size: 26rpx;
    color: #E74C3C;
    line-height: 1.4;
    font-weight: 500;
}

/* 高级动画定义 */
@keyframes slideUpPremium {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes spinRing {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulsePremium {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.3;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}




/* 现代化模态框样式 */
.modern-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8rpx);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.modern-modal-container {
    width: 640rpx;
    background: #ffffff;
    border-radius: 24rpx;
    overflow: hidden;
    position: relative;
    animation: slideUp 0.3s ease-out;
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12);
    margin: 40rpx;
}

.modern-modal-icon {
    padding: 48rpx 40rpx 32rpx;
    text-align: center;
}

.icon-circle {
    width: 120rpx;
    height: 120rpx;
    background: #F8F9FA;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    border: 1rpx solid #E9ECEF;
}

.modal-icon-img {
    width: 64rpx;
    height: 64rpx;
}

.modern-modal-content {
    padding: 0 40rpx 32rpx;
    text-align: center;
}

.modern-modal-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #1D1D1F;
    line-height: 1.3;
    margin-bottom: 16rpx;
}

.modern-modal-message {
    display: block;
    font-size: 28rpx;
    color: #86868B;
    line-height: 1.5;
    margin-bottom: 32rpx;
}

.charging-steps-guide {
    padding: 0 40rpx 32rpx;
}

.step-guide-item {
    display: flex;
    align-items: center;
    gap: 24rpx;
    margin-bottom: 20rpx;
}

.step-guide-item:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 48rpx;
    height: 48rpx;
    background: #007AFF;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    font-weight: 600;
    color: #ffffff;
    flex-shrink: 0;
}

.step-text {
    font-size: 28rpx;
    color: #1D1D1F;
    font-weight: 500;
}

.modern-modal-footer {
    padding: 0 40rpx 40rpx;
}

.modern-modal-button {
    width: 100%;
    height: 88rpx;
    background: #007AFF;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.modern-modal-button:active {
    transform: scale(0.96);
    opacity: 0.8;
}

.modern-button-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #ffffff;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.3;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}


</style>
