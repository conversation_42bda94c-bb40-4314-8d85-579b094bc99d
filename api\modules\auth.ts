/**
 * 用户认证相关API接口
 */
import Request from '../../utils/request'

// 认证相关类型定义
export interface RegisterData {
  grantType: string
  tenantId: string
  password: string
  userType: string
  phone: string
  nickName: string
}

export interface LoginData {
  clientId: string
  grantType: string
  password: string
  rememberMe: boolean
  tenantId: string
  username: string
}

export interface SmsVerifyData {
  phone: string
  smsCode: string
}

export interface ResetPasswordData {
  accountPassword: string
  oldAccountPassword: string
}

export interface PaymentPasswordResetData {
  payPassword: string
  oldPayPassword: string
  operateType: number // 1: 记得密码, 2: 不记得密码
}

export interface PaymentPasswordVerifyData {
  payPassword: string
}

// 4位数密码快速登录相关类型
export interface QuickLoginData {
  phone: string
  pinCode: string
}

export interface QuickLoginResponse {
  code: number
  message: string
  data: {
    access_token: string
    refresh_token: string
    expires_in: number
    token_type: string
    user_info: {
      userId: string
      username: string
      nickname: string
      avatar?: string
      phonenumber: string
      email?: string
      sex?: number
      status: number
      createTime: string
    }
  }
}

export interface PaymentPasswordVerifyResponse {
  code: number
  message: string
  data: boolean
}

export interface LoginResponse {
  code: number
  message: string
  data: {
    access_token: string
    refresh_token: string
    expires_in: number
    token_type: string
    user_info: {
      userId: string
      username: string
      nickname: string
      avatar?: string
      phonenumber: string
      email?: string
      sex?: number
      status: number
      createTime: string
    }
  }
}

export interface LogoutResponse {
  code: number
  message: string
  data: boolean
}

export interface RefreshTokenResponse {
  code: number
  message: string
  data: {
    access_token: string
    refresh_token: string
    expires_in: number
    token_type: string
  }
}

export interface RegisterResponse {
  code: number
  message: string
  data?: any
}

export interface SmsVerifyResponse {
  code: number
  message: string
  data?: any
}

export interface ResetPasswordResponse {
  code: number
  message: string
  data?: any
}

// 常量配置
const AUTH_CONFIG = {
  CLIENT_ID: "10e2f22a9910c139363027f1ecbf3b6c",
  TENANT_ID: "000000"
} as const

/**
 * 注册账户接口
 * @param {RegisterData} registerData - 注册数据
 * @returns {Promise<RegisterResponse>} - 返回Promise对象，包含注册结果
 */
export function registerUser(registerData: RegisterData): Promise<RegisterResponse> {
  return Request.post('/auth/register', registerData)
}

/**
 * 账户密码登录接口
 * @param {LoginData} loginData - 登录数据
 * @returns {Promise<LoginResponse>} - 返回Promise对象，包含登录结果和token
 */
export function passwordLogin(loginData: LoginData): Promise<LoginResponse> {
  return Request.post('/auth/login', loginData)
}

/**
 * 验证码验证接口
 * @param {SmsVerifyData} verifyData - 验证码数据
 * @returns {Promise<SmsVerifyResponse>} - 返回Promise对象，包含验证结果
 */
export function verifySmsCode(verifyData: SmsVerifyData): Promise<SmsVerifyResponse> {
  return Request.post('/auth/verify/sms/code', verifyData)
}

/**
 * 忘记密码接口
 * @param {ResetPasswordData} resetData - 重置密码数据
 * @returns {Promise<ResetPasswordResponse>} - 返回Promise对象，包含重置结果
 */
export function resetPassword(resetData: ResetPasswordData): Promise<ResetPasswordResponse> {
  return Request.post('/account/user/edit/password', resetData)
}

/**
 * 支付密码重置接口
 * @param {PaymentPasswordResetData} resetData - 支付密码重置数据
 * @returns {Promise<ResetPasswordResponse>} - 返回Promise对象，包含重置结果
 */
export function resetPaymentPassword(resetData: PaymentPasswordResetData): Promise<ResetPasswordResponse> {
  return Request.post('/account/user/setting/payment/password', resetData)
}

/**
 * 登出接口
 * @returns {Promise<LogoutResponse>} - 返回Promise对象
 */
export function logout(): Promise<LogoutResponse> {
  return Request.post('/auth/logout')
}

/**
 * 验证支付密码接口
 * @param {PaymentPasswordVerifyData} verifyData - 支付密码验证数据
 * @returns {Promise<PaymentPasswordVerifyResponse>} - 返回Promise对象，包含验证结果
 */
export function verifyPaymentPassword(verifyData: PaymentPasswordVerifyData): Promise<PaymentPasswordVerifyResponse> {
  return Request.post('/account/user/verify/payment/password', verifyData)
}

/**
 * 刷新token接口
 * @param {string} refreshToken - 刷新token
 * @returns {Promise<RefreshTokenResponse>} - 返回Promise对象
 */
export function refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
  return Request.post('/auth/refresh', { refreshToken })
}

/**
 * 4位数密码快速登录接口
 * @param {QuickLoginData} quickLoginData - 快速登录数据
 * @returns {Promise<QuickLoginResponse>} - 返回Promise对象，包含登录结果和token
 */
export function quickLogin(quickLoginData: QuickLoginData): Promise<QuickLoginResponse> {
  return Request.post('/auth/quick-login', {
    clientId: AUTH_CONFIG.CLIENT_ID,
    grantType: "pinCode",
    phone: quickLoginData.phone,
    pinCode: quickLoginData.pinCode,
    tenantId: AUTH_CONFIG.TENANT_ID
  })
}
