<template>
    <view class="terms-of-use-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ $t('legal.termsOfUse') }}</text>
                </view>
            </view>
        </view>
        
        <!-- 使用条款内容 -->
        <view class="content-container">
            <view class="content-section">
                <view class="section-title">
                    <text>1. {{ $t('legal.acceptanceOfTerms') }}</text>
                </view>
                <view class="section-text">
                    <text>{{ $t('legal.acceptanceOfTermsText') }}</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>2. Use License</text>
                </view>
                <view class="section-text">
                    <text>Permission is granted to temporarily download one copy of the materials (information or software) on ARNIO's mobile application for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:</text>
                </view>
                <view class="section-list">
                    <text>• Modify or copy the materials;</text>
                    <text>• Use the materials for any commercial purpose, or for any public display (commercial or non-commercial);</text>
                    <text>• Attempt to decompile or reverse engineer any software contained on ARNIO's mobile application;</text>
                    <text>• Remove any copyright or other proprietary notations from the materials; or</text>
                    <text>• Transfer the materials to another person or "mirror" the materials on any other server.</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>3. Disclaimer</text>
                </view>
                <view class="section-text">
                    <text>The materials on ARNIO's mobile application are provided on an 'as is' basis. ARNIO makes no warranties, expressed or implied, and hereby disclaims and negates all other warranties including, without limitation, implied warranties or conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property or other violation of rights.</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>4. Limitations</text>
                </view>
                <view class="section-text">
                    <text>In no event shall ARNIO or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the materials on ARNIO's mobile application, even if ARNIO or a ARNIO authorized representative has been notified orally or in writing of the possibility of such damage.</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const statusBarHeight = ref(0)

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.terms-of-use-container {
    min-height: 100vh;
    background-color: #ffffff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.content-container {
    padding: 32rpx;
    
    .content-section {
        margin-bottom: 40rpx;
        
        .section-title {
            margin-bottom: 16rpx;
            
            text {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
            }
        }
        
        .section-text {
            margin-bottom: 16rpx;
            
            text {
                font-size: 28rpx;
                color: #666;
                line-height: 1.5;
            }
        }
        
        .section-list {
            text {
                display: block;
                font-size: 28rpx;
                color: #666;
                line-height: 1.5;
                margin-bottom: 16rpx;
                padding-left: 16rpx;
            }
        }
    }
}
</style> 