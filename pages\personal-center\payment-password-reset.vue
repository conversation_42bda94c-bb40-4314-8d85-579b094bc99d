<template>
    <view class="payment-password-reset-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ $t('payment.resetPaymentPassword') }}</text>
                </view>
            </view>
        </view>

        <!-- 选择验证方式 -->
        <view v-if="!selectedType" class="selection-container">
            <view class="selection-title">
                <text>{{ $t('payment.doYouRememberPaymentPassword') }}</text>
            </view>

            <!-- 手机号显示 -->
            <view v-if="phone" class="phone-info">
                <text class="phone-label">{{ $t('payment.account') }}: {{ displayAccount }}</text>
            </view>

            <!-- 支付密码提示 -->
            <view class="password-tip">
                <text class="tip-text">💡 {{ $t('payment.paymentPasswordLastFourDigits') }}</text>
            </view>

            <view class="selection-options">
                <view class="option-item" @click="selectType(1)">
                    <view class="option-icon">
                        <text class="iconfont icon-lock"></text>
                    </view>
                    <view class="option-content">
                        <text class="option-title">{{ $t('payment.iRemember') }}</text>
                        <text class="option-desc"></text>
                    </view>
                    <view class="option-arrow">
                        <text class="iconfont icon-arrow-right"></text>
                    </view>
                </view>

                <view class="option-item" @click="selectType(2)">
                    <view class="option-icon">
                        <text class="iconfont icon-message"></text>
                    </view>
                    <view class="option-content">
                        <text class="option-title">{{ $t('payment.iDontRemember') }}</text>
                        <text class="option-desc"></text>
                    </view>
                    <view class="option-arrow">
                        <text class="iconfont icon-arrow-right"></text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 记得支付密码的表单 -->
        <view v-if="selectedType === 1" class="reset-form">
            <view class="form-header">
                <text class="form-title">{{ $t('payment.resetWithCurrentPassword') }}</text>
                <text class="form-subtitle">{{ $t('payment.enterCurrentPaymentPassword') }}</text>
            </view>

            <!-- 当前支付密码 -->
            <view class="form-item">
                <text class="form-label">{{ $t('payment.currentPaymentPassword') }}</text>
                <view class="input-container">
                    <input
                        class="form-input"
                        :type="showCurrentPassword ? 'text' : 'password'"
                        v-model="currentPassword"
                        :placeholder="$t('payment.enterCurrent4DigitPassword')"
                        maxlength="4"
                        @input="validateCurrentPassword"
                    />
                    <text
                        class="iconfont eye-icon"
                        :class="showCurrentPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleCurrentPasswordVisibility"
                    ></text>
                </view>
                <text v-if="errors.currentPassword" class="error-text">{{ errors.currentPassword }}</text>
            </view>

            <!-- 新支付密码 -->
            <view class="form-item">
                <text class="form-label">{{ $t('payment.newPaymentPassword') }}</text>
                <view class="input-container">
                    <input
                        class="form-input"
                        :type="showNewPassword ? 'text' : 'password'"
                        v-model="newPassword"
                        :placeholder="$t('payment.enterNew4DigitPassword')"
                        maxlength="4"
                        @input="validateNewPassword"
                    />
                    <text
                        class="iconfont eye-icon"
                        :class="showNewPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleNewPasswordVisibility"
                    ></text>
                </view>
                <text v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</text>
                <text class="password-hint">{{ $t('payment.passwordHint') }}</text>
            </view>

            <!-- 确认新密码 -->
            <view class="form-item">
                <text class="form-label">{{ $t('payment.confirmNewPassword') }}</text>
                <view class="input-container">
                    <input
                        class="form-input"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        v-model="confirmPassword"
                        :placeholder="$t('payment.confirmNew4DigitPassword')"
                        maxlength="4"
                        @input="validateConfirmPassword"
                    />
                    <text
                        class="iconfont eye-icon"
                        :class="showConfirmPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleConfirmPasswordVisibility"
                    ></text>
                </view>
                <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
            </view>
        </view>

        <!-- 不记得支付密码的表单 -->
        <view v-if="selectedType === 2" class="reset-form">
            <view class="form-header">
                <text class="form-title">{{ $t('payment.resetWithVerificationCode') }}</text>
                <text class="form-subtitle">{{ $t('payment.sendCodeToPhone') }}</text>
            </view>

            <!-- 手机号码 -->
            <view class="form-item">
                <text class="form-label">{{ $t('user.phone') }}</text>
                <view class="phone-display">
                    <text class="phone-text">{{ phone ? maskedPhone : $t('common.notSet') }}</text>
                    <text class="phone-note">{{ $t('payment.sendVerificationCodeNote') }}</text>
                </view>
            </view>

            <!-- 验证码 -->
            <view class="form-item">
                <text class="form-label">{{ $t('user.verificationCode') }}</text>
                <view class="input-container">
                    <input
                        class="form-input"
                        type="number"
                        v-model="verificationCode"
                        :placeholder="$t('user.enterVerificationCode')"
                        maxlength="6"
                        @input="validateVerificationCode"
                    />
                    <view class="verification-btn" :class="{ disabled: countdown > 0 }" @click="sendVerificationCode">
                        <text>{{ countdown > 0 ? `${countdown}s` : $t('user.send') }}</text>
                    </view>
                </view>
                <text v-if="errors.verificationCode" class="error-text">{{ errors.verificationCode }}</text>
            </view>

            <!-- 新密码 -->
            <view class="form-item">
                <text class="form-label">{{ $t('payment.newPaymentPassword') }}</text>
                <view class="input-container">
                    <input
                        class="form-input"
                        :type="showNewPassword ? 'text' : 'password'"
                        v-model="newPassword"
                        :placeholder="$t('payment.enterNew4DigitPassword')"
                        maxlength="4"
                        @input="validateNewPassword"
                    />
                    <text
                        class="iconfont eye-icon"
                        :class="showNewPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleNewPasswordVisibility"
                    ></text>
                </view>
                <text class="password-hint">{{ $t('payment.passwordHint') }}</text>
                <text v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</text>
            </view>

            <!-- 确认新密码 -->
            <view class="form-item">
                <text class="form-label">{{ $t('payment.confirmNewPassword') }}</text>
                <view class="input-container">
                    <input
                        class="form-input"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        v-model="confirmPassword"
                        :placeholder="$t('payment.confirmNew4DigitPassword')"
                        maxlength="4"
                        @input="validateConfirmPassword"
                    />
                    <text
                        class="iconfont eye-icon"
                        :class="showConfirmPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
                        @click="toggleConfirmPasswordVisibility"
                    ></text>
                </view>
                <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
            </view>
        </view>

        <!-- 安全提示 -->
        <view v-if="selectedType" class="security-tips">
            <text class="tips-title">{{ $t('payment.securityTips') }}</text>
            <text class="tip-item">{{ $t('payment.securityTip1') }}</text>
            <text class="tip-item">{{ $t('payment.securityTip2') }}</text>
            <text class="tip-item">{{ $t('payment.securityTip3') }}</text>
        </view>

        <!-- 提交按钮 -->
        <view v-if="selectedType" class="submit-btn" :class="{ disabled: !isFormValid }" @click="resetPassword">
            <text>{{ $t('common.submit') }}</text>
        </view>

        <!-- 返回选择按钮 -->
        <view v-if="selectedType" class="back-selection-btn" @click="backToSelection">
            <text>{{ $t('payment.backToSelection') }}</text>
        </view>
        <!-- HUD 局部挂载，确保弹窗可见 -->
        <GlobalHUD />
    </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { sendSmsCode, resetPaymentPassword } from '@/api'
import GlobalHUD from '@/components/common/GlobalHUD.vue'
import { useGlobalHud } from '@/composables/useHud'
const hud = useGlobalHud()

import { useUserStore } from '@/store/user'

const { t: $t } = useI18n()
const statusBarHeight = ref(0)
const selectedType = ref(null) // 1: 记得密码, 2: 不记得密码
const phone = ref('')
const currentPassword = ref('')

// 获取用户store
const userStore = useUserStore()
const verificationCode = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const showCurrentPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)
const countdown = ref(0)
const countdownTimer = ref(null)

// 错误信息
const errors = ref({
    currentPassword: '',
    verificationCode: '',
    newPassword: '',
    confirmPassword: ''
})

const maskedPhone = computed(() => {
    if (!phone.value) return 'Not available'
    const phoneStr = phone.value.toString()
    if (phoneStr.length < 7) return phoneStr
    // 显示前3位和后4位，中间用****代替
    return phoneStr.substring(0, 3) + '****' + phoneStr.substring(phoneStr.length - 4)
})

// 显示账号格式：前9位+****+后4位
const displayAccount = computed(() => {
    if (!phone.value) return '*************0000'
    const phoneStr = phone.value.toString()
    if (phoneStr.length >= 13) {
        // 如果手机号有13位或更多，显示前9位+****+后4位
        const prefix = phoneStr.substring(0, 9)
        const suffix = phoneStr.substring(phoneStr.length - 4)
        return prefix + '****' + suffix
    } else if (phoneStr.length >= 9) {
        // 如果手机号9-12位，显示前9位+****+剩余位数补0到4位
        const prefix = phoneStr.substring(0, 9)
        const remaining = phoneStr.substring(9)
        const padding = '0'.repeat(4 - remaining.length)
        return prefix + '****' + remaining + padding
    } else {
        // 如果手机号少于9位，以225开头补0到9位+****+补0到4位
        const paddedPhone = phoneStr.padStart(9, '0')
        const prefix = '225' + paddedPhone.substring(3, 9)
        return prefix + '****' + '0000'
    }
})

const isFormValid = computed(() => {
    if (selectedType.value === 1) {
        // 记得密码模式
        return currentPassword.value &&
               newPassword.value &&
               confirmPassword.value &&
               !errors.value.currentPassword &&
               !errors.value.newPassword &&
               !errors.value.confirmPassword
    } else if (selectedType.value === 2) {
        // 不记得密码模式
    return phone.value &&
               verificationCode.value &&
               newPassword.value &&
               confirmPassword.value &&
               !errors.value.verificationCode &&
               !errors.value.newPassword &&
               !errors.value.confirmPassword
    }
    return false
})

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight

    // 从Pinia store获取用户信息
    const userInfo = userStore.getUserInfo
    if (userInfo) {
        // 优先从 user_info 中获取，然后是根级别
        const userInfoData = userInfo.user_info || userInfo
        phone.value = userInfoData.accountPhone || userInfoData.phonenumber || userInfoData.phone || ''
        console.log('从Pinia获取到用户手机号:', phone.value)
        console.log('完整用户信息:', userInfo)
        console.log('userInfoData:', userInfoData)
        console.log('accountPhone:', userInfoData.accountPhone)
        console.log('phonenumber:', userInfoData.phonenumber)
        console.log('phone:', userInfoData.phone)
    } else {
        // 如果Pinia中没有，尝试从本地存储获取
        try {
            const userInfoStr = uni.getStorageSync('userInfo')
            if (userInfoStr) {
                const userInfo = JSON.parse(userInfoStr)
                const userInfoData = userInfo.user_info || userInfo
                phone.value = userInfoData.accountPhone || userInfoData.phonenumber || userInfoData.phone || ''
                console.log('从本地存储获取到用户手机号:', phone.value)
            }
        } catch (error) {
            console.error('获取用户信息失败:', error)
        }
    }
})

onUnmounted(() => {
    if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
    }
})

const goBack = () => {
    if (selectedType.value) {
        backToSelection()
    } else {
    uni.navigateBack()
    }
}

const selectType = (type) => {
    selectedType.value = type
    // 清空表单数据
    currentPassword.value = ''
    verificationCode.value = ''
    newPassword.value = ''
    confirmPassword.value = ''
    // 清空错误信息
    Object.keys(errors.value).forEach(key => {
        errors.value[key] = ''
    })
}

const backToSelection = () => {
    selectedType.value = null
    // 清空表单数据
    currentPassword.value = ''
    verificationCode.value = ''
    newPassword.value = ''
    confirmPassword.value = ''
    // 清空错误信息
    Object.keys(errors.value).forEach(key => {
        errors.value[key] = ''
    })
    // 停止倒计时
    if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
        countdownTimer.value = null
        countdown.value = 0
    }
}

const toggleCurrentPasswordVisibility = () => {
    showCurrentPassword.value = !showCurrentPassword.value
}

const toggleNewPasswordVisibility = () => {
    showNewPassword.value = !showNewPassword.value
}

const toggleConfirmPasswordVisibility = () => {
    showConfirmPassword.value = !showConfirmPassword.value
}

// 表单验证
const validateCurrentPassword = () => {
    const value = currentPassword.value
    if (!value) {
        errors.value.currentPassword = 'Current password is required'
    } else if (!/^\d{4}$/.test(value)) {
        errors.value.currentPassword = 'Password must be exactly 4 digits'
    } else {
        errors.value.currentPassword = ''
    }
}

const validateVerificationCode = () => {
    const value = verificationCode.value
    if (!value) {
        errors.value.verificationCode = 'Verification code is required'
    } else if (!/^\d{6}$/.test(value)) {
        errors.value.verificationCode = 'Verification code must be 6 digits'
    } else {
        errors.value.verificationCode = ''
    }
}

const validateNewPassword = () => {
    const value = newPassword.value
    if (!value) {
        errors.value.newPassword = 'New password is required'
    } else if (!/^\d{4}$/.test(value)) {
        errors.value.newPassword = 'Password must be exactly 4 digits'
    } else {
        errors.value.newPassword = ''
    }
    // 同时验证确认密码
    if (confirmPassword.value) {
        validateConfirmPassword()
    }
}

const validateConfirmPassword = () => {
    const value = confirmPassword.value
    if (!value) {
        errors.value.confirmPassword = 'Please confirm your password'
    } else if (value !== newPassword.value) {
        errors.value.confirmPassword = 'Passwords do not match'
    } else {
        errors.value.confirmPassword = ''
    }
}

const startCountdown = () => {
    countdown.value = 60
    if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
    }
    countdownTimer.value = setInterval(() => {
        if (countdown.value > 0) {
        countdown.value--
        } else {
            clearInterval(countdownTimer.value)
            countdownTimer.value = null
        }
    }, 1000)
}

const sendVerificationCode = async () => {
    if (countdown.value > 0) return

    if (!phone.value) {
            uni.showToast({
            title: 'Please enter your phone number',
            icon: 'none'
            })
            return
        }

    try {
        hud.loading('Sending...')

        const response = await sendSmsCode(phone.value)

        hud.done()

        if (response && (response.code === 200 || response.code === "200")) {
            hud.success('Verification code sent')
            startCountdown()
        } else {
            throw new Error(response?.message || 'Failed to send verification code')
        }

    } catch (error) {
        hud.done()
        console.error('发送验证码失败:', error)

        let errorMessage = 'Failed to send verification code'
        if (error.response) {
            errorMessage = error.response.data?.message || errorMessage
        } else if (error.message) {
            errorMessage = error.message
        }

        hud.error(errorMessage)
    }
}

const resetPassword = async () => {
    // 根据选择的类型进行相应的验证
    if (selectedType.value === 1) {
        validateCurrentPassword()
        validateNewPassword()
        validateConfirmPassword()
    } else if (selectedType.value === 2) {
        validateVerificationCode()
        validateNewPassword()
        validateConfirmPassword()
    }

    if (!isFormValid.value) {
        uni.showToast({
            title: 'Please fill in all fields correctly',
            icon: 'none'
        })
        return
    }

    try {
    hud.loading('Resetting password...')

        // 根据选择的类型调用不同的API
        let response
        if (selectedType.value === 1) {
            // 记得密码模式：使用当前密码和新密码
            response = await resetPaymentPassword({
                payPassword: newPassword.value,
                oldPayPassword: currentPassword.value,
                operateType: 1
            })
        } else if (selectedType.value === 2) {
            // 不记得密码模式：使用验证码和新密码
            response = await resetPaymentPassword({
                payPassword: newPassword.value,
                oldPayPassword: '', // 不记得密码时不传旧密码
                operateType: 2
            })
        }

        hud.done()

        if (response && (response.code === 200 || response.code === "200")) {
            hud.success('Payment password reset successful')

    setTimeout(() => {
                uni.navigateBack()
            }, 2000)
        } else {
            throw new Error(response?.message || 'Failed to reset payment password')
        }

    } catch (error) {
        hud.done()
        console.error('重置支付密码失败:', error)

        let errorMessage = 'Failed to reset payment password'
        if (error.response) {
            errorMessage = error.response.data?.message || errorMessage
        } else if (error.message) {
            errorMessage = error.message
        }

        hud.error(errorMessage)
    }
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.payment-password-reset-container {
    min-height: 100vh;
    background-color: #f8f9fa;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-bar {
    background: transparent;
    width: 100%;
}

.header {
    background: transparent;
    width: 100%;

    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;

        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;

            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }

        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: 600;
            color: #333;
        }
    }
}

.selection-container {
    margin: 32rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 48rpx 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .selection-title {
        font-size: 36rpx;
        color: #333;
        font-weight: 700;
        margin-bottom: 24rpx;
        text-align: center;
        line-height: 1.5;
        word-wrap: break-word;
        word-break: break-word;
    }

    .phone-info {
        text-align: center;
        margin-bottom: 40rpx;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 16rpx;
        border: 2rpx solid #e9ecef;

        .phone-label {
            font-size: 28rpx;
            color: #333;
            font-weight: 600;
        }
    }

    .password-tip {
        text-align: center;
        margin-bottom: 40rpx;
        padding: 20rpx;
        background: #fff3cd;
        border-radius: 16rpx;
        border: 2rpx solid #ffeaa7;

        .tip-text {
            font-size: 26rpx;
            color: #856404;
            line-height: 1.5;
            word-wrap: break-word;
            word-break: break-word;
        }
    }

    .selection-options {
        display: flex;
        flex-direction: column;
        gap: 20rpx;
    }

    .option-item {
        display: flex;
        align-items: center;
        padding: 32rpx 24rpx;
        background: #fff;
        border-radius: 20rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 2rpx solid transparent;

        &:active {
            transform: translateY(2rpx);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
        }

        .option-icon {
            width: 96rpx;
            height: 96rpx;
            border-radius: 48rpx;
            background: #fff;
            border: 3rpx solid #ff6b6b;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 24rpx;
            box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.2);

            .iconfont {
                font-size: 48rpx;
                color: #ff6b6b;
            }
        }

        .option-content {
            flex: 1;
            margin-right: 24rpx;

            .option-title {
                font-size: 32rpx;
                color: #333;
                font-weight: 700;
                margin-bottom: 8rpx;
                line-height: 1.4;
                word-wrap: break-word;
                word-break: break-word;
            }

            .option-desc {
                font-size: 26rpx;
                color: #666;
                line-height: 1.4;
            }
        }

        .option-arrow {
            width: 80rpx;
            height: 80rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 40rpx;

            .iconfont {
                font-size: 36rpx;
                color: #999;
            }
        }
    }
}

.reset-form {
    margin: 32rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 48rpx 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .form-header {
        margin-bottom: 40rpx;
        text-align: center;

        .form-title {
            font-size: 36rpx;
            color: #333;
            font-weight: 700;
            margin-bottom: 12rpx;
            display: block;
        }

        .form-subtitle {
            font-size: 26rpx;
            color: #666;
            line-height: 1.4;
            display: block;
        }
    }

    .form-item {
        margin-bottom: 40rpx;

        .form-label {
            font-size: 30rpx;
            color: #333;
            font-weight: 600;
            margin-bottom: 16rpx;
            display: block;
        }

        .phone-display {
            background: #f8f9fa;
            border-radius: 16rpx;
            padding: 32rpx 24rpx;
            border: 2rpx solid #e9ecef;
            display: flex;
            flex-direction: column;
            gap: 12rpx;

            .phone-text {
                font-size: 36rpx;
                color: #333;
                font-weight: 700;
                letter-spacing: 3rpx;
                text-align: center;
            }

            .phone-note {
                font-size: 26rpx;
                color: #666;
                font-weight: 400;
                text-align: center;
            }


        }

        .input-container {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 16rpx;
            padding: 0 24rpx;
            border: 2rpx solid #e9ecef;
            transition: all 0.3s ease;

            &:focus-within {
                border-color: #667eea;
                box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
            }

            .form-input {
                flex: 1;
                height: 96rpx;
                font-size: 30rpx;
                background: transparent;
                border: none;
                color: #333;

                &::placeholder {
                    color: #adb5bd;
                }
            }

            .eye-icon {
                font-size: 36rpx;
                color: #adb5bd;
                padding: 20rpx;
                cursor: pointer;
                transition: color 0.3s ease;

                &.icon-mimayanjing {
                    color: #667eea;
                }

                &.icon-yanjingmima {
                    color: #adb5bd;
                }

                &:active {
                    transform: scale(0.95);
                }
            }

            .verification-btn {
                padding: 20rpx 32rpx;
                background: #ff6b6b;
                border-radius: 12rpx;
                margin-left: 16rpx;
                box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
                transition: all 0.3s ease;

                text {
                    font-size: 26rpx;
                    color: #fff;
                    font-weight: 600;
                }

                &:active {
                    transform: translateY(2rpx);
                    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
                }

                &.disabled {
                    background: #e9ecef;
                    box-shadow: none;

                    text {
                        color: #adb5bd;
                    }
                }
            }
        }

        .error-text {
            color: #dc3545;
            font-size: 24rpx;
            margin-top: 12rpx;
            display: block;
            padding-left: 8rpx;
        }

        .password-hint {
            color: #666;
            font-size: 24rpx;
            margin-top: 8rpx;
            display: block;
            padding-left: 8rpx;
            font-style: italic;
            line-height: 1.4;
            word-wrap: break-word;
            word-break: break-word;
        }
    }
}

.security-tips {
    margin: 0 32rpx 32rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);

    .tips-title {
        font-size: 30rpx;
        color: #333;
        font-weight: 700;
        margin-bottom: 20rpx;
        display: block;
        text-align: center;
    }

    .tip-item {
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 12rpx;
        display: block;
        padding-left: 20rpx;
        position: relative;

        &::before {
            content: '•';
            color: #ff6b6b;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    }
}

.submit-btn {
    margin: 0 32rpx 32rpx;
    background: #ff6b6b;
    height: 96rpx;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;

    text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 700;
    }

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
    }

    &.disabled {
        background: #e9ecef;
        box-shadow: none;

        text {
            color: #adb5bd;
        }
    }
}

.back-selection-btn {
    margin: 0 32rpx 32rpx;
    background: #fff;
    height: 96rpx;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid #e9ecef;
    transition: all 0.3s ease;

    text {
        font-size: 32rpx;
        color: #666;
        font-weight: 600;
    }

    &:active {
        transform: translateY(2rpx);
        background: #f8f9fa;
    }
}
</style>