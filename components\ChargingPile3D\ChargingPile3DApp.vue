<template>
  <view class="charging-pile-3d-container">
    <div id="threejs-container" ref="container" class="w-full h-full" :data-model-type="currentModelType"></div>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, watch, nextTick, ref, onMounted } from 'vue';

const props = defineProps({
  modelType: {
    type: String,
    default: 'default',
    validator: (v) => ['default', 'arnio'].includes(v)
  }
});

const emit = defineEmits(['loaded']);

// 当前模型类型
const currentModelType = ref(props.modelType);

// 显示loading
const showLoading = () => {
  uni.showLoading({
            title: t('common.loading') || 'Chargement...',
    mask: true
  });
};

// 隐藏loading
const hideLoading = () => {
  uni.hideLoading();
};

// 暴露方法给父组件调用
const reloadModel = (newType) => {
  console.log('🎯 reloadModel调用，类型:', newType);
  if (currentModelType.value !== newType) {
    currentModelType.value = newType;
    showLoading();
    console.log('📝 模型类型已更新为:', newType);
  }
}

// 暴露给父组件
defineExpose({
  reloadModel,
  showLoading,
  hideLoading
});

// 监听props变化
watch(() => props.modelType, (newType) => {
  console.log('🔄 props.modelType变化:', newType);
  if (currentModelType.value !== newType) {
    console.log('📝 props变化，更新currentModelType:', newType);
    currentModelType.value = newType;
    showLoading();
  }
});



// 监听当前模型类型变化，多种方式触发
watch(currentModelType, (newType, oldType) => {
  console.log('🔄 currentModelType变化:', oldType, '->', newType);

  // 方法1：直接调用全局函数
  setTimeout(() => {
    try {
      const win = typeof window !== 'undefined' ? window : globalThis;
      const reloadFunc = win['reloadChargingPileModel'];
      console.log('🔍 检查全局函数是否存在:', !!reloadFunc);

      if (reloadFunc) {
        console.log('🔧 方法1：调用全局函数重新加载模型，参数:', newType);
        reloadFunc(newType);
      } else {
        console.log('❌ 方法1失败：全局函数不存在');
      }
    } catch (error) {
      console.log('❌ 方法1异常:', error);
    }
  }, 100);

  // 方法2：通过DOM事件触发
  nextTick(() => {
    const container = document.querySelector('#threejs-container');
    if (container) {
      console.log('� 方法2：通过DOM事件触发模型切换');
      container.setAttribute('data-model-type', newType);

      // 触发自定义事件
      const event = new CustomEvent('modelTypeChange', {
        detail: { modelType: newType }
      });
      container.dispatchEvent(event);

      // 也尝试在window上触发事件
      try {
        const win = typeof window !== 'undefined' ? window : globalThis;
        win.dispatchEvent(new CustomEvent('chargingPileModelChange', {
          detail: { modelType: newType }
        }));
      } catch (error) {
        console.log('❌ 触发window事件失败:', error);
      }
    }
  });

  // 方法3：延迟重试全局函数
  setTimeout(() => {
    try {
      const win = typeof window !== 'undefined' ? window : globalThis;
      const reloadFunc = win['reloadChargingPileModel'];
      if (reloadFunc) {
        console.log('🔧 方法3：延迟重试调用全局函数，参数:', newType);
        reloadFunc(newType);
      } else {
        console.log('❌ 方法3失败：延迟重试仍然失败');
        console.log('🔍 当前window对象上的所有属性:', Object.keys(win).slice(0, 20));
      }
    } catch (error) {
      console.log('❌ 方法3异常:', error);
    }
  }, 1000);
});

// 在组件挂载时设置初始的data-model-type属性
onMounted(() => {
  console.log('🎯 Vue组件mounted，设置初始模型类型:', props.modelType);

  // 立即显示loading
  showLoading();

  nextTick(() => {
    const container = document.querySelector('#threejs-container');
    if (container) {
      container.setAttribute('data-model-type', props.modelType);
      console.log('✅ 已设置初始data-model-type属性:', props.modelType);

      // 监听renderjs发送的hideLoading事件
      container.addEventListener('hideLoading', () => {
        console.log('📡 收到hideLoading事件');
        hideLoading();
      });

      // 等待renderjs初始化完成后再尝试调用
      const checkAndCallReload = () => {
        try {
          const win = typeof window !== 'undefined' ? window : globalThis;
          const reloadFunc = win['reloadChargingPileModel'];
          if (reloadFunc) {
            console.log('🔄 renderjs已初始化，立即切换模型');
            reloadFunc(props.modelType);
            return true;
          }
          return false;
        } catch (error) {
          console.log('❌ 调用全局函数失败:', error);
          return false;
        }
      };

      // 立即尝试一次
      if (!checkAndCallReload()) {
        // 如果失败，每500ms重试一次，最多重试10次
        let retryCount = 0;
        const retryInterval = setInterval(() => {
          retryCount++;
          console.log(`🔄 重试调用全局函数 (${retryCount}/10)`);

          if (checkAndCallReload() || retryCount >= 10) {
            clearInterval(retryInterval);
            if (retryCount >= 10) {
              console.log('❌ 重试10次后仍然失败，放弃调用');
            }
          }
        }, 500);
      }
    } else {
      console.log('❌ 未找到threejs-container元素');
    }
  });
});
</script>

<script module="renderjs" lang="renderjs">
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js'

let scene, camera, renderer, controls, model, frameId
let isInit = false

// 获取模型路径列表
function getModelPaths(modelType) {
  console.log('📍 调用堆栈:', new Error().stack)
  if (modelType === 'arnio') {
    console.log('✅ 选择arnio模型路径')
    return [
      '_www/static/models/arnio.fbx',
      '_www/static/arnio.fbx',
      'static/models/arnio.fbx',
      'static/arnio.fbx',
      '/android_asset/www/static/models/arnio.fbx',
      '/android_asset/www/static/arnio.fbx'
    ]
  } else {
    console.log('✅ 选择default模型路径')
    return [
      '_www/static/GEN3APPFBX.fbx',
      '_www/static/models/GEN3APPFBX.fbx',
      'static/GEN3APPFBX.fbx',
      'static/models/GEN3APPFBX.fbx',
      '/android_asset/www/static/GEN3APPFBX.fbx',
      '/android_asset/www/static/models/GEN3APPFBX.fbx'
    ]
  }
}

// 递归尝试多个路径
function tryLoadFromPaths(paths, index) {
  if (index >= paths.length) {
    console.error('所有路径都尝试失败，无法加载模型')
    return
  }

  const currentPath = paths[index]
  console.log(`尝试加载模型路径 ${index + 1}/${paths.length}:`, currentPath)

  plus.io.resolveLocalFileSystemURL(currentPath, entry => {
    const loader = new FBXLoader()
    const reader = new plus.io.FileReader()

    reader.onloadend = function (e) {
      try {
        const result = e.target.result
        const arrayBuffer = base64ToArrayBuffer(result.split(',')[1])
        const fbx = loader.parse(arrayBuffer, '')

        // 移除旧模型
        if (model) {
          scene.remove(model)
          model.traverse(o => {
            o.geometry?.dispose?.()
            if (o.material) {
              Array.isArray(o.material)
                ? o.material.forEach(m => m.dispose?.())
                : o.material.dispose?.()
            }
          })
        }

        // 设置新模型
        model = fbx
        fbx.scale.set(0.18, 0.18, 0.18)
        fbx.position.set(0, -1.4, 0)
        fbx.rotation.y = Math.PI * 8/1
        fbx.rotation.x = Math.PI * 0.02

        // 优化材质渲染 - 特别针对ARNIO文字清晰度
        const optimizeMaterialsForText = (object3d) => {
          object3d.traverse((node) => {
            if (!node.isMesh || !node.material) return

            const applyTextOptimization = (mat) => {
              if (!mat) return

              // 获取材质的基础颜色来判断材质类型
              const baseColor = mat.color
              if (!baseColor) return

              // 检查是否是白色/浅色材质（可能是ARNIO文字）
              const isLightMaterial = (baseColor.r + baseColor.g + baseColor.b) / 3 > 0.7
              
              // 检查是否是红色主体材质
              const isRedMaterial = baseColor.r > 0.6 && baseColor.g < 0.4 && baseColor.b < 0.4

              // 检查是否是深色材质（可能是边框或细节）
              const isDarkMaterial = (baseColor.r + baseColor.g + baseColor.b) / 3 < 0.3

              if (isLightMaterial) {
                // 白色/浅色材质 - 优化文字清晰度
                mat.roughness = 0.1       // 非常低的粗糙度，高光泽
                mat.metalness = 0.0       // 非金属
                mat.emissive = new THREE.Color(0x111111)  // 轻微自发光，增强对比度

                // 确保白色材质足够亮和清晰
                if ((baseColor.r + baseColor.g + baseColor.b) / 3 > 0.8) {
                  mat.color.setHex(0xffffff)  // 纯白色
                }

                // 增强纹理清晰度
                if (mat.map) {
                  mat.map.anisotropy = renderer.capabilities.getMaxAnisotropy()
                  mat.map.minFilter = THREE.LinearFilter
                  mat.map.magFilter = THREE.LinearFilter
                  mat.map.generateMipmaps = true
                }

                console.log('⚪ 文字材质优化 - 增强清晰度')

              } else if (isRedMaterial) {
                // 红色主体 - 类似汽车漆面的光泽效果
                mat.roughness = 0.15      // 低粗糙度，高光泽
                mat.metalness = 0.05      // 轻微金属感
                mat.color.setHex(0xff2020) // 增强颜色饱和度

                // 添加轻微的环境反射
                if (mat.envMapIntensity !== undefined) {
                  mat.envMapIntensity = 0.3
                }

                console.log('🔴 红色材质光泽增强')

              } else if (isDarkMaterial) {
                // 深色材质 - 增加反射，模拟塑料光泽
                mat.roughness = 0.25      // 中等粗糙度
                mat.metalness = 0.1       // 轻微金属感

                console.log('⚫ 深色材质光泽增强')

              } else {
                // 其他材质 - 通用光泽优化
                mat.roughness = Math.max(0.2, (mat.roughness || 0.5) * 0.7)  // 降低粗糙度
                mat.metalness = Math.min(0.15, (mat.metalness || 0) + 0.05)  // 轻微增加金属感

                console.log('🔧 通用材质光泽优化')
              }

              mat.needsUpdate = true
            }

            if (Array.isArray(node.material)) {
              node.material.forEach(applyTextOptimization)
            } else {
              applyTextOptimization(node.material)
            }
          })
        }

        optimizeMaterialsForText(fbx)

        // 添加模型标识
        fbx.userData.modelType = currentPath.includes('arnio') ? 'arnio' : 'default'
        fbx.userData.loadPath = currentPath

        scene.add(fbx)

        // 调整相机位置
        const box = new THREE.Box3().setFromObject(fbx)
        const size = box.getSize(new THREE.Vector3())
        const center = box.getCenter(new THREE.Vector3())
        const maxDim = Math.max(size.x, size.y, size.z)
        const fov = camera.fov * Math.PI / 180
        let z = Math.abs(maxDim / Math.sin(fov / 2)) * 1.1
        camera.position.set(z * 0.3, z * 0.4, z)
        controls.target.set(center.x, center.y + size.y * 0.1, center.z)
        controls.update()

        // 强制渲染
        renderer.render(scene, camera)

        console.log('🎉 模型加载成功:', fbx.userData.modelType)

        // 隐藏loading
        hideVueLoading()
      } catch (err) {
        console.error('FBX解析错误:', err)
        tryLoadFromPaths(paths, index + 1)
      }
    }

    reader.readAsDataURL(entry)
  }, e => {
    console.log(`路径失败，尝试下一个:`, e)
    tryLoadFromPaths(paths, index + 1)
  })
}

// 隐藏Vue组件的loading
function hideVueLoading() {
  try {
    const container = document.querySelector('#threejs-container')
    if (container) {
      // 通过DOM事件通知Vue组件隐藏loading
      container.dispatchEvent(new CustomEvent('hideLoading'))
    }
  } catch (error) {
    console.log('❌ 隐藏loading失败:', error)
  }
}

// Base64 转 ArrayBuffer 工具函数
function base64ToArrayBuffer(base64) {
  const binaryString = atob(base64)
  const len = binaryString.length
  const bytes = new Uint8Array(len)
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes.buffer
}

function animate() {
  frameId = requestAnimationFrame(animate)
  
  // 确保控制器更新
  if (controls) {
    controls.update()
  }
  
  // 渲染场景
  if (renderer && scene && camera) {
    renderer.render(scene, camera)
  }
}

export default {
  mounted() {
    if (isInit) return
    isInit = true

    const container = document.querySelector('#threejs-container')

    // 从data-model-type属性获取初始模型类型，如果没有则使用默认值
    const modelType = container.getAttribute('data-model-type') || 'default'
    console.log('🚀 renderjs mounted，初始模型类型:', modelType)

    // 初始化Three.js场景
    scene = new THREE.Scene()
    camera = new THREE.PerspectiveCamera(40, container.clientWidth / container.clientHeight, 0.1, 2000)
    
    // 优化渲染器配置，提高性能和文字清晰度
    renderer = new THREE.WebGLRenderer({ 
      antialias: true, 
      alpha: true,
      powerPreference: 'high-performance',  // 优先使用高性能GPU
      precision: 'highp',                   // 使用高精度着色器
      preserveDrawingBuffer: false,         // 提高性能
      stencil: false,                       // 禁用模板缓冲
      depth: true                           // 启用深度缓冲
    })
    renderer.setSize(container.clientWidth, container.clientHeight)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 3))  // 提高像素比，增强清晰度
    renderer.shadowMap.enabled = false  // 禁用阴影以提高性能
    
    // 优化渲染设置 - 增强光泽效果和文字清晰度
    renderer.outputColorSpace = THREE.SRGBColorSpace
    renderer.toneMapping = THREE.ACESFilmicToneMapping
    renderer.toneMappingExposure = 1.2  // 提高曝光，增强文字对比度
    
    container.appendChild(renderer.domElement)

    // 优化光照 - 增强文字清晰度
    const amb = new THREE.AmbientLight(0xffffff, 0.5)  // 增加环境光，提高整体亮度
    scene.add(amb)
    
    // 主光源 - 从右上方照射，产生自然高光
    const mainLight = new THREE.DirectionalLight(0xffffff, 1.4)  // 增强主光源强度
    mainLight.position.set(5, 8, 5)
    mainLight.castShadow = false
    scene.add(mainLight)
    
    // 补光 - 从左侧轻柔照射，避免过暗
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.7)  // 增强补光强度
    fillLight.position.set(-4, 6, 6)
    fillLight.castShadow = false
    scene.add(fillLight)
    
    // 顶部光源 - 增强整体亮度，特别是文字区域
    const topLight = new THREE.DirectionalLight(0xffffff, 0.6)  // 增强顶部光源
    topLight.position.set(0, 12, 0)
    topLight.castShadow = false
    scene.add(topLight)
    
    // 前光源 - 专门照亮文字区域
    const frontLight = new THREE.DirectionalLight(0xffffff, 0.9)  // 前光源，专门照亮正面
    frontLight.position.set(0, 2, 8)
    frontLight.castShadow = false
    scene.add(frontLight)

    // 设置控制器
    controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.08  // 增加阻尼系数，让转动更丝滑
    controls.rotateSpeed = 0.6     // 增加旋转速度，提高灵敏度
    controls.enableZoom = false
    controls.enablePan = false
    controls.autoRotate = false
    controls.minPolarAngle = Math.PI / 3.5   // 稍微放宽最高视角 (约51度)
    controls.maxPolarAngle = Math.PI / 1.7   // 稍微放宽最低视角 (约106度)
    controls.target.set(0, 0.5, 0)
    
    // 添加触摸优化配置
    controls.touches = {
      ONE: THREE.TOUCH.ROTATE,
      TWO: THREE.TOUCH.DOLLY_PAN
    }
    
    // 优化移动端触摸响应
    controls.enableKeys = false
    controls.screenSpacePanning = false
    
    controls.update()

    // 加载初始模型
    const modelPaths = getModelPaths(modelType)
    tryLoadFromPaths(modelPaths, 0)
    animate()

    // 设置全局函数供外部调用
    window.reloadChargingPileModel = (newModelType) => {
      console.log('🎯🎯🎯 全局函数reloadChargingPileModel被调用！类型:', newModelType)
      console.log('🔍 场景状态检查:', {
        scene: !!scene,
        camera: !!camera,
        renderer: !!renderer,
        model: !!model,
        currentModelType: model?.userData?.modelType
      })

      if (scene && camera && renderer) {
        const currentModelType = model?.userData?.modelType
        if (!model || currentModelType !== newModelType) {
          console.log('✅ 需要重新加载模型:', currentModelType, '->', newModelType)
          const newModelPaths = getModelPaths(newModelType)
          tryLoadFromPaths(newModelPaths, 0)
        } else {
          console.log('⏭️ 模型类型未变化，跳过重新加载')
        }
      } else {
        console.log('❌ 场景未初始化，无法重新加载模型')
      }
    }

    // 设置简化的全局函数，直接切换模型
    window.switchChargingPileModel = (newModelType) => {
      console.log('🚀 简化全局函数switchChargingPileModel被调用！类型:', newModelType)

      if (scene && camera && renderer) {
        console.log('✅ 直接切换模型:', newModelType)
        const newModelPaths = getModelPaths(newModelType)
        tryLoadFromPaths(newModelPaths, 0)
      } else {
        console.log('❌ 场景未初始化，无法切换模型')
      }
    }
    
    console.log('✅ 全局函数已设置:', {
      reloadChargingPileModel: !!window.reloadChargingPileModel,
      switchChargingPileModel: !!window.switchChargingPileModel
    })

    // 添加DOM事件监听器
    container.addEventListener('modelTypeChange', (event) => {
      console.log('🎯🎯🎯 DOM事件监听器被触发！', event.detail);
      const newModelType = event.detail.modelType;
      if (newModelType && scene && camera && renderer) {
        const currentModelType = model?.userData?.modelType;
        if (!model || currentModelType !== newModelType) {
          console.log('✅ DOM事件：需要重新加载模型:', currentModelType, '->', newModelType);
          const newModelPaths = getModelPaths(newModelType);
          tryLoadFromPaths(newModelPaths, 0);
        }
      }
    });

    // 添加window事件监听器
    window.addEventListener('chargingPileModelChange', (event) => {
      console.log('🎯🎯🎯 Window事件监听器被触发！', event.detail);
      const newModelType = event.detail.modelType;
      if (newModelType && scene && camera && renderer) {
        const currentModelType = model?.userData?.modelType;
        if (!model || currentModelType !== newModelType) {
          console.log('✅ Window事件：需要重新加载模型:', currentModelType, '->', newModelType);
          const newModelPaths = getModelPaths(newModelType);
          tryLoadFromPaths(newModelPaths, 0);
        }
      }
    });

    // 添加定时检查机制
    setInterval(() => {
      const container = document.querySelector('#threejs-container');
      if (container) {
        const dataModelType = container.getAttribute('data-model-type');
        if (dataModelType && model) {
          const currentModelType = model.userData?.modelType;
          if (currentModelType !== dataModelType) {
            console.log('🎯🎯🎯 定时检查发现模型类型变化:', currentModelType, '->', dataModelType);
            const newModelPaths = getModelPaths(dataModelType);
            tryLoadFromPaths(newModelPaths, 0);
          }
        }
      }
    }, 1000);

    // 窗口大小调整
    const onResize = () => {
      camera.aspect = container.clientWidth / container.clientHeight
      camera.updateProjectionMatrix()
      renderer.setSize(container.clientWidth, container.clientHeight)
    }
    window.addEventListener('resize', onResize)
    window._onResizeHandler = onResize
  },

  beforeDestroy() {
    window.removeEventListener('resize', window._onResizeHandler)
    cancelAnimationFrame(frameId)
    controls?.dispose()
    model?.traverse(o => {
      o.geometry?.dispose?.()
      if (Array.isArray(o.material))
        o.material.forEach(m => m.dispose?.())
      else o.material?.dispose?.()
    })
    renderer?.dispose()
    renderer?.forceContextLoss()
  }
}
</script>

<style scoped>
.charging-pile-3d-container {
  width: 100%;
  height: 100%;
  min-height: 1000rpx;
  overflow: hidden;
  position: relative;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}
</style>
