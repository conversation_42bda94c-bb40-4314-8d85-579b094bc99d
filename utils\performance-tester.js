/**
 * 性能测试工具
 * 用于测试和验证扫码功能的性能优化效果
 */

import { getDeviceSummary } from './device-performance.js';
import { getPerformanceSummary } from './performance-optimizer.js';

/**
 * 性能测试器类
 */
class PerformanceTester {
  constructor() {
    this.testResults = [];
    this.isRunning = false;
    this.startTime = null;
    this.memoryBaseline = null;
  }

  /**
   * 开始性能测试
   * @param {string} testName 测试名称
   */
  startTest(testName = 'Performance Test') {
    if (this.isRunning) {
      console.warn('测试已在运行中');
      return;
    }

    this.isRunning = true;
    this.startTime = Date.now();
    this.testName = testName;
    
    // 记录内存基线
    this.recordMemoryBaseline();
    
    console.log(`🚀 开始性能测试: ${testName}`);
    console.log(`📱 测试时间: ${new Date().toLocaleString()}`);
  }

  /**
   * 结束性能测试
   * @returns {Object} 测试结果
   */
  endTest() {
    if (!this.isRunning) {
      console.warn('没有正在运行的测试');
      return null;
    }

    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    const result = {
      testName: this.testName,
      startTime: this.startTime,
      endTime: endTime,
      duration: duration,
      memoryUsage: this.getMemoryUsage(),
      deviceInfo: null,
      performanceInfo: null,
      timestamp: new Date().toISOString()
    };

    this.testResults.push(result);
    this.isRunning = false;
    
    console.log(`✅ 测试完成: ${this.testName}`);
    console.log(`⏱️ 耗时: ${duration}ms`);
    
    return result;
  }

  /**
   * 记录内存基线
   */
  recordMemoryBaseline() {
    this.memoryBaseline = this.getCurrentMemoryUsage();
  }

  /**
   * 获取当前内存使用情况
   * @returns {Object} 内存信息
   */
  getCurrentMemoryUsage() {
    // #ifdef H5
    if (typeof performance !== 'undefined' && performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      };
    }
    // #endif

    // #ifdef APP-PLUS
    try {
      if (plus.device && plus.device.getMemoryInfo) {
        return new Promise((resolve) => {
          plus.device.getMemoryInfo({
            success: (info) => resolve({
              used: info.used || 0,
              total: info.total || 0,
              available: info.available || 0
            }),
            fail: () => resolve({ used: 0, total: 0, available: 0 })
          });
        });
      }
    } catch (error) {
      console.warn('获取内存信息失败:', error);
    }
    // #endif

    return { used: 0, total: 0, available: 0 };
  }

  /**
   * 获取内存使用变化
   * @returns {Object} 内存变化信息
   */
  getMemoryUsage() {
    const current = this.getCurrentMemoryUsage();
    const baseline = this.memoryBaseline || { used: 0, total: 0 };
    
    return {
      baseline: baseline,
      current: current,
      increase: current.used - baseline.used,
      percentage: baseline.used > 0 ? ((current.used - baseline.used) / baseline.used * 100).toFixed(2) : 0
    };
  }

  /**
   * 测试扫码性能
   * @returns {Promise<Object>} 测试结果
   */
  async testScannerPerformance() {
    console.log('🔍 开始扫码性能测试');
    
    this.startTest('Scanner Performance Test');
    
    try {
      // 获取设备信息
      const deviceInfo = await getDeviceSummary();
      console.log('📱 设备信息:', deviceInfo);
      
      // 获取性能优化信息
      const performanceInfo = getPerformanceSummary();
      console.log('⚡ 性能优化:', performanceInfo);
      
      // 模拟扫码操作
      const scanResult = await this.simulateScanOperation();
      console.log('📷 扫码结果:', scanResult);
      
      const result = this.endTest();
      result.deviceInfo = deviceInfo;
      result.performanceInfo = performanceInfo;
      result.scanResult = scanResult;
      
      return result;
    } catch (error) {
      console.error('扫码性能测试失败:', error);
      this.endTest();
      throw error;
    }
  }

  /**
   * 模拟扫码操作
   * @returns {Promise<Object>} 扫码结果
   */
  async simulateScanOperation() {
    const startTime = Date.now();
    
    // 模拟扫码初始化时间
    await this.delay(100);
    
    // 模拟相机启动时间
    const cameraStartTime = Date.now();
    await this.delay(500);
    const cameraStartDuration = Date.now() - cameraStartTime;
    
    // 模拟扫码识别时间
    const recognitionStartTime = Date.now();
    await this.delay(800);
    const recognitionDuration = Date.now() - recognitionStartTime;
    
    const totalDuration = Date.now() - startTime;
    
    return {
      totalDuration,
      cameraStartDuration,
      recognitionDuration,
      success: true
    };
  }

  /**
   * 测试UI渲染性能
   * @returns {Promise<Object>} 测试结果
   */
  async testUIPerformance() {
    console.log('🎨 开始UI渲染性能测试');
    
    this.startTest('UI Performance Test');
    
    try {
      // 测试动画性能
      const animationResult = await this.testAnimationPerformance();
      
      // 测试渲染性能
      const renderResult = await this.testRenderPerformance();
      
      const result = this.endTest();
      result.animationResult = animationResult;
      result.renderResult = renderResult;
      
      return result;
    } catch (error) {
      console.error('UI性能测试失败:', error);
      this.endTest();
      throw error;
    }
  }

  /**
   * 测试动画性能
   * @returns {Promise<Object>} 动画测试结果
   */
  async testAnimationPerformance() {
    const startTime = Date.now();
    
    // 模拟动画执行
    for (let i = 0; i < 10; i++) {
      await this.delay(50);
      // 模拟DOM操作
      if (typeof document !== 'undefined') {
        const element = document.createElement('div');
        element.style.transform = `translateX(${i * 10}px)`;
        document.body.appendChild(element);
        setTimeout(() => document.body.removeChild(element), 10);
      }
    }
    
    return {
      duration: Date.now() - startTime,
      frameCount: 10,
      averageFrameTime: (Date.now() - startTime) / 10
    };
  }

  /**
   * 测试渲染性能
   * @returns {Promise<Object>} 渲染测试结果
   */
  async testRenderPerformance() {
    const startTime = Date.now();
    
    // 模拟复杂渲染操作
    await this.delay(200);
    
    return {
      duration: Date.now() - startTime,
      renderComplexity: 'medium'
    };
  }

  /**
   * 生成性能报告
   * @returns {Object} 性能报告
   */
  generateReport() {
    if (this.testResults.length === 0) {
      return { message: '没有测试结果' };
    }

    const report = {
      summary: {
        totalTests: this.testResults.length,
        averageDuration: this.calculateAverageDuration(),
        memoryImpact: this.calculateMemoryImpact()
      },
      tests: this.testResults,
      recommendations: this.generateRecommendations()
    };

    console.log('📊 性能测试报告:', report);
    return report;
  }

  /**
   * 计算平均耗时
   * @returns {number} 平均耗时（毫秒）
   */
  calculateAverageDuration() {
    const totalDuration = this.testResults.reduce((sum, result) => sum + result.duration, 0);
    return Math.round(totalDuration / this.testResults.length);
  }

  /**
   * 计算内存影响
   * @returns {Object} 内存影响统计
   */
  calculateMemoryImpact() {
    const memoryIncreases = this.testResults
      .filter(result => result.memoryUsage && result.memoryUsage.increase)
      .map(result => result.memoryUsage.increase);
    
    if (memoryIncreases.length === 0) {
      return { average: 0, max: 0, min: 0 };
    }

    return {
      average: Math.round(memoryIncreases.reduce((sum, val) => sum + val, 0) / memoryIncreases.length),
      max: Math.max(...memoryIncreases),
      min: Math.min(...memoryIncreases)
    };
  }

  /**
   * 生成优化建议
   * @returns {Array} 建议列表
   */
  generateRecommendations() {
    const recommendations = [];
    const avgDuration = this.calculateAverageDuration();
    const memoryImpact = this.calculateMemoryImpact();

    if (avgDuration > 3000) {
      recommendations.push('扫码响应时间较长，建议进一步优化扫码算法');
    }

    if (memoryImpact.average > 50 * 1024 * 1024) { // 50MB
      recommendations.push('内存使用增长较大，建议加强内存管理');
    }

    if (recommendations.length === 0) {
      recommendations.push('性能表现良好，继续保持');
    }

    return recommendations;
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清除测试结果
   */
  clearResults() {
    this.testResults = [];
    console.log('🗑️ 测试结果已清除');
  }

  /**
   * 导出测试结果
   * @returns {string} JSON格式的测试结果
   */
  exportResults() {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      results: this.testResults,
      report: this.generateReport()
    }, null, 2);
  }
}

// 创建全局实例
const performanceTester = new PerformanceTester();

/**
 * 运行完整的性能测试套件
 * @returns {Promise<Object>} 测试报告
 */
export async function runPerformanceTestSuite() {
  console.log('🧪 开始完整性能测试套件');
  
  try {
    // 清除之前的结果
    performanceTester.clearResults();
    
    // 运行扫码性能测试
    await performanceTester.testScannerPerformance();
    
    // 运行UI性能测试
    await performanceTester.testUIPerformance();
    
    // 生成报告
    const report = performanceTester.generateReport();
    
    console.log('✅ 性能测试套件完成');
    return report;
  } catch (error) {
    console.error('❌ 性能测试套件失败:', error);
    throw error;
  }
}

/**
 * 快速扫码性能测试
 * @returns {Promise<Object>} 测试结果
 */
export async function quickScannerTest() {
  return await performanceTester.testScannerPerformance();
}

/**
 * 获取性能测试器实例
 * @returns {PerformanceTester} 测试器实例
 */
export function getPerformanceTester() {
  return performanceTester;
}

export default performanceTester;
