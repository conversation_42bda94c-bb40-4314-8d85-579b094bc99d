/**
 * 车辆行驶证扫描工具类
 * 提供车辆行驶证扫描、OCR识别和数据处理功能
 * 支持设备性能自适应优化
 */

import { getPerformanceConfig, isLowEndDevice } from './device-performance.js';
import { getPreloadedScanConfig, isReadyForScan } from './scanner-preloader.js';

// 模拟OCR服务API地址
const OCR_API_URL = 'https://api.example.com/ocr/vehicle-license'

// 扫码性能配置缓存
let performanceConfig = null;
let isLowEndDeviceCache = null;

/**
 * 启动扫描流程
 * @param {Object} options 配置选项
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 * @param {Function} options.complete 完成回调
 */
export function scanVehicleLicense(options = {}) {
  // 直接启动扫描，不检查权限
  startScan(options)
}

/**
 * 启动充电桩扫描流程（支持性能自适应优化）
 * @param {Object} options 配置选项
 * @param {Function} options.success 成功回调
 * @param {Function} options.fail 失败回调
 * @param {Function} options.complete 完成回调
 * @param {Boolean} options.showTips 是否显示提示，默认true
 * @param {String} options.tips 自定义提示文本
 * @param {Boolean} options.forceHighQuality 强制使用高质量扫码（忽略设备性能）
 */
export async function scanChargingPile(options = {}) {
  console.log('Starting charging pile scan function');

  try {
    // 获取设备性能配置
    const config = await getScannerPerformanceConfig();
    console.log('扫码性能配置:', config);

    // 移除扫描提示，直接启动

    // 根据设备性能调整扫码参数
    const scanConfig = buildScanConfig(config, options);

    // 启动扫码
    uni.scanCode(scanConfig);
  } catch (error) {
    console.error('扫码初始化失败:', error);
    // 降级到基础扫码配置
    fallbackScanCode(options);
  }
}

/**
 * 获取扫码器性能配置
 * @returns {Promise<Object>} 性能配置
 */
async function getScannerPerformanceConfig() {
  if (performanceConfig && isLowEndDeviceCache !== null) {
    return performanceConfig.scanner;
  }

  try {
    performanceConfig = await getPerformanceConfig();
    isLowEndDeviceCache = await isLowEndDevice();
    return performanceConfig.scanner;
  } catch (error) {
    console.warn('获取性能配置失败，使用默认配置:', error);
    return {
      enableAnimations: true,
      scanQuality: 'medium',
      previewFrameRate: 24,
      enableHoverEffects: true,
      enable3DRendering: true
    };
  }
}

/**
 * 构建扫码配置（优化版本）
 * @param {Object} config 性能配置
 * @param {Object} options 用户选项
 * @returns {Object} 扫码配置
 */
function buildScanConfig(config, options) {
  // 尝试使用预加载的配置
  const preloadedConfig = getPreloadedScanConfig();

  const baseConfig = preloadedConfig || {
    scanType: ['qrCode'], // 只扫描二维码，减少处理负担
    onlyFromCamera: true,
    autoDecodeCharset: true
  };

  // 添加回调函数
  Object.assign(baseConfig, {
    success: (res) => handleScanSuccess(res, options),
    fail: (err) => handleScanFail(err, options),
    complete: () => {
      if (options.complete) {
        options.complete();
      }
    }
  });

  // 根据设备性能调整扫码参数
  if (isLowEndDeviceCache || config.scanQuality === 'low') {
    // 低端设备优化配置
    Object.assign(baseConfig, {
      // 降低扫码质量以提升性能
      compressed: true,
      // 限制扫码区域以减少处理量
      scanArea: {
        x: 0.2,
        y: 0.3,
        width: 0.6,
        height: 0.4
      },
      // 添加低端设备专用优化
      enableBeep: false, // 禁用提示音
      enableVibrate: false, // 禁用震动
      enableFlash: false // 禁用闪光灯
    });

    console.log('应用低端设备扫码优化配置');
  } else if (config.scanQuality === 'high' || options.forceHighQuality) {
    // 高端设备或强制高质量配置
    Object.assign(baseConfig, {
      compressed: false,
      // 全屏扫码区域
      scanArea: {
        x: 0,
        y: 0,
        width: 1,
        height: 1
      }
    });

    console.log('应用高质量扫码配置');
  }

  return baseConfig;
}

/**
 * 处理扫码成功
 * @param {Object} res 扫码结果
 * @param {Object} options 选项
 */
function handleScanSuccess(res, options) {
  console.log('Scan successful:', res);

  // 验证扫码结果是否有效
  if (!res || (!res.result && !res.path && !res.rawData)) {
    const error = new Error('Invalid scan result');
    console.error('Invalid scan result');
    if (options.fail) {
      options.fail(error);
    }
    return;
  }

  // 处理扫码结果
  if (options.success) {
    options.success(res);
  }
}

/**
 * 处理扫码失败
 * @param {Object} err 错误信息
 * @param {Object} options 选项
 */
function handleScanFail(err, options) {
  console.error('Scan failed:', err);

  // 用户取消扫描不显示错误提示
  if (err && err.errMsg && err.errMsg.includes('cancel')) {
    if (options.fail) {
      options.fail({ errMsg: 'cancel', cancelled: true });
    }
    return;
  }

  // 移除错误提示，只记录日志
  console.warn('扫码失败:', err.errMsg);

  if (options.fail) {
    options.fail(err);
  }
}

/**
 * 降级扫码配置（当性能检测失败时使用）
 * @param {Object} options 选项
 */
function fallbackScanCode(options) {
  console.log('使用降级扫码配置');

  uni.scanCode({
    scanType: ['qrCode'],
    onlyFromCamera: true,
    autoDecodeCharset: true,
    compressed: true, // 默认启用压缩以提升兼容性
    success: (res) => handleScanSuccess(res, options),
    fail: (err) => handleScanFail(err, options),
    complete: () => {
      if (options.complete) {
        options.complete();
      }
    }
  });
}

/**
 * 检查相机权限
 * @returns {Promise} 权限检查结果
 */
function checkCameraPermission() {
  return new Promise((resolve, reject) => {
    // #ifdef APP-PLUS
    const camera = plus.camera.getCamera();
    if (!camera) {
      reject(new Error('No camera available'));
      return;
    }
    
    plus.navigator.checkPermission('CAMERA', (result) => {
      if (result === 'authorized') {
        resolve();
      } else {
        plus.navigator.requestPermission('CAMERA', 
          (status) => {
            if (status === 'authorized') {
              resolve();
            } else {
              reject(new Error('Camera permission denied'));
            }
          },
          (error) => {
            reject(error);
          }
        );
      }
    });
    // #endif
    
    // #ifdef H5 || MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO
    resolve(); // 小程序和H5环境下，uni.scanCode会自动处理权限
    // #endif
  });
}

/**
 * 从相册选择二维码图片
 * @param {Object} options 配置选项
 */
function selectQRCodeFromAlbum(options = {}) {
  uni.chooseImage({
    count: 1,
    sourceType: ['album'],
    success: (res) => {
      const tempFilePath = res.tempFilePaths[0];
      
      // 显示加载中
      uni.showLoading({
        title: 'Recognizing...'
      });
      
      // 调用uni.scanCode的图片模式或自定义解析
      // #ifdef APP-PLUS
      plus.barcode.scan(tempFilePath, 
        (type, result) => {
          uni.hideLoading();
          if (result) {
            if (options.success) {
              options.success({ result });
            }
          } else {
            if (options.fail) {
              options.fail({ errMsg: 'Unable to recognize QR code in image' });
            }
          }
        },
        (error) => {
          uni.hideLoading();
          if (options.fail) {
            options.fail({ errMsg: 'QR code recognition failed: ' + error.message });
          }
        }
      );
      // #endif
      
      // #ifdef H5 || MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO
      // 小程序环境下，可能需要调用云函数或后端API进行解析
      setTimeout(() => {
        uni.hideLoading();
        if (options.fail) {
          options.fail({ errMsg: 'Current environment does not support image QR code recognition, please scan directly' });
        }
      }, 1000);
      // #endif
    },
    fail: (err) => {
      if (options.fail) {
        options.fail(err);
      }
    }
  });
}

/**
 * 启动扫描
 * @param {Object} options 配置选项
 */
function startScan(options = {}) {
  // 判断是使用原生扫码还是拍照上传
  if (isOcrScanAvailable()) {
    // 使用原生扫码
    nativeScan(options)
  } else {
    // 使用拍照上传
    photoUploadScan(options)
  }
}

/**
 * 判断是否可以使用原生OCR扫描
 * @returns {Boolean} 是否可用
 */
function isOcrScanAvailable() {
  // 启用原生扫码，显示二维码框界面
  return true
}

/**
 * 使用原生扫码API
 * @param {Object} options 配置选项
 */
function nativeScan(options = {}) {
  uni.scanCode({
    scanType: ['qrCode', 'barCode', 'datamatrix', 'pdf417'],
    onlyFromCamera: true,
    success: (res) => {
      // 直接返回扫描结果，不强制解析为JSON
      if (options.success) {
        options.success(res)
      }
    },
    fail: (err) => {
      if (options.fail) {
        options.fail(err)
      }
    },
    complete: () => {
      if (options.complete) {
        options.complete()
      }
    }
  })
}

/**
 * 使用拍照上传方式
 * @param {Object} options 配置选项
 */
function photoUploadScan(options = {}) {
  // 显示引导提示
  uni.showToast({
    title: 'Please place the vehicle license within the frame for shooting',
    icon: 'none',
    duration: 2000
  })
  
  // 延迟启动相机
  setTimeout(() => {
    uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        
        // 显示加载中
        uni.showLoading({
          title: t('loading.recognizing') || 'Reconnaissance...'
        })
        
        // 上传图片进行OCR识别
        uploadForOcr(tempFilePath, {
          success: (vehicleInfo) => {
            uni.hideLoading()
            
            if (options.success) {
              options.success(vehicleInfo)
            }
          },
          fail: (err) => {
            uni.hideLoading()
            
            if (options.fail) {
              options.fail(err)
            }
          }
        })
      },
      fail: (err) => {
        if (options.fail) {
          options.fail(err)
        }
      }
    })
  }, 1000)
}

/**
 * 上传图片进行OCR识别
 * @param {String} filePath 图片路径
 * @param {Object} options 配置选项
 */
function uploadForOcr(filePath, options = {}) {
  // 实际项目中应上传到服务器进行OCR识别
  // 这里模拟识别过程
  
  // 模拟网络请求延迟
  setTimeout(() => {
    try {
      // 模拟OCR识别结果
      const vehicleInfo = {
        licensePlate: 'AB123CD',
        vehicleType: 'Electric Vehicle',
        registrationDate: '2023-01-15',
        brand: 'Tesla',
        model: 'Model X',
        owner: 'John Doe',
        engineNumber: 'ENG12345678',
        vin: 'VIN9876543210'
      }
      
      // 处理识别结果
      if (options.success) {
        options.success(vehicleInfo)
      }
    } catch (error) {
      if (options.fail) {
        options.fail({ errMsg: t('toast.ocrFailed') || 'Échec de reconnaissance OCR', error })
      }
    }
  }, 2000)
  
  // 实际项目中的代码应类似：
  /*
  uni.uploadFile({
    url: OCR_API_URL,
    filePath: filePath,
    name: 'file',
    success: (uploadRes) => {
      try {
        const result = JSON.parse(uploadRes.data)
        if (result.success) {
          // 处理OCR结果
          const vehicleInfo = processOcrResult(result.data)
          
          if (options.success) {
            options.success(vehicleInfo)
          }
        } else {
          throw new Error(result.message || t('toast.recognitionFailed') || 'Échec de reconnaissance')
        }
      } catch (error) {
        if (options.fail) {
          options.fail({ errMsg: t('toast.ocrProcessingFailed') || 'Échec du traitement du résultat OCR', error })
        }
      }
    },
    fail: (err) => {
      if (options.fail) {
        options.fail({ errMsg: t('toast.uploadFailed') || 'Échec du téléchargement d\'image', error: err })
      }
    }
  })
  */
}

/**
 * 处理OCR识别结果
 * @param {Object} ocrData OCR原始数据
 * @returns {Object} 格式化后的车辆信息
 */
function processOcrResult(ocrData) {
  // 提取关键信息并格式化
  return {
    licensePlate: ocrData.plate_num || '',
    vehicleType: mapVehicleType(ocrData.vehicle_type) || '',
    registrationDate: formatDate(ocrData.register_date) || '',
    brand: ocrData.brand || '',
    model: ocrData.model || '',
    owner: ocrData.owner || '',
    engineNumber: ocrData.engine_number || '',
    vin: ocrData.vin || ''
  }
}

/**
 * 映射车辆类型
 * @param {String} rawType 原始类型
 * @returns {String} 映射后的类型
 */
function mapVehicleType(rawType) {
  const typeMap = {
    '新能源': 'Electric Vehicle',
    '混合动力': 'Hybrid',
    '汽油': 'Gasoline',
    '柴油': 'Diesel'
  }
  
  return typeMap[rawType] || 'Electric Vehicle'
}

/**
 * 格式化日期
 * @param {String} rawDate 原始日期
 * @returns {String} 格式化后的日期 (YYYY-MM-DD)
 */
function formatDate(rawDate) {
  if (!rawDate) return ''
  
  try {
    // 处理常见的日期格式
    const date = new Date(rawDate)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    return `${year}-${month}-${day}`
  } catch (error) {
    console.error('日期格式化失败:', error)
    return rawDate
  }
} 