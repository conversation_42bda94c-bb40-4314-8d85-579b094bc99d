/**
 * Toast 组合式函数
 * 提供全局的Toast功能
 */
import { ref, reactive } from 'vue'

// 全局Toast状态
const toastState = reactive({
    visible: false,
    message: '',
    title: '',
    type: 'info',
    duration: 3000,
    showIcon: true,
    showClose: false,
    position: 'center',
    confirmText: 'Confirm', // 默认英文，调用方应传入国际化文本
    cancelText: 'Cancel', // 默认英文，调用方应传入国际化文本
    closeOnClickOverlay: true,
    onConfirm: null,
    onCancel: null
})

/**
 * 使用Toast
 */
export function useToast() {
    
    /**
     * 显示Toast
     * @param {Object} options - Toast选项
     * @param {string} options.message - 消息内容
     * @param {string} options.title - 标题
     * @param {string} options.type - 类型：success, error, warning, info
     * @param {number} options.duration - 持续时间（毫秒），0表示不自动关闭
     * @param {boolean} options.showIcon - 是否显示图标
     * @param {boolean} options.showClose - 是否显示关闭按钮
     * @param {string} options.position - 位置：top, center, bottom
     */
    const showToast = (options) => {
        // 如果只传入字符串，则作为message
        if (typeof options === 'string') {
            options = { message: options }
        }
        
        // 合并默认选项
        const finalOptions = {
            message: '',
            title: '',
            type: 'info',
            duration: 3000,
            showIcon: true,
            showClose: false,
            position: 'center',
            ...options
        }
        
        // 更新状态
        Object.assign(toastState, finalOptions, { visible: true })
    }
    
    /**
     * 成功提示
     * @param {string|Object} options - 消息内容或选项对象
     */
    const success = (options) => {
        if (typeof options === 'string') {
            options = { message: options }
        }
        showToast({ ...options, type: 'success' })
    }
    
    /**
     * 错误提示
     * @param {string|Object} options - 消息内容或选项对象
     */
    const error = (options) => {
        if (typeof options === 'string') {
            options = { message: options }
        }
        showToast({ ...options, type: 'error' })
    }
    
    /**
     * 警告提示
     * @param {string|Object} options - 消息内容或选项对象
     */
    const warning = (options) => {
        if (typeof options === 'string') {
            options = { message: options }
        }
        showToast({ ...options, type: 'warning' })
    }
    
    /**
     * 信息提示
     * @param {string|Object} options - 消息内容或选项对象
     */
    const info = (options) => {
        if (typeof options === 'string') {
            options = { message: options }
        }
        showToast({ ...options, type: 'info' })
    }
    
    /**
     * 隐藏Toast
     */
    const hideToast = () => {
        toastState.visible = false
    }

    /**
     * 清理Toast状态
     * 用于退出登录时清理所有弹窗状态
     */
    const clearToastState = () => {
        Object.assign(toastState, {
            visible: false,
            message: '',
            title: '',
            type: 'info',
            duration: 3000,
            showIcon: true,
            showClose: false,
            position: 'center',
            confirmText: 'Confirm',
            cancelText: 'Cancel',
            closeOnClickOverlay: true,
            onConfirm: null,
            onCancel: null
        })
        console.log('Toast状态已清理')
    }
    
    /**
     * 确认对话框
     * @param {Object} options - 选项
     * @param {string} options.message - 消息内容
     * @param {string} options.title - 标题
     * @param {string} options.confirmText - 确认按钮文字
     * @param {string} options.cancelText - 取消按钮文字
     * @param {Function} options.onConfirm - 确认回调
     * @param {Function} options.onCancel - 取消回调
     */
    const confirm = (options) => {
        const {
            message = 'Are you sure you want to perform this operation?', // 默认英文，调用方应传入国际化文本
            title = 'Confirm', // 默认英文，调用方应传入国际化文本
            confirmText = 'Confirm', // 默认英文，调用方应传入国际化文本
            cancelText = 'Cancel', // 默认英文，调用方应传入国际化文本
            onConfirm,
            onCancel
        } = options

        // 更新状态
        Object.assign(toastState, {
            visible: true,
            message,
            title,
            type: 'confirm',
            confirmText,
            cancelText,
            onConfirm,
            onCancel,
            duration: 0, // 确认对话框不自动关闭
            showIcon: false,
            showClose: false,
            closeOnClickOverlay: false
        })
    }
    
    return {
        // 状态
        toastState,

        // 方法
        showToast,
        success,
        error,
        warning,
        info,
        hideToast,
        confirm,
        clearToastState
    }
}

/**
 * 全局Toast实例（单例模式）
 */
let globalToastInstance = null

export function useGlobalToast() {
    if (!globalToastInstance) {
        globalToastInstance = useToast()
    }
    return globalToastInstance
}

/**
 * 全局清理Toast状态的工具函数
 * 可以在任何地方调用，确保Toast状态被正确清理
 */
export function clearGlobalToastState() {
    try {
        const toast = useGlobalToast()
        if (toast && toast.clearToastState) {
            toast.clearToastState()
            console.log('全局Toast状态已清理')
        }
    } catch (error) {
        console.warn('清理全局Toast状态失败:', error)
    }
}
