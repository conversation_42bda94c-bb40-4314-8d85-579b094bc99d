<template>
  <view class="standard-header">
    <!-- 状态栏占位 -->
    <view class="status-bar :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- 导航栏 -->
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="handleBack" v-if="showBack">       <text class="iconfont icon-back"></text>
        </view>
        <text class="title">{{ title }}</text>
        <view class="right-btn" @click="handleRightClick" v-if="rightText || rightIcon">
          <text v-if="rightText">{{ rightText }}</text>
          <image v-if="rightIcon" :src="rightIcon" class="right-icon" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  showBack: {
    type: <PERSON>olean,
    default: true
  },
  rightText: {
    type: String,
    default: ''
  },
  rightIcon: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['back', 'right-click'])

// 响应式数据
const statusBarHeight = ref(0)

// 生命周期
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
})

// 方法
const handleBack = () => {
  emit('back')
  uni.navigateBack()
}

const handleRightClick = () => {
  emit('right-click')
}
</script>

<style lang="scss" scoped>
.standard-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0,0,0,0.04);
}

.status-bar {
  background-color: #fff;
}

.header {
  background: #fff;
  
  .header-content {
    height: 88px;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32px;
    border-bottom: 1px solid #f0f0f0;

    .back-btn {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      
      .iconfont {
        font-size: 40rpx;
        color: #333
      }

      &:active {
        opacity: 0.7;
      }
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32px;
      font-weight: bold;
      pointer-events: none;
    }

    .right-btn {
      position: absolute;
      right: 32rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      font-size: 28px;
      color: #333     
      .right-icon {
        width: 32rpx;
        height: 32px;
      }
    }
  }
}
</style> 