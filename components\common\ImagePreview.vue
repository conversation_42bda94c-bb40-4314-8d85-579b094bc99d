<template>
  <view v-if="visible" class="image-preview-overlay" @click="handleOverlayClick">
    <view class="image-preview-container">
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="closePreview">
        <text class="close-icon">×</text>
      </view>

      <!-- 图片计数器 -->
      <view v-if="images.length > 1" class="image-counter">
        {{ currentIndex + 1 }} / {{ images.length }}
      </view>

      <!-- 当前显示的图片 -->
      <view class="image-wrapper" v-if="images && images.length > 0 && images[currentIndex]">
        <image
          :src="images[currentIndex]"
          class="preview-image"
          mode="aspectFit"
          @click.stop
          @longpress="saveImage(images[currentIndex])"
          @error="onImageError"
          @load="onImageLoad"
        />
      </view>

      <!-- 左右切换按钮 -->
      <view v-if="images.length > 1" class="nav-buttons">
        <view
          class="nav-btn prev-btn"
          @click.stop="prevImage"
          :class="{ disabled: currentIndex === 0 }"
        >
          <text class="nav-icon">‹</text>
        </view>
        <view
          class="nav-btn next-btn"
          @click.stop="nextImage"
          :class="{ disabled: currentIndex === images.length - 1 }"
        >
          <text class="nav-icon">›</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  images: {
    type: Array,
    default: () => []
  },
  initialIndex: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['close', 'change'])

// 当前图片索引
const currentIndex = ref(0)

// 监听初始索引变化
watch(() => props.initialIndex, (newIndex) => {
  currentIndex.value = newIndex
}, { immediate: true })

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible) {
    console.log('图片预览打开，图片列表:', props.images)
    console.log('初始索引:', props.initialIndex)
    // 阻止背景滚动
    // #ifdef H5
    document.body && (document.body.style.overflow = 'hidden')
    // #endif
  } else {
    // 恢复背景滚动
    // #ifdef H5
    document.body && (document.body.style.overflow = '')
    // #endif
  }
})



// 上一张图片
const prevImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
    emit('change', currentIndex.value)
  }
}

// 下一张图片
const nextImage = () => {
  if (currentIndex.value < props.images.length - 1) {
    currentIndex.value++
    emit('change', currentIndex.value)
  }
}

// 关闭预览
const closePreview = () => {
  emit('close')
}

// 点击遮罩层关闭
const handleOverlayClick = () => {
  closePreview()
}

// 长按保存图片
const saveImage = (imageUrl) => {
  uni.showActionSheet({
    itemList: ['保存图片', '取消'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 保存图片到相册
        uni.downloadFile({
          url: imageUrl,
          success: (downloadRes) => {
            if (downloadRes.statusCode === 200) {
              uni.saveImageToPhotosAlbum({
                filePath: downloadRes.tempFilePath,
                success: () => {
                  uni.showToast({
                    title: t('toast.saved') || 'Enregistré avec succès',
                    icon: 'success'
                  })
                },
                fail: () => {
                  uni.showToast({
                    title: t('toast.saveFailed') || 'Échec de l\'enregistrement',
                    icon: 'error'
                  })
                }
              })
            }
          },
          fail: () => {
            uni.showToast({
              title: t('toast.downloadFailed') || 'Échec du téléchargement',
              icon: 'error'
            })
          }
        })
      }
    }
  })
}

// 图片加载成功
const onImageLoad = (e) => {
  console.log('图片加载成功:', e)
}

// 图片加载失败
const onImageError = (e) => {
  console.log('图片加载失败:', e)
}
</script>

<style lang="less" scoped>
.image-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn {
  position: absolute;
  top: 60rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  
  .close-icon {
    color: #fff;
    font-size: 60rpx;
    font-weight: 300;
    line-height: 1;
  }
}

.image-counter {
  position: absolute;
  top: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  z-index: 10;
}

.image-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.preview-image {
  max-width: calc(100vw - 80rpx);
  max-height: calc(100vh - 200rpx);
  width: auto;
  height: auto;
  display: block;
  object-fit: contain;
}

.nav-buttons {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx;
  pointer-events: none;
  z-index: 10;
}

.nav-btn {
  width: 100rpx;
  height: 100rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  transition: all 0.3s ease;
  
  &:active {
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(0.95);
  }
  
  &.disabled {
    opacity: 0.3;
    pointer-events: none;
  }
  
  .nav-icon {
    color: #fff;
    font-size: 60rpx;
    font-weight: bold;
    line-height: 1;
  }
}

.prev-btn {
  .nav-icon {
    margin-left: -8rpx;
  }
}

.next-btn {
  .nav-icon {
    margin-right: -8rpx;
  }
}
</style>
