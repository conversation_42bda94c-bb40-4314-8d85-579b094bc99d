<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Google Map</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- 引入iconfont字体 -->
    <style>
      @font-face {
        font-family: "iconfont";
        src: url('../iconfont/iconfont.woff2') format('woff2'),
             url('../iconfont/iconfont.woff') format('woff'),
             url('../iconfont/iconfont.ttf') format('truetype');
      }
    </style>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html,
      body {
        height: 100%;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #fff;
      }

      .container {
        height: 100vh;
        display: flex;
        flex-direction: column;
      }

      /* 状态栏 */
      .status-bar {
        height: 40px;
        background-color: #fff;
      }

      /* 头部样式 - 与其他页面保持一致 */
      .header {
        padding: 5px 16px 0;
        background-color: #fff;
      }

      .header-content {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 44px;
      }

      .back-btn {
        position: absolute;
        left: 0;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-family: "iconfont";
        font-size: 20px;
        color: #333;
      }

      .back-btn:before {
        content: "\e88e"; /* 使用iconfont的左箭头图标 */
        transform: rotate(180deg); /* 旋转180度变成左箭头 */
        display: inline-block;
        font-size: 28px;
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #000;
        pointer-events: none;
      }

      /* 搜索区域样式 */
      .search-section {
        padding: 0 16px 10px;
        display: flex;
        align-items: center;
        gap: 8px;
        background: #FFFFFF;
      }

      .search-bar {
        flex: 1;
        height: 40px;
        background-color: #F5F5F5;
        border-radius: 6px;
        display: flex;
        align-items: center;
        padding: 0 12px;
        cursor: pointer;
      }

      .search-icon {
        width: 18px;
        height: 18px;
        margin-right: 6px;
      }

      .search-input {
        flex: 1;
        height: 100%;
        font-size: 14px;
        color: #333;
        border: none;
        background: transparent;
        outline: none;
      }

      .search-input::placeholder {
        color: #999;
      }

      .menu-btn {
        width: 40px;
        height: 40px;
        background-color: #F5F5F5;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }

      .menu-icon {
        width: 20px;
        height: 20px;
      }

      /* 地图区域 */
      .map-area {
        flex: 1;
        position: relative;
      }

      #map {
        width: 100%;
        height: 100%;
      }

      /* 地图控制按钮 */
      .map-controls {
        position: absolute;
        right: 16px;
        bottom: 40px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        z-index: 1000;
      }

      .control-btn {
        width: 40px;
        height: 40px;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
        cursor: pointer;
        z-index: 1000;
      }

      .control-icon {
        width: 20px;
        height: 20px;
      }

      .watch-icon {
        font-size: 14px;
      }

      .control-btn.active {
        background-color: #4CAF50;
        color: white;
      }

      /* 充电桩详情卡片 */
      .station-card {
        position: fixed;
        left: 8px;
        right: 8px;
        bottom: -150px;
        background: #F7F7F7;
        border-radius: 6px;
        padding: 12px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        transition: transform 0.3s ease;
        cursor: pointer;
        z-index: 10001; /* 确保弹窗在最上层 */
      }

      .station-card.show {
        transform: translateY(-160px);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
      }

      .station-name {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }

      .close-btn {
        font-size: 16px;
        color: #999;
        padding: 2px;
        cursor: pointer;
      }

      .card-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
      }

      .left-info {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .parking {
        background: #F5F5F5;
        padding: 1px 4px;
        font-size: 12px;
        color: #666;
        border-radius: 2px;
      }

      .free-text {
        font-size: 12px;
        color: #666;
      }

      .divider {
        font-size: 12px;
        color: #333;
        margin-left: 4px;
      }

      .status {
        font-size: 12px;
      }

      .status-text {
        color: #00C853;
      }

      .total {
        color: #999;
      }

      .price-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 6px;
      }

      .normal-price {
        flex-shrink: 0;
        max-width: 120px;
      }

      .normal-price .amount {
        font-size: 16px;
        color: #FF0000;
        font-weight: 600;
      }

      .normal-price .currency {
        font-size: 12px;
        color: #FF0000;
        margin-left: 2px;
      }

      .normal-price .unit {
        font-size: 10px;
        color: #FF0000;
        margin-left: 2px;
      }

      .vip-section {
        display: flex;
        align-items: center;
        gap: 3px;
        flex-shrink: 0;
        min-width: 80px;
        max-width: 100px;
      }

      .vip-icon {
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        opacity: 1 !important;
        visibility: visible !important;
        transition: none;
      }

      .vip-price {
        font-size: 10px;
        color: #B8860B;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .distance {
        display: flex;
        align-items: center;
        gap: 2px;
        background: #F5F5F5;
        padding: 2px 4px;
        border-radius: 8px;
        font-size: 10px;
        color: #333;
        flex-shrink: 0;
        min-width: 50px;
        white-space: nowrap;
      }

      .location-icon {
        width: 12px;
        height: 12px;
        flex-shrink: 0;
        opacity: 1 !important;
        visibility: visible !important;
        transition: none;
      }

      /* 隐藏Google Maps的所有弹窗和控件 */
      .gm-style-iw,
      .gm-style-iw-c,
      .gm-style-iw-d,
      .gm-style-iw-t,
      .gm-ui-hover-effect,
      .gm-style .gm-style-iw,
      .gm-style .gm-style-iw-c,
      .gm-style .gm-style-iw-d,
      .gm-style .gm-style-iw-t,
      .gm-style-cc,
      .gmnoprint,
      .gm-bundled-control,
      .gm-fullscreen-control,
      .gm-svpc,
      .gm-control-active,
      .gm-err-container,
      .gm-err-content,
      .gm-err-icon,
      .gm-err-title,
      .gm-err-message,
      .gm-err-autocomplete,
      .pac-container,
      .pac-item,
      .pac-icon,
      .pac-item-query,
      .pac-matched {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
      }

      /* 隐藏Google Maps的版权信息和条款链接 */
      .gm-style-cc a,
      .gm-style-cc span,
      .gm-style-mtc,
      .gm-style-pbc {
        display: none !important;
      }

      /* 确保地图容器覆盖所有内容 */
      #map {
        position: relative;
        z-index: 1;
      }
    </style>
    <script>
      // 定义uni-app的调试函数，避免__f__ is not defined错误
      if (typeof __f__ === 'undefined') {
        window.__f__ = function(type, at, ...args) {
          if (type === 'log') {
            console.log(...args);
          } else if (type === 'error') {
            console.error(...args);
          } else if (type === 'warn') {
            console.warn(...args);
          }
        };
      }

      // 覆盖可能的弹窗函数，静默处理
      window.alert = function(message) {
        console.log('Alert blocked:', message);
        return true;
      };

      window.confirm = function(message) {
        console.log('Confirm blocked:', message);
        return true;
      };

      window.prompt = function(message, defaultText) {
        console.log('Prompt blocked:', message);
        return defaultText || '';
      };

      let map;
      let userMarker;
      let chargingStationMarkers = [];
      let markerCluster;
      let isWatchingLocation = false;
      let selectedStation = null;
      let userHeading = 0; // 用户方向角度
      
      // 多语言支持
      // 从URL参数获取语言设置
      const urlParams = new URLSearchParams(window.location.search);
      let currentLanguage = urlParams.get('lang') || 'fr'; // 默认法语
      const translations = {
        en: {
          locatingPosition: 'Locating your position...',
          searchStations: 'Search charging stations'
        },
        fr: {
          locatingPosition: 'Localisation en cours...',
          searchStations: 'Rechercher des stations de recharge'
        }
      };
      
      // 获取翻译文本
      const t = (key) => {
        return translations[currentLanguage]?.[key] || translations.en[key] || key;
      };

      // 更新UI文本
      const updateUITexts = () => {
        // 更新搜索框占位符
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
          searchInput.placeholder = t('searchStations');
          console.log('🌐 已更新搜索框占位符为:', t('searchStations'));
        }

        // 更新定位文本
        const locatingTextElement = document.getElementById('locating-text');
        if (locatingTextElement) {
          locatingTextElement.textContent = t('locatingPosition');
          console.log('🌐 已更新定位文本为:', t('locatingPosition'));
        }
      };


	      // 🌐 Centering control flags
	      // 始终以用户位置作为地图中心（避免被其他逻辑改动）
	      const KEEP_USER_CENTER = true;
	      // 首次已按用户位置居中过
	      let firstUserCenterApplied = false;
	      // 最近一次设置的中心点
	      let lastCenter = null;
	      // 开启中心变化日志
	      let centerChangeLoggerAttached = false;

	      // 默认缩放级别（数值越小，范围越大）
	      const DEFAULT_ZOOM = 14;


      // ✅ 封装通信函数，兼容 H5 和 APP
      function postToApp(payload) {
        const msg = JSON.stringify(payload);
        console.log('发送消息到APP:', msg)

        if (typeof uni !== 'undefined' && uni.postMessage) {
          uni.postMessage({ data: msg })
        } else {
          console.warn('uni对象不可用')
        }
      }

      // ✅ 页面交互函数
      function goBack() {
        console.log('返回按钮被点击')
        postToApp({
          type: 'goBack',
          data: {}
        })
      }

      function goToSearch() {
        console.log('搜索按钮被点击')
        postToApp({
          type: 'goToSearch',
          data: {}
        })
      }

      function goToStationList() {
        console.log('列表按钮被点击')
        postToApp({
          type: 'goToStationList',
          data: {}
        })
      }

      function goToMyLocation() {
        console.log('定位按钮被点击')
        postToApp({
          type: 'goToMyLocation',
          data: {}
        })

        // 本地也执行定位
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const pos = {
                lat: position.coords.latitude,
                lng: position.coords.longitude,
              };

              console.log('APP地图定位成功:', pos, '精度:', position.coords.accuracy + 'm');

              if (map) {
                map.setCenter(pos);
                map.setZoom(DEFAULT_ZOOM);
              }

              updateUserLocation(pos);
            },
            (error) => {
              console.error('APP地图定位失败:', error);
            },
            {
              enableHighAccuracy: true, // 启用高精度GPS定位
              timeout: 15000, // 与其他页面保持一致的超时时间
              maximumAge: 0 // 不使用缓存位置
            }
          );
        }
      }

      function toggleWatchLocation() {
        isWatchingLocation = !isWatchingLocation;
        const btn = document.getElementById('watchLocationBtn');

        if (isWatchingLocation) {
          btn.classList.add('active');
          btn.querySelector('.watch-icon').textContent = '📍';
        } else {
          btn.classList.remove('active');
          btn.querySelector('.watch-icon').textContent = '📌';
        }

        console.log('实时定位切换:', isWatchingLocation)
        postToApp({
          type: 'toggleWatchLocation',
          data: { isWatching: isWatchingLocation }
        })
      }



      function goToStationDetail() {
        if (selectedStation) {
          console.log('跳转到充电站详情:', selectedStation)
          postToApp({
            type: 'goToStationDetail',
            data: selectedStation
          })
        }
      }

      function hideStationDetail(event) {
        if (event) {
          event.stopPropagation();
        }

        const card = document.getElementById('stationCard');
        if (card) {
          card.classList.remove('show');

          // 重置强制样式
          card.style.transform = '';
          card.style.display = '';
          card.style.visibility = '';
          card.style.opacity = '';

          console.log('APP环境：隐藏充电站详情，重置样式');
        }

        postToApp({
          type: 'hideStationDetail',
          data: {}
        })
      }

      function showStationDetail(station) {
        console.log('APP环境：显示充电站详情', station);
        selectedStation = station;

        // 检查卡片元素是否存在
        const card = document.getElementById('stationCard');
        if (!card) {
          console.error('APP环境：找不到stationCard元素');
          return;
        }

        // 更新卡片信息
        const stationName = document.getElementById('stationName');
        const stationType = document.getElementById('stationType');
        const availableCount = document.getElementById('availableCount');
        const totalCount = document.getElementById('totalCount');
        const normalPrice = document.getElementById('normalPrice');
        const vipPrice = document.getElementById('vipPrice');
        const stationDistance = document.getElementById('stationDistance');

        if (stationName) stationName.textContent = station.name || 'Charging Station';
        if (stationType) stationType.textContent = station.type || 'GBT DC';
        if (availableCount) availableCount.textContent = station.available || 0;
        if (totalCount) totalCount.textContent = station.total || 0;
        if (normalPrice) normalPrice.textContent = station.price || 0;
        if (vipPrice) vipPrice.textContent = (station.vipPrice || 0) + ' fafc/kWh';
        if (stationDistance && stationDistance.firstChild) {
          stationDistance.firstChild.textContent = station.distance || '0km';
        }

        // 显示卡片
        console.log('APP环境：添加show类到卡片');
        card.classList.add('show');
        console.log('APP环境：卡片类名:', card.className);

        // 强制设置样式确保显示
        card.style.display = 'block';
        card.style.visibility = 'visible';
        card.style.opacity = '1';
        card.style.transform = 'translateY(-160px)';
        card.style.zIndex = '9999';

        console.log('APP环境：强制设置卡片样式完成');
        console.log('APP环境：卡片当前样式:', {
          display: card.style.display,
          visibility: card.style.visibility,
          opacity: card.style.opacity,
          transform: card.style.transform,
          zIndex: card.style.zIndex
        });
      }

      // ✅ 更新用户位置标记（并保持地图中心与小蓝点一致）
      function updateUserLocation(location) {
        // 坐标有效性检查
        const isValid = location && typeof location.lat === 'number' && typeof location.lng === 'number' && !isNaN(location.lat) && !isNaN(location.lng);
        if (!isValid) {
          console.warn('⚠️ 无效的用户位置，忽略:', location);
          return;
        }

        console.log('📍 updateUserLocation 调用，目标坐标:', location);

        if (userMarker) {
          userMarker.setPosition(location);
          console.log('📍 已更新现有用户标记到:', location);
        } else {
          // 创建用户位置图标函数
          function createUserIcon(heading = 0) {
            return {
              url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="36" height="36" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="18" cy="18" r="17" fill="#4285F4" opacity="0.3"/>
                  <circle cx="18" cy="18" r="9" fill="#4285F4" stroke="#fff" stroke-width="3"/>
                  <g transform="rotate(${heading} 18 18)">
                    <path d="M18 6 L24 15 L12 15 Z" fill="#fff"/>
                  </g>
                </svg>
              `),
              scaledSize: new google.maps.Size(36, 36),
              anchor: new google.maps.Point(18, 18)
            };
          }

          userMarker = new google.maps.Marker({
            position: location,
            map: map,
            title: "Your Location",
            icon: createUserIcon(userHeading),
            zIndex: 999
          });
          console.log('🆕 已创建用户位置标记:', location);

          // 启动设备方向监听
          if (window.DeviceOrientationEvent) {
            window.addEventListener('deviceorientation', function(event) {
              if (event.alpha !== null) {
                userHeading = event.alpha;
                if (userMarker) {
                  userMarker.setIcon(createUserIcon(userHeading));
                }
              }
            });
          }
        }

        if (map) {
          const before = map.getCenter() ? map.getCenter().toJSON() : null;
          console.log('🛰️ setCenter 前，地图中心:', before, 'zoom:', map.getZoom());

          // 首帧强制以用户为中心
          if (KEEP_USER_CENTER && !firstUserCenterApplied) {
            map.setCenter(location);
            if (map.getZoom() < DEFAULT_ZOOM) map.setZoom(DEFAULT_ZOOM);
            firstUserCenterApplied = true;
            lastCenter = location;
            console.log('🎯 首帧已居中到用户位置:', location);
          } else {
            // 后续也保持居中（如果中心已被外部改动）
            if (!lastCenter || lastCenter.lat !== location.lat || lastCenter.lng !== location.lng) {
              map.setCenter(location);
              lastCenter = location;
              console.log('🎯 后续更新，重新居中到用户位置:', location);
            }
          }

          const after = map.getCenter() ? map.getCenter().toJSON() : null;
          console.log('🛰️ setCenter 后，地图中心:', after, 'zoom:', map.getZoom());
        }
      }

      // ✅ 添加充电桩标记
      function addChargingStationMarker(station) {
        const lat = parseFloat(station.latitude);
        const lng = parseFloat(station.longitude);

        if (isNaN(lat) || isNaN(lng)) {
          console.error('无效的坐标:', station);
          return;
        }

        const position = { lat, lng };

        // 使用内联SVG生成“红色点位 + 中央logo”的样式，稳定且不依赖外部资源（APP端web-view）
        const customIcon = {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="40" height="50" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <clipPath id="roundedClip">
                  <circle cx="20" cy="16" r="7"/>
                </clipPath>
              </defs>
              <!-- 红色地图标记背景 -->
              <path d="M20 2 C12 2 6 8 6 16 C6 28 20 48 20 48 S34 28 34 16 C34 8 28 2 20 2 Z"
                    fill="#F44336" stroke="#fff" stroke-width="2"/>
              <!-- 白色圆形背景 -->
              <circle cx="20" cy="16" r="9" fill="#fff"/>
              <!-- logo白色背景圆圈 -->
              <circle cx="20" cy="16" r="8" fill="#fff" stroke="#f0f0f0" stroke-width="1"/>
              <!-- maps_logo图片 (base64编码) - 居中显示 -->
              <image href="data:image/png;base64,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"
                       x="13" y="9" width="14" height="14" clip-path="url(#roundedClip)"/>
            </svg>
          `),
          scaledSize: new google.maps.Size(40, 50),
          anchor: new google.maps.Point(20, 50)
        };

        const marker = new google.maps.Marker({
          position: position,
          title: station.name || '充电站',
          icon: customIcon
        });

        // 添加点击事件
        marker.addListener('click', () => {
          console.log('APP环境：充电桩标记被点击:', station);
          console.log('APP环境：标记位置:', marker.getPosition().toString());
          console.log('APP环境：标记标题:', marker.getTitle());

          // 显示详情卡片
          const stationData = {
            id: station.id,
            name: station.name,
            type: station.connectorType || 'GBT DC',
            available: station.availableCount || station.fastCount || 0,
            total: station.totalCount || ((station.fastCount || 0) + (station.slowCount || 0)),
            price: station.price || 0,
            vipPrice: station.vipPrice || 0,
            distance: formatDistance(station.distance || 0),
            plotNum: station.plotNum || ''
          };

          console.log('APP环境：准备显示详情，数据:', stationData);
          showStationDetail(stationData);

          // 通知APP
          postToApp({
            type: 'stationClick',
            data: station
          });
        });

        console.log('APP环境：标记创建完成，标题:', marker.getTitle());
        chargingStationMarkers.push(marker);
      }



      // ✅ 格式化距离
      function formatDistance(distance) {
        if (typeof distance === 'string' && (distance.includes('km') || distance.includes('m'))) {
          return distance;
        }

        const distanceNum = parseFloat(distance);
        if (isNaN(distanceNum)) return '未知';

        if (distanceNum >= 1000) {
          return (distanceNum / 1000).toFixed(2) + 'km';
        }
        return Math.round(distanceNum) + 'm';
      }

      // ✅ 更新充电桩标记
      function updateChargingStations(stations) {
        console.log('🎯🎯🎯 开始更新充电桩标记');
        console.log('📊 接收到的数据:', stations);
        console.log('📊 数据类型:', typeof stations);
        console.log('📊 是否为数组:', Array.isArray(stations));
        console.log('📊 数据长度:', stations ? stations.length : 'undefined');

        // 清除现有标记和聚合
        if (markerCluster && markerCluster.clearMarkers) {
          markerCluster.clearMarkers();
        }
        chargingStationMarkers.forEach(marker => marker.setMap(null));
        chargingStationMarkers = [];

        if (!stations || !Array.isArray(stations)) {
          console.warn('❌ 无效的充电桩数据:', stations);
          return;
        }

        if (stations.length === 0) {
          console.warn('⚠️ 充电桩数据为空数组');
          return;
        }

        console.log('✅ 开始处理充电桩数据，数量:', stations.length);

        stations.forEach((station, index) => {
          console.log(`处理第${index + 1}个充电桩:`, station);

          if (station.latitude && station.longitude) {
            console.log(`✅ 充电桩${index + 1}坐标有效:`, station.latitude, station.longitude);
            try {
              addChargingStationMarker(station);
              console.log(`✅ 充电桩${index + 1}标记添加成功`);
            } catch (error) {
              console.error(`❌ 充电桩${index + 1}标记添加失败:`, error);
              console.error(`❌ 充电桩${index + 1}数据:`, station);
            }
          } else {
            console.warn(`❌ 充电桩${index + 1}坐标无效:`, {
              latitude: station.latitude,
              longitude: station.longitude,
              station: station
            });
          }
        });

        // 使用聚合器显示标记
        if (chargingStationMarkers.length > 0) {
          console.log('APP环境：准备显示标记，数量:', chargingStationMarkers.length);
          console.log('APP环境：聚合器状态:', !!markerCluster);
          console.log('APP环境：聚合器addMarkers方法:', !!(markerCluster && markerCluster.addMarkers));

          if (markerCluster && markerCluster.addMarkers) {
            // 使用聚合器
            markerCluster.addMarkers(chargingStationMarkers);
            console.log('✅ APP环境：标记已添加到聚合器，数量:', chargingStationMarkers.length);
          } else {
            // 降级方案：直接显示标记
            console.log('⚠️ APP环境：聚合器不可用，使用降级方案');
            chargingStationMarkers.forEach(marker => {
              marker.setMap(map);
              console.log('APP环境：标记已添加到地图:', marker.getTitle());
            });
            console.log('✅ APP环境：使用降级方案显示标记，数量:', chargingStationMarkers.length);
          }

          // 自动调整地图视野以包含所有充电桩
          const bounds = new google.maps.LatLngBounds();

          // 添加用户位置到边界
          if (userMarker) {
            bounds.extend(userMarker.getPosition());
          }

          // 添加所有充电桩位置到边界
          chargingStationMarkers.forEach(marker => {
            bounds.extend(marker.getPosition());
          });

          // 调整地图视野或保持用户中心
          if (KEEP_USER_CENTER) {
            console.log('🛡️ 保持用户中心，跳过 fitBounds，当前中心:', map.getCenter() ? map.getCenter().toJSON() : null);
          } else {
            console.log('📐 执行 fitBounds 以包含所有标记');
            map.fitBounds(bounds, {
              top: 50,
              right: 50,
              bottom: 50,
              left: 50
            });

            // 确保缩放级别在合理范围内
            google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
              if (map.getZoom() < 6) {
                map.setZoom(6);
              }
              if (map.getZoom() > DEFAULT_ZOOM) {
                map.setZoom(DEFAULT_ZOOM);
              }
            });
          }
        }

        console.log(`🎉 成功添加了 ${chargingStationMarkers.length} 个充电桩标记`);
      }

      // ✅ 获取用户实际位置并初始化地图 - 只在获取到真实位置后才创建地图
      function getUserLocationAndInitMap(mapContainer) {
        console.log('📍 等待获取用户真实GPS位置，不创建任何默认地图...');

        // 在地图容器中显示等待状态
        mapContainer.innerHTML = `
          <div style="
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            background-color: #f8f9fa;
            color: #6c757d;
            font-size: 14px;
          ">
            <div style="margin-bottom: 15px; font-size: 24px;">📍</div>
            <div id="locating-text">Locating your position...</div>
          </div>
        `;
        
        // 初始化后立即更新文本为正确的语言
        setTimeout(() => {
          updateUITexts();
        }, 100);

        // 检查是否在APP环境中
        const isInApp = typeof uni !== 'undefined';
        console.log('🔍 检测环境:', isInApp ? 'APP环境' : '浏览器环境');

        if (isInApp) {
          // 在APP环境中，只请求位置，不创建地图
          console.log('📱 APP环境：请求真实用户位置，等待响应');

          // 立即请求APP提供用户位置
          postToApp({
            type: 'requestUserLocation',
            data: {}
          });

          // 备用请求
          setTimeout(() => {
            if (!map) {
              postToApp({
                type: 'getCurrentLocation',
                data: {}
              });
            }
          }, 1000);

          // 如果5秒内没有获取到位置，使用浏览器定位API作为备用
          setTimeout(() => {
            if (!map) {
              console.warn('⚠️ APP位置获取超时，尝试浏览器定位');
              tryBrowserGeolocation(mapContainer);
            }
          }, 5000);

        } else {
          // 在浏览器环境中，直接使用浏览器地理定位API
          console.log('🌐 浏览器环境：使用地理定位API获取真实位置');
          tryBrowserGeolocation(mapContainer);
        }
      }



      // ✅ 尝试使用浏览器地理定位
      function tryBrowserGeolocation(mapContainer) {
        if (navigator.geolocation) {
          console.log('🌐 尝试浏览器地理定位...');

          navigator.geolocation.getCurrentPosition(
            (position) => {
              // 成功获取用户位置
              const userLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude,
              };
              console.log('✅ 浏览器成功获取用户位置:', userLocation);
              console.log('📍 位置精度:', position.coords.accuracy + 'm');

              // 检查是否是中东地区坐标
              const isInMiddleEast = userLocation.lat >= 15 && userLocation.lat <= 35 && userLocation.lng >= 40 && userLocation.lng <= 65;
              if (isInMiddleEast) {
                console.error('🚨 浏览器定位返回中东地区坐标，拒绝使用！');
                console.error('🚨 可能原因：VPN影响、网络代理');
                console.error('🚨 坐标详情:', userLocation);
                showLocationError(mapContainer, 'Location appears to be affected by VPN or proxy. Please disable VPN and try again.');
                return;
              }

              // 清除等待状态，直接用用户真实位置创建地图
              mapContainer.innerHTML = '';
              console.log('🗺️ 浏览器定位成功，创建地图');
              createMapWithUserLocation(mapContainer, userLocation);
            },
            (error) => {
              // 获取位置失败
              console.error('❌ 浏览器地理定位失败:', error.message);
              showLocationError(mapContainer, error.message);
            },
            {
              enableHighAccuracy: true, // 启用高精度GPS定位
              timeout: 10000, // 10秒超时
              maximumAge: 0 // 不使用缓存位置，确保获取最新位置
            }
          );
        } else {
          // 浏览器不支持地理定位
          console.error('❌ 浏览器不支持地理定位');
          showLocationError(mapContainer, 'Browser does not support geolocation');
        }
      }

      // ✅ 显示位置获取错误
      function showLocationError(mapContainer, errorMessage) {
        if (mapContainer) {
          mapContainer.innerHTML = `
            <div style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100%;
              background-color: #f5f5f5;
              color: #e74c3c;
              font-size: 16px;
              text-align: center;
              padding: 20px;
            ">
              <div style="margin-bottom: 20px;">❌</div>
              <div>Unable to get your location</div>
              <div style="font-size: 12px; margin-top: 10px; color: #999;">
                ${errorMessage}
              </div>
              <div style="font-size: 12px; margin-top: 10px; color: #999;">
                Please check location permission settings
              </div>
            </div>
          `;
        }
      }

      // ✅ 使用用户真实位置创建地图
      function createMapWithUserLocation(mapContainer, userLocation) {
        console.log('🗺️ 使用用户真实位置创建地图');
        console.log('📍 用户位置坐标:', userLocation);

        // 创建地图，直接以用户位置为中心
        map = new google.maps.Map(mapContainer, {
          center: userLocation,
          zoom: DEFAULT_ZOOM, // 默认缩放级别，可展示更多范围
          minZoom: 2,
          maxZoom: 20,
          mapTypeControl: false,
          streetViewControl: false,
          fullscreenControl: false,
          zoomControl: false,
          scaleControl: false,
          rotateControl: false,
          panControl: false,
          disableDefaultUI: true,
          gestureHandling: 'greedy',
          clickableIcons: false,
          keyboardShortcuts: false,
          styles: [
            {
              featureType: "poi",
              elementType: "labels",
              stylers: [{ visibility: "off" }]
            },
            {
              featureType: "poi",
              elementType: "geometry",
              stylers: [{ visibility: "off" }]
            }
          ]
        });

        // 绑定中心变化日志（仅绑定一次）
        if (!centerChangeLoggerAttached) {
          centerChangeLoggerAttached = true;
          map.addListener('center_changed', () => {
            const c = map.getCenter();
            if (c) {
              const j = c.toJSON();
              console.log('🛰️ map center_changed ->', j);
              lastCenter = j;
            }
          });
        }

        console.log('✅ 地图创建完成，中心位置:', userLocation);

        // 立即添加用户位置标记（内部会setCenter确保一致）
        updateUserLocation(userLocation);

        // 地图加载完成后的处理
        google.maps.event.addListenerOnce(map, 'idle', function() {
          console.log('✅ 地图完全加载完成');
          console.log('🎯 地图当前中心(Idle时):', map.getCenter().toJSON());
          console.log('🎯 firstUserCenterApplied:', firstUserCenterApplied);

          setupMapEvents();
          initializeLocationServices();
          loadMarkerClusterer();
        });
      }

      // ✅ 兼容旧版本的地图创建函数
      function createMapWithLocation(mapContainer, centerLocation, zoomLevel) {
        createMapWithUserLocation(mapContainer, centerLocation);
      }

      // ✅ 安全的地图初始化函数 - 只负责准备环境，不创建地图
      function initMap() {
        console.log('🗺️ 开始准备地图环境...');

        // 检查Google Maps API是否加载
        if (typeof google === 'undefined' || !google.maps) {
          console.warn('⚠️ Google Maps API未加载，延迟重试...');
          setTimeout(initMap, 1000);
          return;
        }

        // 等待DOM完全加载
        if (document.readyState !== 'complete') {
          console.log('⏳ 等待DOM加载完成...');
          document.addEventListener('DOMContentLoaded', initMap);
          return;
        }

        // 检查地图容器是否存在
        const mapContainer = document.getElementById("map");
        if (!mapContainer) {
          console.error('❌ 地图容器不存在，延迟重试...');
          setTimeout(initMap, 500);
          return;
        }

        // 检查容器是否有尺寸
        const rect = mapContainer.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
          console.warn('⚠️ 地图容器尺寸为0，延迟重试...');
          setTimeout(initMap, 500);
          return;
        }

        console.log('✅ 地图环境准备完成，尺寸:', rect.width, 'x', rect.height);

        try {
          // 只获取用户位置，不创建任何默认地图
          console.log('📍 开始获取用户真实位置，不创建默认地图');
          getUserLocationAndInitMap(mapContainer);

        } catch (error) {
          console.error('❌ 地图环境准备失败:', error);

          // 通知APP初始化失败
          postToApp({
            type: 'mapInitError',
            data: { error: error.message }
          });
          return;
        }
      }

      // ✅ 加载MarkerClusterer库
      function loadMarkerClusterer() {
        console.log('🔗 开始加载MarkerClusterer库...');

        // 检查是否已经加载过
        if (window.MarkerClusterer) {
          console.log('✅ MarkerClusterer已存在');
          initializeMarkerClusterer();
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://unpkg.com/@google/markerclustererplus@4.0.1/dist/markerclustererplus.min.js';
        script.onload = function() {
          console.log('✅ MarkerClusterer库加载完成');
          initializeMarkerClusterer();
        };
        script.onerror = function() {
          console.warn('⚠️ MarkerClusterer库加载失败，使用普通标记');
        };
        document.head.appendChild(script);
      }

      // ✅ 初始化MarkerClusterer
      function initializeMarkerClusterer() {
        if (window.MarkerClusterer && map) {
          markerCluster = new window.MarkerClusterer(map, [], {
            gridSize: 60,        // 聚合网格大小
            maxZoom: 15,         // 最大聚合缩放级别
            minimumClusterSize: 2, // 最少2个点才聚合
            styles: [
              {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                  <svg width="50" height="50" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="25" cy="25" r="22" fill="#2196F3" stroke="#fff" stroke-width="3"/>
                  </svg>
                `),
                height: 50,
                width: 50,
                textColor: 'white',
                textSize: 16,
                fontWeight: 'bold'
              }
            ]
          });
          console.log('✅ MarkerClusterer初始化完成');
        }
      }

      // ✅ 设置地图事件
      function setupMapEvents() {
        if (!map) return;

        // 地图点击事件
        map.addListener('click', (event) => {
          postToApp({
            type: 'mapClick',
            data: {
              lat: event.latLng.lat(),
              lng: event.latLng.lng()
            }
          });
        });

        console.log('✅ 地图事件设置完成');
      }

      // ✅ 初始化位置相关服务
      function initializeLocationServices() {
        if (!map) return;

        // 获取当前地图中心位置
        const currentCenter = map.getCenter();
        const centerData = {
          lat: currentCenter.lat(),
          lng: currentCenter.lng()
        };

        // ✅ 通知APP地图初始化完成，传递实际的地图中心位置
        postToApp({
          type: "mapReady",
          data: {
            center: centerData,
            zoom: map.getZoom(),
            timestamp: Date.now()
          }
        });

        // 检查是否在APP环境中
        const isInApp = typeof uni !== 'undefined';

        if (isInApp) {
          // 在APP环境中，地图已经通过用户位置初始化，不需要额外请求
          console.log('📱 APP环境：地图已使用用户位置初始化');
        }

        // ✅ 通知 APP 用户位置更新（如果已经有用户标记）
        if (userMarker) {
          const userPos = userMarker.getPosition();
          postToApp({
            type: "userLocationUpdate",
            data: {
              lat: userPos.lat(),
              lng: userPos.lng()
            }
          });
        }

        // ✅ 主动请求充电桩数据
        setTimeout(() => {
          console.log('🔄 主动请求充电桩数据');
          postToApp({
            type: "requestChargingStations",
            data: {}
          });
        }, 500);
      }

      // ✅ 接收来自APP的消息
      function receiveFromApp(data) {
        console.log('🎯🎯🎯 收到APP消息!');
        console.log('📨 消息类型:', data.type);
        console.log('📨 完整消息:', data);

        try {
          if (data.type === 'updateChargingStations') {
            console.log('🔄 处理充电桩数据更新');
            console.log('📊 充电桩数据:', data.stations);
            updateChargingStations(data.stations);

          } else if (data.type === 'updateLanguage') {
            console.log('🌐 收到语言更新消息');
            currentLanguage = data.language || 'en';
            console.log('✅ 语言已更新为:', currentLanguage);

            // 更新所有UI文本
            updateUITexts();
            
          } else if (data.type === 'updateUserLocation' || data.type === 'userLocationUpdate') {
            console.log('📍 收到APP发送的用户位置数据');
            const location = data.location || data.data;
            if (location && location.lat && location.lng) {
              console.log('✅ 收到用户真实GPS位置:', location);
              console.log('📍 位置坐标: 纬度', location.lat, '经度', location.lng);

              // 检查是否是中东地区坐标
              const isInMiddleEast = location.lat >= 15 && location.lat <= 35 && location.lng >= 40 && location.lng <= 65;
              if (isInMiddleEast) {
                console.error('🚨 检测到中东地区坐标，拒绝使用！');
                console.error('🚨 可能原因：VPN影响、网络代理、或API Key地理限制');
                console.error('🚨 坐标详情:', location);
                return; // 拒绝使用这个位置
              }

              // 如果地图还没有初始化，立即使用用户真实位置创建地图
              if (!map) {
                console.log('🗺️ 首次创建地图，直接以用户位置为中心');
                const mapContainer = document.getElementById("map");
                if (mapContainer) {
                  // 清除等待状态
                  mapContainer.innerHTML = '';
                  // 使用用户真实位置创建地图，高缩放级别显示详细信息
                  createMapWithUserLocation(mapContainer, location);
                }
              } else {
                // 地图已存在，更新用户位置（updateUserLocation会自动居中地图）
                console.log('🎯 地图已存在，更新用户位置标记');
                updateUserLocation(location);
              }
            } else {
              console.warn('⚠️ 收到的用户位置数据格式不正确:', data);
              console.log('📊 原始数据:', data);
            }

          } else if (data.type === 'centerToLocation') {
            console.log('🎯 处理地图中心定位');
            if (map && data.location) {
              // 使用updateUserLocation来确保地图中心和用户标记保持一致
              updateUserLocation(data.location);
            }

          } else {
            console.warn('❓ 未知的消息类型:', data.type);
          }
        } catch (error) {
          console.error('❌ 处理APP消息失败:', error);
          console.error('❌ 错误详情:', error.stack);
        }
      }

      // ✅ 将receiveFromApp函数暴露到全局作用域
      window.receiveFromApp = receiveFromApp;

      // ✅ 全局错误处理 - 静默处理所有错误
      window.onerror = function(msg, url, line, col, error) {
        console.error('🚨 全局错误:', { msg, url, line, col, error });

        // 静默处理，不显示任何弹窗或错误信息
        return true; // 阻止默认错误处理
      };

      // ✅ Promise错误处理 - 静默处理
      window.addEventListener('unhandledrejection', function(event) {
        console.error('🚨 未处理的Promise错误:', event.reason);

        // 阻止默认的错误处理，避免显示弹窗
        event.preventDefault();
      });
    </script>
    <!-- ✅ 先加载 uni-webview-js -->
    <script src="https://unpkg.com/@dcloudio/uni-webview-js@0.0.3/index.js"></script>

    <!-- ✅ 延迟加载 Google Maps 脚本 -->
    <script>
      // 确保DOM和uni对象都准备好后再加载Google Maps
      document.addEventListener('DOMContentLoaded', function() {
        console.log('📄 DOM加载完成');

        // 初始化UI文本
        updateUITexts();

        // 等待uni对象加载
        let checkCount = 0;
        const checkUni = setInterval(() => {
          checkCount++;

          if (typeof uni !== 'undefined' || checkCount > 20) {
            clearInterval(checkUni);

            if (typeof uni !== 'undefined') {
              console.log('✅ uni对象已加载');
            } else {
              console.warn('⚠️ uni对象加载超时，继续加载地图');
            }

            // 加载Google Maps API
            loadGoogleMaps();
          }
        }, 200);
      });

      function loadGoogleMaps() {
        console.log('🌍 开始加载Google Maps API...');

        // 检查是否已经加载过
        if (typeof google !== 'undefined' && google.maps) {
          console.log('✅ Google Maps API已存在，直接初始化');
          initMap();
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc&libraries=marker&callback=initMap';
        script.async = true;
        script.defer = true;

        script.onload = function() {
          console.log('✅ Google Maps API脚本加载完成');
        };

        script.onerror = function(error) {
          console.error('❌ Google Maps API脚本加载失败:', error);

          // 静默处理错误，不显示错误信息
          console.error('Google Maps API脚本加载失败，但不显示错误信息');

          // 通知APP加载失败（但不显示弹窗）
          if (typeof postToApp === 'function') {
            postToApp({
              type: 'mapLoadError',
              data: { error: 'Google Maps API脚本加载失败', silent: true }
            });
          }
        };

        // 设置超时
        setTimeout(() => {
          if (typeof google === 'undefined') {
            console.error('❌ Google Maps API加载超时');
            script.onerror(new Error('加载超时'));
          }
        }, 15000);

        document.head.appendChild(script);
      }
    </script>
  </head>
  <body>
    <div class="container">
      <!-- 状态栏 -->
      <div class="status-bar"></div>

      <!-- 头部 -->
      <div class="header">
        <div class="header-content">
          <div class="back-btn" onclick="goBack()"></div>
          <div class="title">Map</div>
        </div>
      </div>

      <!-- 搜索栏 -->
      <div class="search-section">
        <div class="search-bar" onclick="goToSearch()">
          <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="11" cy="11" r="8"></circle>
            <path d="m21 21-4.35-4.35"></path>
          </svg>
          <input type="text" placeholder="Search charging stations" class="search-input" readonly id="search-input" />
        </div>
        <div class="menu-btn" onclick="goToStationList()">
          <svg class="menu-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="8" y1="6" x2="21" y2="6"></line>
            <line x1="8" y1="12" x2="21" y2="12"></line>
            <line x1="8" y1="18" x2="21" y2="18"></line>
            <line x1="3" y1="6" x2="3.01" y2="6"></line>
            <line x1="3" y1="12" x2="3.01" y2="12"></line>
            <line x1="3" y1="18" x2="3.01" y2="18"></line>
          </svg>
        </div>
      </div>

      <!-- 地图区域 -->
      <div class="map-area">
        <div id="map"></div>

        <!-- 地图控制按钮 -->
        <div class="map-controls">
          <div class="control-btn location-btn" onclick="goToMyLocation()">
            <svg class="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polygon points="3,11 22,2 13,21 11,13 3,11"></polygon>
            </svg>
          </div>
          <div class="control-btn watch-location-btn" id="watchLocationBtn" onclick="toggleWatchLocation()">
            <span class="watch-icon">📍</span>
          </div>
        </div>
      </div>

      <!-- 充电桩详情卡片 -->
      <div class="station-card" id="stationCard" onclick="goToStationDetail()">
        <div class="card-header">
          <div class="station-name" id="stationName">Arnio charging 01 PIL</div>
          <div class="close-btn" onclick="hideStationDetail(event)">×</div>
        </div>
        <div class="card-info">
          <div class="left-info">
            <div class="parking">P</div>
            <div class="free-text">Free1 hour</div>
            <div class="divider" id="stationType">GBT DC 50KW</div>
          </div>
          <div class="status">
            <span class="status-text">Available <span id="availableCount">10</span></span>
            <span class="total">/<span id="totalCount">24</span></span>
          </div>
        </div>
        <div class="price-section">
          <div class="normal-price">
            <span class="amount" id="normalPrice">250</span>
            <span class="currency">FCFA</span>
            <span class="unit">/kWh</span>
          </div>
          <div class="vip-section">
            <svg class="vip-icon" viewBox="0 0 24 24" fill="gold" style="opacity: 1; visibility: visible;">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26"></polygon>
            </svg>
            <div class="vip-price" id="vipPrice">248 fafc/kWh</div>
          </div>
          <div class="distance" id="stationDistance">
            4.26km
            <svg class="location-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" style="opacity: 1; visibility: visible;">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
