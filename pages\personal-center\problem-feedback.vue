<template>
    <view class="problem-feedback-container">
        <!-- 头部导航栏 -->
        <view class="fixed-header" :style="{ paddingTop: statusBarHeight + 'px' }">
            <view class="header-content">
                <view class="back-btn" @click="goBack">
                    <text class="iconfont icon-back"></text>
                </view>
                <text class="title">Problem Feedback</text>
            </view>
        </view>
        
        <!-- 内容区域 -->
        <view class="content-wrapper" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
            <!-- 分类选择 -->
            <view class="section classification-section">
                <view class="section-title">{{ $t('feedback.classification') }}</view>
                <view class="classification-options">
                    <view
                        class="option-item"
                        :class="{ active: selectedType === 'functional' }"
                        @click="selectType('functional')"
                    >
                        <text>{{ $t('feedback.functionalSuggestion') }}</text>
                    </view>
                    <view
                        class="option-item"
                        :class="{ active: selectedType === 'bug' }"
                        @click="selectType('bug')"
                    >
                        <text>{{ $t('feedback.bugFeedback') }}</text>
                    </view>
                </view>
                <view class="classification-options second-row">
                    <view
                        class="option-item"
                        :class="{ active: selectedType === 'other' }"
                        @click="selectType('other')"
                    >
                        <text>{{ $t('feedback.otherIssues') }}</text>
                    </view>
                </view>
            </view>
            
            <!-- 反馈描述 -->
            <view class="section">
                <view class="section-title">{{ $t('feedback.feedbackDescription') }}</view>
                <view class="feedback-input-area">
                    <textarea
                        class="feedback-textarea"
                        v-model="feedbackContent"
                        placeholder="Veuillez décrire en détail pour nous aider à résoudre le problème. Maximum 200 caractères."
                        maxlength="200"
                    ></textarea>
                </view>
            </view>
            
            <!-- 图片上传 -->
            <view class="section">
                <view class="image-upload-area">
                    <view class="image-list">
                        <view class="upload-item" @click="chooseImage">
                            <text class="upload-icon">+</text>
                        </view>
                        <view class="image-count">{{ uploadedImages.length }}/3</view>
                    </view>
                </view>
            </view>
            
            <!-- 联系信息 -->
            <view class="section">
                <view class="section-title">{{ $t('feedback.contactInformation') }}</view>
                <view class="contact-input-area">
                    <input
                        class="contact-input"
                        v-model="contactInfo"
                        :placeholder="$t('feedback.pleaseLeaveContactInfo')"
                    />
                </view>
            </view>

            <!-- 提交按钮 -->
            <view class="submit-button" @click="submitFeedback">
                <text>{{ $t('feedback.submit') }}</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const statusBarHeight = ref(0)
const selectedType = ref('functional')
const feedbackContent = ref('')
const uploadedImages = ref([])
const contactInfo = ref('')

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}

const selectType = (type) => {
    selectedType.value = type
}

const chooseImage = () => {
    if (uploadedImages.value.length >= 3) {
        uni.showToast({
            title: 'Maximum 3 images',
            icon: 'none'
        })
        return
    }
    
    uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
            uploadedImages.value.push(res.tempFilePaths[0])
        }
    })
}

const submitFeedback = () => {
    if (!selectedType.value) {
        uni.showToast({
            title: 'Please select a classification',
            icon: 'none'
        })
        return
    }
    
    if (!feedbackContent.value.trim()) {
        uni.showToast({
            title: 'Please enter feedback description',
            icon: 'none'
        })
        return
    }
    
    // 这里可以添加提交反馈的逻辑
    uni.showLoading({
        title: 'Submitting...'
    })
    
    // 模拟提交过程
    setTimeout(() => {
        uni.hideLoading()
        uni.showToast({
            title: 'Feedback submitted successfully',
            icon: 'success'
        })
        
        // 提交成功后返回上一页
        setTimeout(() => {
            uni.navigateBack()
        }, 1500)
    }, 1000)
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.problem-feedback-container {
    min-height: 100vh;
    background-color: #ffffff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 32rpx;
}

.section {
    margin-bottom: 40rpx;
    
    .section-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 24rpx;
    }
}

.classification-section {
    margin-top: 20rpx;
}

.classification-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    
    .option-item {
        padding: 20rpx 30rpx;
        background-color: #f5f5f5;
        border-radius: 16rpx;
        
        text {
            font-size: 28rpx;
            color: #333;
        }
        
        &.active {
            background-color: #e1f2ff;
            
            text {
                color: #0086ff;
            }
        }
    }
    
    &.second-row {
        margin-top: 20rpx;
    }
}

.feedback-input-area {
    position: relative;
    
    .feedback-textarea {
        width: 100%;
        height: 200rpx;
        background-color: #f5f5f5;
        border-radius: 16rpx;
        padding: 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
        color: #666;
    }
}

.image-upload-area {
    .image-list {
        display: flex;
        align-items: center;
        
        .upload-item {
            width: 160rpx;
            height: 160rpx;
            background-color: #f5f5f5;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20rpx;
            
            .upload-icon {
                font-size: 60rpx;
                color: #ccc;
                font-weight: 200;
            }
        }
        
        .image-count {
            font-size: 24rpx;
            color: #999;
        }
    }
}

.contact-input-area {
    .contact-input {
        width: 100%;
        height: 80rpx;
        background-color: #f5f5f5;
        border-radius: 16rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-sizing: border-box;
        color: #666;
    }
}

.submit-button {
    margin-top: 60rpx;
    background-color: #ff4d4f;
    border-radius: 16rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
    }
}
</style> 