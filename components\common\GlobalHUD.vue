<template>
  <view v-if="hud.state.visible" class="hud-root" :class="[`hud-${hud.state.type}`]" @touchmove.stop.prevent>
    <view class="hud-box">
      <view class="hud-icon">
        <view v-if="hud.state.type==='loading'" class="spinner" />
        <view v-else-if="hud.state.type==='success'" class="glyph">✓</view>
        <view v-else class="glyph">✕</view>
      </view>
      <text class="hud-text">{{ hud.state.message }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useGlobalHud } from '../../composables/useHud'
const hud = useGlobalHud()
</script>

<style scoped lang="less">
.hud-root {
  position: fixed; inset: 0; z-index: 999999;
  display: flex; align-items: center; justify-content: center;
  background: transparent; // 纯 HUD，不遮挡交互
  pointer-events: none; // 保持页面可点
}
.hud-box {
  min-width: 200rpx; max-width: 560rpx;
  padding: 20rpx 26rpx;
  background: rgba(0,0,0,0.75);
  border-radius: 18rpx;
  display: flex; align-items: center; gap: 16rpx;
  box-shadow: 0 12rpx 28rpx rgba(0,0,0,.2);
}
.hud-icon { width: 42rpx; height: 42rpx; display:flex; align-items:center; justify-content:center; }
.glyph { color: #fff; font-size: 30rpx; font-weight: 700; }
.hud-text { color:#fff; font-size: 28rpx; line-height: 1.3; }
.spinner {
  width: 36rpx; height: 36rpx; border-radius: 50%;
  border: 4rpx solid rgba(255,255,255,.35);
  border-top-color: #fff; animation: spin 0.8s linear infinite;
}
@keyframes spin { to { transform: rotate(360deg); } }
</style>

