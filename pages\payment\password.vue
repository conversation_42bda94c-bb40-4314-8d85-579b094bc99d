<template>
  <view class="payment-password">
    <!-- 固定头部区域 -->
    <view class="fixed-header">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <!-- 导航栏 -->
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back"></text>
          </view>
          <text class="title">{{ $t('payment.expenseSettlement') }}</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-scroll" :style="{ marginTop: (statusBarHeight + 88) + 'rpx' }">
      <view class="password-container">
        <view class="password-title">
          <text>{{ $t('payment.enterPaymentPassword') }}</text>
        </view>

        <view class="password-input-wrapper" @click="focusPasswordInput">
          <view v-for="(digit, index) in 4" :key="index" class="password-digit"
            :class="{ 'filled': password.length > index }">
            <text v-if="password.length > index">•</text>
          </view>
        </view>

        <view class="forgot-password" @click="handleforgotPassword">
          <text>{{ $t('auth.forgotPassword') }}</text>
        </view>

        <!-- 隐藏的输入框，用于触发系统键盘 -->
        <input type="tel" pattern="[0-9]*" inputmode="numeric" maxlength="4" v-model="password" class="hidden-input"
          ref="passwordInput" @input="handlePasswordInput" :adjust-position="false" confirm-type="done"
          :cursor-spacing="0" :focus="false" :hold-keyboard="true" :confirm-hold="true" />
      </view>
    </view>
    <!-- HUD 局部挂载，确保在小程序端显示 -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { setupNumericKeyboard, numericInputProps } from '@/utils/keyboard.js'
import { payOrder } from '@/api/modules/payment'
import { verifyPaymentPassword } from '@/api/modules/auth'

import GlobalHUD from '@/components/common/GlobalHUD.vue'

import { useGlobalHud } from '@/composables/useHud'

const hud = useGlobalHud()

// 响应式数据
const statusBarHeight = ref(0)
const password = ref('')
const passwordInput = ref(null)

// 生命周期
onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const focusPasswordInput = () => {
  // 使用公共组件方法处理数字键盘
  nextTick(() => {
    setupNumericKeyboard(passwordInput.value)
  })
}

const handlePasswordInput = () => {
  // 密码输入完成后的处理
  if (password.value.length === 4) {
    // 延迟执行，确保UI更新
    setTimeout(() => {
      verifyPassword()
    }, 300)
  }
}

const verifyPassword = async () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const query = currentPage?.$page?.options || {}

  // 订单参数：优先使用 orderId，如果没有则使用 orderNumber
  const orderId = query?.orderId || ''
  const orderNumber = query?.orderNumber || ''
  const orderParam = orderId  // 优先使用 orderId
  const prepaidFees = query?.prepaidFees || query?.amount || ''

  console.log('支付密码页面接收参数:', {
    orderId,
    orderNumber,
    orderParam,
    prepaidFees
  })

  // HUD 加载
  hud.loading('Verifying password...')

  try {
    // 调用后端验证支付密码接口
    // const verifyRes = await verifyPaymentPassword({
    //   payPassword: password.value
    // })

    // if (!verifyRes || verifyRes.code !== 200 || verifyRes.data !== true) {
    //   uni.hideLoading()
    //   uni.showToast({
    //     title: verifyRes?.message || 'Payment password incorrect',
    //     icon: 'none'
    //   })
    //   // 清空密码输入
    //   password.value = ''
    //   return
    // }

    // // 密码验证成功，调用支付接口
    // uni.showLoading({
    //   title: 'Processing payment...',
    //   mask: true
    // })

    console.log('调用支付接口，参数:', {
      orderNum: orderNumber, // 保持原来的逻辑，使用 orderNumber
      paymentMethod: '2',
      prepaidFees,
      type: '0'
    })

    const res = await payOrder({
      orderNum: orderNumber, // 保持原来的逻辑，不修改支付接口参数
      paymentMethod: '2',
      prepaidFees,
      type: '0'
    })

    // 结束HUD
    hud.done()

    if (res && res.code === 200) {
      // 支付成功，跳转到订单详情页面
      console.log('支付接口返回数据:', JSON.stringify(res, null, 2))
      // 直接使用地址栏传递过来的 orderId，不需要从支付接口获取
      const finalOrderId = orderId // 直接使用地址栏的 orderId
      console.log('使用地址栏传递的 orderId:', finalOrderId)

      uni.redirectTo({
        url: `/pages/payment/order-detail?orderId=${finalOrderId}`
      })
    } else {
      // 支付失败，显示错误信息（HUD）
      hud.error(res?.msg || 'Payment failed')
    }
  } catch (e) {
    // 结束HUD
    hud.done()

    console.error('支付失败:', e)
    hud.error(e?.response?.data?.message || e?.message || 'Payment failed')
  }
}

const handleforgotPassword = () => {

  uni.navigateTo({
    url: '/pages/personal-center/payment-password-reset'
  })
}
</script>

<style lang="scss">
@import '@/static/iconfont/iconfont.css';

.payment-password {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0, 0, 0, 0.04);
}

.status-bar {
  background-color: #fff;
  width: 100%;
}

.header {
  background: #fff;
  width: 100%;

  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .back-btn {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;

      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 20rpx;
  background-color: #fff;
}

.password-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 32rpx;
  background-color: #fff;
}

.password-title {
  font-size: 40rpx; // 增大标题字号
  font-weight: 500;
  color: #333;
  margin-bottom: 100rpx; // 增加与输入框的间距
}

.password-input-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 40rpx;
  padding: 40rpx 0; // 增加上下内边距，扩大点击区域
}

.password-digit {
  width: 100rpx; // 增加宽度
  height: 100rpx; // 增加高度
  border-bottom: 4rpx solid #ddd; // 加粗底部边框
  margin: 0 30rpx; // 增加间距
  display: flex;
  justify-content: center;
  align-items: center;

  &.filled {
    border-bottom-color: #333; // 输入后加深底部边框颜色

    text {
      font-size: 80rpx; // 增大圆点大小
      line-height: 80rpx;
      color: #333;
    }
  }
}

.forgot-password {
  align-self: flex-end;
  margin-top: 40rpx; // 增加与输入框的间距
  padding: 20rpx; // 增加点击区域
  z-index: 1;

  text {
    font-size: 32rpx; // 增大字号
    color: #666;
  }
}

.hidden-input {
  position: absolute;
  opacity: 0;
  width: 100%; // 扩大隐藏输入框的点击区域
  height: 100%;
  left: 0;
  top: 0;
}
</style>