/**
 * API接口统一管理
 * 调用Request实例进行网络请求
 */
import Request from '../utils/request'

// 导出Request实例作为默认导出
export default Request

// 导出所有类型定义
export * from './types'

// 导出短信相关接口
export * from './modules/sms'

// 导出认证相关接口
export * from './modules/auth'

// 导出用户相关接口
export * from './modules/user'

// 导出充电桩相关接口
export * from './modules/charging'

//导出vip相关接口
export * from './modules/vip'

// 导出车辆相关接口
export * from './modules/car'

// 导出优惠券相关接口
export * from './modules/coupon'

// 支付相关接口
export * from './modules/payment'

// 版本检测相关接口
export * from './modules/version'
