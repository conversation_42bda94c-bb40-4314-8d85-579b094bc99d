<template>
  <view class="verify-code-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <view class="header">
      <view class="header-content">
        <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
        <text class="title">{{ t('user.verifyCode') || 'Verify Code' }}</text>
      </view>
    </view>

    <view class="description">
      {{ t('user.verifyCodeDesc') || 'Please enter the verification code sent to your mobile number' }}
    </view>

    <view class="form-container">
      <!-- 验证码输入 -->
      <view class="form-item">
        <text class="form-label">{{ t('user.verificationCode') || 'Verification Code' }}</text>
        <input type="number" class="form-input" v-model="verificationCode"
          :placeholder="t('user.enterVerificationCode') || 'Please enter verification code'" maxlength="6"
          @input="validateVerificationCode" />
        <text v-if="errors.verificationCode" class="error-text">{{ errors.verificationCode }}</text>
      </view>

      <!-- 新密码输入 -->
      <view class="form-item" v-if="isVerified">
        <text class="form-label">{{ t('user.newPassword') || 'New Password' }}</text>
        <view class="input-wrapper">
          <input :type="showPassword ? 'text' : 'password'" class="form-input" v-model="newPassword"
            :placeholder="t('user.enterNewPassword') || 'Please enter new password'" maxlength="4"
            @input="validateNewPassword" />
          <text class="iconfont eye-icon" :class="showPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
            @click="togglePassword"></text>
        </view>
        <text v-if="errors.newPassword" class="error-text">{{ errors.newPassword }}</text>
      </view>

      <!-- 确认新密码输入 -->
      <view class="form-item" v-if="isVerified">
        <text class="form-label">{{ t('user.confirmNewPassword') || 'Confirm New Password' }}</text>
        <view class="input-wrapper">
          <input :type="showConfirmPassword ? 'text' : 'password'" class="form-input" v-model="confirmPassword"
            :placeholder="t('user.confirmNewPassword') || 'Please confirm new password'" maxlength="4"
            @input="validateConfirmPassword" />
          <text class="iconfont eye-icon" :class="showConfirmPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
            @click="toggleConfirmPassword"></text>
        </view>
        <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
      </view>

      <view class="button-group">
        <button v-if="!isVerified" class="verify-btn" @click="verifyCode" :disabled="!isVerifyFormValid">
          {{ t('user.verify') || 'Verify Code' }}
        </button>
        <button v-if="isVerified" class="reset-btn" @click="resetPasswordAction" :disabled="!isResetFormValid">
          {{ t('user.resetPassword') || 'Reset Password' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { verifySmsCode, resetPassword } from '@/api'

// 国际化
const { t } = useI18n()

// 状态栏高度
const statusBarHeight = ref(0)

// 页面参数
const phoneNumber = ref('')
const type = ref('') // 'reset' 或其他类型

// 表单数据
const verificationCode = ref('')
const newPassword = ref('')
const confirmPassword = ref('')
const isVerified = ref(false)

// 错误信息
const errors = ref({
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 是否显示密码
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 获取页面参数
onMounted(() => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })
  
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  phoneNumber.value = options.phoneNumber || ''
  type.value = options.type || ''
  
  console.log('页面参数:', {
    phoneNumber: phoneNumber.value,
    type: type.value,
    options: options
  })
})

// 表单验证
const validateVerificationCode = () => {
  const value = verificationCode.value
  if (!value) {
    errors.value.verificationCode = 'Verification code is required'
  } else if (!/^\d{6}$/.test(value)) {
    errors.value.verificationCode = 'Verification code must be 6 digits'
  } else {
    errors.value.verificationCode = ''
  }
}

const validateNewPassword = () => {
  const value = newPassword.value
  if (!value) {
    errors.value.newPassword = 'New password is required'
  } else if (!/^\d{4}$/.test(value)) {
    errors.value.newPassword = 'Password must be exactly 4 digits'
  } else {
    errors.value.newPassword = ''
  }
  // 同时验证确认密码
  if (confirmPassword.value) {
    validateConfirmPassword()
  }
}

const validateConfirmPassword = () => {
  const value = confirmPassword.value
  if (!value) {
    errors.value.confirmPassword = 'Please confirm your password'
  } else if (value !== newPassword.value) {
    errors.value.confirmPassword = 'Passwords do not match'
  } else {
    errors.value.confirmPassword = ''
  }
}

// 表单是否有效
const isVerifyFormValid = computed(() => {
  return verificationCode.value && !errors.value.verificationCode
})

const isResetFormValid = computed(() => {
  return newPassword.value &&
    confirmPassword.value &&
    !errors.value.newPassword &&
    !errors.value.confirmPassword
})

// 切换密码可见性
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 验证验证码
const verifyCode = async () => {
  validateVerificationCode()

  if (!isVerifyFormValid.value) {
    uni.showToast({
      title: 'Please enter a valid verification code',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: 'Verifying...',
      mask: true
    })

    // 准备验证码验证参数
    const verifyData = {
      phonenumber: phoneNumber.value,
      smsCode: verificationCode.value,
      clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
      grantType: "sms",
    }

    console.log('验证码验证参数:', verifyData)

    // 调用验证码验证API
    const response = await verifySmsCode(verifyData)

    console.log('验证码验证响应:', response)

    uni.hideLoading()

    if (response && (response.code === 200 || response.code === "200")) {
      uni.showToast({
        title: 'Verification successful',
        icon: 'success'
      })

      // 验证成功，显示密码重置表单
      isVerified.value = true
    } else {
      throw new Error(response?.message || 'Verification failed')
    }

  } catch (error) {
    uni.hideLoading()
    console.error('验证码验证失败:', error)

    let errorMessage = 'Verification failed'
    if (error.response) {
      errorMessage = error.response.data?.message || errorMessage
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none'
    })
  }
}

// 重置密码
const resetPasswordAction = async () => {
  validateNewPassword()
  validateConfirmPassword()

  if (!isResetFormValid.value) {
    uni.showToast({
      title: 'Please fill in all fields correctly',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: 'Resetting password...',
      mask: true
    })

    // 准备重置密码参数
    const resetData = {
      accountPassword: newPassword.value,
      accountPhone: phoneNumber.value, // 忘记密码时使用手机号
      operateType:'1'
    }

    console.log('重置密码参数:', resetData)

    // 调用重置密码API
    const response = await resetPassword(resetData)

    console.log('重置密码响应:', response)

    uni.hideLoading()

    if (response && (response.code === 200 || response.code === "200")) {
      uni.showToast({
        title: 'Password reset successful',
        icon: 'success'
      })

      // 重置成功，跳转到登录页面
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/auth/login/index'
        })
      }, 1500)
    } else {
      throw new Error(response?.message || 'Password reset failed')
    }

  } catch (error) {
    uni.hideLoading()
    console.error('密码重置失败:', error)

    let errorMessage = 'Password reset failed'
    if (error.response) {
      errorMessage = error.response.data?.message || errorMessage
    } else if (error.message) {
      errorMessage = error.message
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none'
    })
  }
}
</script>

<style lang="less">
.verify-code-container {
  min-height: 100vh;
  background-color: #fff;
  padding: 0 40rpx;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 32rpx 0;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
  }

  .back-icon {
    position: absolute;
    left: 0;
    font-size: 32rpx;
    color: #000;
    font-weight: bold;
  }

  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
  }

  .description {
    font-size: 28rpx;
    color: #999;
    line-height: 1.5;
    text-align: left;
    margin: 40rpx 0 48rpx 0;
  }

  .form-container {
    width: 100%;

    .form-item {
      margin-bottom: 32rpx;

      .form-label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 24rpx;
        display: block;
      }

      .form-input {
        width: 100%;
        height: 96rpx;
        background: #fff;
        border: 2rpx solid #eee;
        border-radius: 8rpx;
        padding: 0 32rpx;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        box-sizing: border-box;

        &::placeholder {
          color: #999;
          font-size: 28rpx;
        }

        &:focus {
          border-color: #f23030;
        }
      }

      .error-text {
        color: #f23030;
        font-size: 24rpx;
        margin-bottom: 16rpx;
        display: block;
      }

      .input-wrapper {
        position: relative;

        .form-input {
          padding-right: 80rpx;
        }

        .eye-icon {
          position: absolute;
          right: 32rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 32rpx;
          color: #999;
          z-index: 1;
          padding: 10rpx;
          cursor: pointer;
          user-select: none;
          
          &:hover {
            color: #f23030;
          }
          
          &.icon-mimayanjing {
            color: #f23030;
          }
          
          &.icon-yanjingmima {
            color: #999;
          }
        }
      }
    }

    .button-group {
      margin-top: 48rpx;

      .verify-btn,
      .reset-btn {
        width: 100%;
        height: 96rpx;
        background: #f23030;
        color: #fff;
        border-radius: 18rpx;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        border: none;

        &:disabled {
          background: #ccc;
          color: #999;
        }
      }
    }
  }
}
</style>