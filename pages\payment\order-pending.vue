<template>
  <view class="order-pending">
    <!-- 固定头部区域 -->
    <view class="fixed-header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px', background: '#FF4D4F' }"></view>
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back" style="color:#fff;"></text>
          </view>
          <text class="title">{{ $t('payment.orderDetails') }}</text>
          <image src="/static/images/ARNIO.png" class="logo" mode="aspectFit" />
        </view>
      </view>
    </view>

    <!-- 内容区域 - 设置paddingTop防止被头部遮挡 -->
    <view class="content" :style="{ paddingTop: (statusBarHeight + 88) + 'px' }">
      <!-- 订单状态提示 -->
      <view class="status-tip">
        <image src="/static/images/charging-pile-red.png" class="status-icon" />
        <view class="status-info">
          <text class="status-title">{{ $t('payment.orderPendingPayment') }}</text>
          <text class="status-desc">{{ $t('payment.insufficientBalance') }}</text>
        </view>
      </view>

      <!-- 充电站信息 -->
      <view class="station-card">
        <text class="station-name">Arnio charging 01 PIL</text>
        <view class="gun-info">
          <text class="gun-label">{{ $t('charging.chargingGun') }} 001</text>
          <text class="gun-code">( 20444510001 )</text>
        </view>
        <view class="divider"></view>
        <view class="vehicle-info">
          <text class="vehicle-label">{{ $t('charging.chargingVehicle') }}</text>
          <text class="vehicle-id">LDP4A9609035269</text>
        </view>
      </view>

      <!-- 费用明细 -->
      <view class="price-card">
        <view class="total-amount">
          <text class="amount">280 F</text>
          <view class="collapse-btn" @click="toggleDetails">
            <text>{{ $t('payment.collapseDetails') }}</text>
            <text class="arrow-icon">^</text>
          </view>
        </view>
        <view class="price-details" v-if="showDetails">
          <view class="detail-item">
            <text class="detail-label">{{ $t('payment.chargingAmount') }}</text>
            <text class="detail-value">247 F</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">{{ $t('payment.electricityBill') }}</text>
            <text class="detail-value">220 F</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">{{ $t('payment.serviceFee') }}</text>
            <text class="detail-value">30 F</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">{{ $t('payment.memberOffers') }}</text>
            <text class="detail-value discount">-9 F</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">{{ $t('payment.coupon') }}</text>
            <text class="detail-value discount">-12 F</text>
          </view>
          <view class="detail-item total">
            <text class="detail-label">{{ $t('payment.totalPaid') }}</text>
            <text class="detail-value">280 F</text>
          </view>
        </view>
        <view class="charged-info">
          <text class="charged-label">{{ $t('payment.chargedAmount') }}</text>
          <text class="charged-value">29.425kWh</text>
        </view>
      </view>

      <!-- 充电信息 -->
      <view class="info-card">
        <text class="info-title">{{ $t('payment.chargingInformation') }}</text>
        <view class="info-grid">
          <view class="info-row">
            <text class="info-label">{{ $t('payment.orderNumber') }}</text>
            <text class="info-value">551246413106</text>
          </view>
          <view class="info-row">
            <text class="info-label">{{ $t('payment.startTime') }}</text>
            <text class="info-value">2025-06-20 10:06:00</text>
          </view>
          <view class="info-row">
            <text class="info-label">{{ $t('payment.endTime') }}</text>
            <text class="info-value">2025-06-20 13:20:00</text>
          </view>
          <view class="info-row">
            <text class="info-label">{{ $t('payment.chargingPower') }}</text>
            <text class="info-value">50kWh</text>
          </view>
          <view class="info-row">
            <text class="info-label">Charging Type</text>
            <text class="info-value">GBT DC</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button class="pay-btn" @click="handlePay">{{ $t('payment.goToPay') }}</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

import { getRouteParams } from '@/utils/route'
const statusBarHeight = ref(0)
const showDetails = ref(true)
const orderInfo = ref({
  id: '551246413106',
  status: 'pending',
  station: 'Arnio charging 01 PIL',
  gun: '001',
  gunCode: '20444510001',
  vehicle: 'LDP4A9609035269',
  totalAmount: 280,
  chargingAmount: 247,
  electricityBill: 220,
  serviceFee: 30,
  memberOffer: -9,
  coupon: -12,
  chargedAmount: '29.425',
  startTime: '2025-06-20 10:06:00',
  endTime: '2025-06-20 13:20:00',
  power: '50kWh',
  type: 'GBT DC'
})

onMounted(() => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight

  // 尝试获取路由参数
  const params = getRouteParams(['orderId'])
  const orderId = (params && params.orderId) ? params.orderId : '551246413106'

  // 根据orderId可以调用API获取详情，这里使用模拟数据
  console.log('订单ID:', orderId)

  // 在实际应用中，这里应该是API请求获取订单详情
  // fetchOrderDetail(orderId)
})

// 折叠/展开详情
const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 去支付：跳转到支付密码页，传入必要参数
const handlePay = () => {
  // 使用接口返回的 orderNumber 作为唯一订单号
  const orderNumber = orderInfo.value?.id || '551246413106'
  const prepaidFees = orderInfo.value?.totalAmount || 0
  const url = `/pages/payment/password?orderNumber=${encodeURIComponent(orderNumber)}&prepaidFees=${encodeURIComponent(prepaidFees)}`
  uni.navigateTo({ url })
}
</script>

<style lang="scss" scoped>
@import '@/static/iconfont/iconfont.css';

.order-pending {
  min-height: 100vh;
  background-color: #FF4D4F;
  position: relative;
  display: flex;
  flex-direction: column;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.header {
  width: 100%;
  background-color: #FF4D4F;

  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;

    .back-btn {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: white;
      pointer-events: none;
    }

    .logo {
      position: absolute;
      right: 32rpx;
      height: 60rpx;
      width: 60rpx;
    }
  }
}

.content {
  flex: 1;
  background-color: #FF4D4F;
  display: flex;
  flex-direction: column;
  padding-bottom: 180rpx;  // 留出底部按钮的空间
}

.status-tip {
  padding: 20rpx 32rpx;
  display: flex;
  align-items: center;

  .status-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: 16rpx;
  }

  .status-info {
    flex: 1;
    display: flex;
    flex-direction: column;

    .status-title {
      font-size: 36rpx;
      font-weight: 600;
      color: white;
      margin-bottom: 8rpx;
    }

    .status-desc {
      font-size: 24rpx;
      color: rgba(255,255,255,0.9);
      line-height: 1.4;
    }
  }
}

.station-card {
  margin: 16rpx 32rpx;
  padding: 32rpx;
  background-color: white;
  border-radius: 16rpx;

  .station-name {
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }

  .gun-info {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .gun-label {
      font-size: 28rpx;
      color: #333;
    }

    .gun-code {
      font-size: 28rpx;
      color: #999;
      margin-left: 8rpx;
    }
  }

  .divider {
    height: 1rpx;
    background-color: #f0f0f0;
    margin: 16rpx 0;
  }

  .vehicle-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .vehicle-label {
      font-size: 28rpx;
      color: #999;
    }

    .vehicle-id {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.price-card {
  margin: 16rpx 32rpx;
  background-color: white;
  border-radius: 16rpx;

  .total-amount {
    padding: 32rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .amount {
      font-size: 48rpx;
      font-weight: 600;
      color: #333;
    }

    .collapse-btn {
      display: flex;
      align-items: center;

      text {
        font-size: 28rpx;
        color: #666;
      }

      .arrow-icon {
        margin-left: 8rpx;
        transition: transform 0.3s;
        transform: rotate(180deg);
      }
    }
  }

  .price-details {
    padding: 0 32rpx 32rpx;
    background-color: #f9f9f9;

    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-label {
        font-size: 28rpx;
        color: #666;
      }

      .detail-value {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;

        &.discount {
          color: #FF4D4F;
        }
      }

      &.total {
        margin-top: 16rpx;
        border-top: 1rpx dashed #e0e0e0;
        padding-top: 16rpx;

        .detail-label, .detail-value {
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .charged-info {
    padding: 24rpx 32rpx;
    border-top: 1rpx solid #f0f0f0;
    display: flex;
    align-items: center;

    .charged-label {
      font-size: 28rpx;
      color: #666;
      margin-right: 16rpx;
    }

    .charged-value {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

.info-card {
  margin: 16rpx 32rpx;
  padding: 32rpx;
  background-color: white;
  border-radius: 16rpx;

  .info-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
    display: block;
  }

  .info-grid {
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .info-row {
      display: flex;
      flex-direction: row;

      .info-label {
        width: 220rpx;
        font-size: 28rpx;
        color: #999;
      }

      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background-color: white;
  box-shadow: 0 -4rpx 16rpx rgba(0,0,0,0.05);

  .pay-btn {
    height: 96rpx;
    background-color: #FF4D4F;
    color: white;
    font-size: 36rpx;
    font-weight: 600;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
  }
}
</style>