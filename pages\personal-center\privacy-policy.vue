<template>
    <view class="privacy-policy-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ $t('legal.privacyPolicy') }}</text>
                </view>
            </view>
        </view>
        
        <!-- 隐私政策内容 -->
        <view class="content-container">
            <view class="content-section">
                <view class="section-title">
                    <text>1. {{ $t('legal.introduction') }}</text>
                </view>
                <view class="section-text">
                    <text>{{ $t('legal.introductionText') }}</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>2. Collection of Your Information</text>
                </view>
                <view class="section-text">
                    <text>We may collect information about you in a variety of ways. The information we may collect via the Application includes:</text>
                </view>
                <view class="section-list">
                    <text>• Personal Data: Personally identifiable information, such as your name, email address, telephone number, and demographic information that you voluntarily give to us when you register with the Application or when you choose to participate in various activities related to the Application.</text>
                    <text>• Derivative Data: Information our servers automatically collect when you access the Application, such as your IP address, browser type, operating system, access times, and the pages you have viewed directly before and after accessing the Application.</text>
                    <text>• Mobile Device Data: Device information, such as your mobile device ID, model, and manufacturer, and information about the location of your device, if you access the Application from a mobile device.</text>
                </view>
            </view>
            
            <view class="content-section">
                <view class="section-title">
                    <text>3. Use of Your Information</text>
                </view>
                <view class="section-text">
                    <text>Having accurate information about you permits us to provide you with a smooth, efficient, and customized experience. Specifically, we may use information collected about you via the Application to:</text>
                </view>
                <view class="section-list">
                    <text>• Create and manage your account.</text>
                    <text>• Process payments and refunds.</text>
                    <text>• Administer sweepstakes, promotions, and contests.</text>
                    <text>• Assist law enforcement and respond to subpoenas.</text>
                    <text>• Compile anonymous statistical data and analysis for use internally or with third parties.</text>
                    <text>• Deliver targeted advertising, newsletters, and other information regarding promotions and the Application to you.</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const statusBarHeight = ref(0)

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.privacy-policy-container {
    min-height: 100vh;
    background-color: #ffffff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.content-container {
    padding: 32rpx;
    
    .content-section {
        margin-bottom: 40rpx;
        
        .section-title {
            margin-bottom: 16rpx;
            
            text {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
            }
        }
        
        .section-text {
            margin-bottom: 16rpx;
            
            text {
                font-size: 28rpx;
                color: #666;
                line-height: 1.5;
            }
        }
        
        .section-list {
            text {
                display: block;
                font-size: 28rpx;
                color: #666;
                line-height: 1.5;
                margin-bottom: 16rpx;
                padding-left: 16rpx;
            }
        }
    }
}
</style> 