/**
 * 简化版网络异常处理
 * 直接使用 uni.showModal，虽然样式无法完全自定义，但可以设置一些属性
 * 支持国际化
 */

import { reactive } from 'vue'

// 直接导入语言包
import frLocale from '../locale/fr.js'
import enLocale from '../locale/en.js'

// 获取当前语言
const getCurrentLocale = () => {
  try {
    // 从本地存储获取
    const savedLocale = uni.getStorageSync('locale')
    if (savedLocale === 'fr' || savedLocale === 'en') {
      return savedLocale
    }
    
    // 获取系统语言
    const systemInfo = uni.getSystemInfoSync()
    const systemLanguage = systemInfo.language || systemInfo.locale
    
    // 根据系统语言设置默认语言
    if (systemLanguage.startsWith('fr')) {
      return 'fr'
    } else {
      return 'en'
    }
  } catch (error) {
    console.warn('获取当前语言失败:', error)
    return 'en' // 默认英语
  }
}

// 获取网络异常文本
const getNetworkErrorTexts = () => {
  const currentLocale = getCurrentLocale()
  console.log('🌐 [国际化] 当前语言:', currentLocale)
  
  let locale
  if (currentLocale === 'fr') {
    locale = frLocale
  } else {
    locale = enLocale
  }
  
  return {
    title: locale.common.networkError || 'Network Error',
    content: locale.common.networkErrorMessage || 'Network connection failed. Please check your network settings and try again.',
    confirmText: locale.common.confirm || 'Confirm'
  }
}

// 网络异常状态
const networkErrorState = reactive({
  visible: false,
  isShowing: false
})

/**
 * 检测是否为网络异常
 */
const isNetworkError = (error: any): boolean => {
  if (!error) return false
  
  // 检查各种网络错误情况
  const errorChecks = [
    // Axios 错误
    error.code === 'NETWORK_ERROR',
    error.code === 'ERR_NETWORK',
    error.message?.includes('Network Error'),
    error.message?.includes('网络错误'),
    
    // uni.request 错误
    error.errMsg?.includes('request:fail'),
    error.errMsg?.includes('timeout'),
    error.errMsg?.includes('network'),
    
    // 自定义网络错误标识
    error.message === 'networkError',
    
    // HTTP 状态码相关
    !error.response && error.request,
    
    // 其他网络相关错误
    error.message?.includes('ERR_NETWORK_CHANGED'),
    error.message?.includes('ERR_INTERNET_DISCONNECTED')
  ]
  
  const result = errorChecks.some(check => check)
  console.log('🔍 [网络检测] 错误分析:', {
    error: error,
    isNetworkError: result,
    errorMessage: error.message || error.errMsg,
    errorCode: error.code
  })
  
  return result
}

/**
 * 显示网络异常弹窗
 */
const showNetworkError = () => {
  // 防止重复显示
  if (networkErrorState.isShowing) {
    console.log('网络异常弹窗已在显示中，跳过重复显示')
    return
  }
  
  console.log('🚨 [弹窗显示] 显示网络异常弹窗')
  networkErrorState.isShowing = true
  
  // 获取国际化文本
  const texts = getNetworkErrorTexts()
  console.log('🌐 [国际化] 使用的文本:', texts)
  
  try {
    if (typeof uni !== 'undefined' && uni.showModal) {
      uni.showModal({
        title: texts.title,
        content: texts.content,
        showCancel: false,
        confirmText: texts.confirmText,
        confirmColor: '#FF6B6B', // 设置确认按钮颜色
        success: () => {
          console.log('✅ [弹窗显示] 用户点击确认，弹窗已关闭')
          hideNetworkError()
        },
        fail: (err) => {
          console.error('❌ [弹窗显示] uni.showModal调用失败:', err)
          hideNetworkError()
        }
      })
    } else {
      console.error('❌ [弹窗显示] uni.showModal不可用')
      hideNetworkError()
    }
  } catch (error) {
    console.error('❌ [弹窗显示] uni.showModal调用异常:', error)
    hideNetworkError()
  }
}

/**
 * 隐藏网络异常弹窗
 */
const hideNetworkError = () => {
  console.log('🔍 [弹窗隐藏] 隐藏网络异常弹窗')
  networkErrorState.visible = false
  networkErrorState.isShowing = false
}

/**
 * 处理网络异常
 */
const handleNetworkError = (error: any) => {
  if (isNetworkError(error)) {
    console.log('🚨 [网络异常] 检测到网络异常，显示弹窗:', error)
    showNetworkError()
  }
}

/**
 * 全局网络异常处理 Hook
 */
export const useSimpleNetworkError = () => {
  return {
    networkErrorState,
    showNetworkError,
    hideNetworkError,
    handleNetworkError,
    isNetworkError,
    // 导出用于测试的函数
    getNetworkErrorTexts,
    getCurrentLocale
  }
}

// 全局单例模式
let globalNetworkErrorInstance: ReturnType<typeof useSimpleNetworkError> | null = null

export const useGlobalSimpleNetworkError = () => {
  if (!globalNetworkErrorInstance) {
    globalNetworkErrorInstance = useSimpleNetworkError()
    
    // 注册到全局对象，供其他模块使用
    globalThis.__simpleNetworkErrorModule = globalNetworkErrorInstance
  }
  
  return globalNetworkErrorInstance
}

// 导出类型
export type SimpleNetworkErrorInstance = ReturnType<typeof useSimpleNetworkError>
