<template>
  <view class="toast-container" v-if="visible">
    <view class="toast-content" :class="[`toast-${type}`, position]">
      <image v-if="showIcon" :src="iconSrc" class="toast-icon" />
      <text class="toast-text">{{ message }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'Toast',
  props: {
    // Toast是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // Toast类型: default, success, error, warning, loading
    type: {
      type: String,
      default: 'default',
      validator: function(value) {
        return ['default', 'success', 'error', 'warning', 'loading'].includes(value)
      }
    },
    // Toast消息
    message: {
      type: String,
      default: ''
    },
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: true
    },
    // Toast位置: top, center, bottom
    position: {
      type: String,
      default: 'center',
      validator: function(value) {
        return ['top', 'center', 'bottom'].includes(value)
      }
    },
    // 自动关闭延时（毫秒）
    duration: {
      type: Number,
      default: 2000
    },
    // 是否显示遮罩
    mask: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      timer: null
    }
  },
  computed: {
    // 根据类型获取对应的图标
    iconSrc() {
      const iconMap = {
        success: '/static/images/success.png',
        error: '/static/images/tip-2.png',
        warning: '/static/images/tip.png',
        loading: '', // 加载图标使用CSS实现
        default: ''
      }
      return iconMap[this.type]
    }
  },
  watch: {
    visible(val) {
      if (val && this.duration > 0) {
        this.startTimer()
      }
    }
  },
  methods: {
    // 开始定时器
    startTimer() {
      this.clearTimer()
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          this.close()
        }, this.duration)
      }
    },
    
    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
    },
    
    // 关闭Toast
    close() {
      this.clearTimer()
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  },
  beforeUnmount() {
    this.clearTimer()
  }
}
</script>

<style lang="scss" scoped>
.toast-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  
  &.has-mask {
    pointer-events: auto;
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.toast-content {
  max-width: 70%;
  padding: 24rpx 32rpx;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: toast-fade-in 0.3s ease;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  
  &.top {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.bottom {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
  }
  
  &.toast-success {
    background-color: rgba(7, 193, 96, 0.9);
  }
  
  &.toast-error {
    background-color: rgba(255, 77, 79, 0.9);
  }
  
  &.toast-warning {
    background-color: rgba(255, 153, 0, 0.9);
  }
  
  &.toast-loading {
    background-color: rgba(0, 0, 0, 0.7);
    
    &::after {
      content: '';
      display: block;
      width: 48rpx;
      height: 48rpx;
      border: 4rpx solid #fff;
      border-radius: 50%;
      border-top-color: transparent;
      animation: toast-loading 0.8s linear infinite;
      margin-bottom: 16rpx;
    }
  }
}

.toast-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
}

.toast-text {
  font-size: 28rpx;
  color: #fff;
  text-align: center;
  line-height: 1.4;
  word-break: break-word;
}

@keyframes toast-fade-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes toast-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 