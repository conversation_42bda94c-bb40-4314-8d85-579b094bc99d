<template>
    <view class="message-settings-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ $t('messageSettings.title') }}</text>
                </view>
            </view>
        </view>
        
        <!-- 总开关设置 -->
        <view class="settings-card">
            <view class="settings-item">
                <text class="settings-label">{{ $t('messageSettings.messageNotification') }}</text>
                <view class="switch-container">
                    <switch 
                        :checked="messageNotification" 
                        @change="toggleMessageNotification" 
                        color="#168CFA"
                    />
                </view>
            </view>
        </view>
        
        <!-- 通知类型标题 -->
        <view class="section-title">
            <text>{{ $t('messageSettings.notificationType') }}</text>
        </view>
        
        <!-- 通知类型设置 -->
        <view class="settings-card">
            <!-- 充电订单提醒 -->
            <view class="settings-item">
                <text class="settings-label">{{ $t('messageSettings.chargingOrderReminder') }}</text>
                <view class="switch-container">
                    <switch 
                        :checked="chargingOrderReminder" 
                        @change="toggleChargingOrderReminder" 
                        color="#168CFA"
                    />
                </view>
            </view>
            
            <!-- 分隔线 -->
            <view class="divider"></view>
            
            <!-- 营销活动 -->
            <view class="settings-item">
                <text class="settings-label">{{ $t('messageSettings.marketingActivities') }}</text>
                <view class="switch-container">
                    <switch 
                        :checked="marketingActivities" 
                        @change="toggleMarketingActivities" 
                        color="#168CFA"
                    />
                </view>
            </view>
            
            <!-- 分隔线 -->
            <view class="divider"></view>
            
            <!-- 系统通知 -->
            <view class="settings-item">
                <text class="settings-label">{{ $t('messageSettings.systemNotification') }}</text>
                <view class="switch-container">
                    <switch 
                        :checked="systemNotification" 
                        @change="toggleSystemNotification" 
                        color="#168CFA"
                    />
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

const statusBarHeight = ref(0)
const messageNotification = ref(true)
const chargingOrderReminder = ref(false)
const marketingActivities = ref(true)
const systemNotification = ref(true)

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
    
    // 这里可以从本地存储或服务器获取用户的消息设置
    loadSettings()
})

const goBack = () => {
    uni.navigateBack()
}

const loadSettings = () => {
    // 模拟从本地存储加载设置
    try {
        const settings = uni.getStorageSync('messageSettings')
        if (settings) {
            const parsedSettings = JSON.parse(settings)
            messageNotification.value = parsedSettings.messageNotification !== undefined ? parsedSettings.messageNotification : true
            chargingOrderReminder.value = parsedSettings.chargingOrderReminder !== undefined ? parsedSettings.chargingOrderReminder : false
            marketingActivities.value = parsedSettings.marketingActivities !== undefined ? parsedSettings.marketingActivities : true
            systemNotification.value = parsedSettings.systemNotification !== undefined ? parsedSettings.systemNotification : true
        }
    } catch (e) {
        console.error('Failed to load message settings:', e)
    }
}

const saveSettings = () => {
    // 保存设置到本地存储
    try {
        const settings = JSON.stringify({
            messageNotification: messageNotification.value,
            chargingOrderReminder: chargingOrderReminder.value,
            marketingActivities: marketingActivities.value,
            systemNotification: systemNotification.value
        })
        uni.setStorageSync('messageSettings', settings)
    } catch (e) {
        console.error('Failed to save message settings:', e)
    }
}

const toggleMessageNotification = (e) => {
    messageNotification.value = e.detail.value
    
    // 如果总开关关闭，所有子开关也应该关闭
    if (!messageNotification.value) {
        chargingOrderReminder.value = false
        marketingActivities.value = false
        systemNotification.value = false
    }
    
    saveSettings()
}

const toggleChargingOrderReminder = (e) => {
    chargingOrderReminder.value = e.detail.value
    
    // 如果有任何子开关打开，总开关也应该打开
    if (chargingOrderReminder.value) {
        messageNotification.value = true
    }
    
    saveSettings()
}

const toggleMarketingActivities = (e) => {
    marketingActivities.value = e.detail.value
    
    // 如果有任何子开关打开，总开关也应该打开
    if (marketingActivities.value) {
        messageNotification.value = true
    }
    
    saveSettings()
}

const toggleSystemNotification = (e) => {
    systemNotification.value = e.detail.value
    
    // 如果有任何子开关打开，总开关也应该打开
    if (systemNotification.value) {
        messageNotification.value = true
    }
    
    saveSettings()
}

// 监听总开关变化
watch(() => messageNotification.value, (newVal) => {
    if (!newVal) {
        // 如果总开关关闭，所有子开关也应该关闭
        chargingOrderReminder.value = false
        marketingActivities.value = false
        systemNotification.value = false
        saveSettings()
    }
})
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.message-settings-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.section-title {
    padding: 32rpx;
    padding-bottom: 16rpx;
    
    text {
        font-size: 28rpx;
        color: #999;
    }
}

.settings-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .settings-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        
        .settings-label {
            font-size: 32rpx;
            color: #333;
            flex: 1;
            padding-right: 16rpx;
        }
        
        .switch-container {
            // 自定义开关样式
            :deep(.uni-switch-input) {
                background-color: #e0e0e0 !important;
            }
            
            :deep(.uni-switch-input.uni-switch-input-checked) {
                background-color: #168CFA !important;
            }
        }
    }
    
    .divider {
        height: 1rpx;
        background-color: #f0f0f0;
        margin-left: 32rpx;
        margin-right: 32rpx;
    }
}
</style> 