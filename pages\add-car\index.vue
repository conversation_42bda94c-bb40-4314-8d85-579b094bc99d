<template>
  <view class="add-car-page" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
    <!-- 顶部状态栏和返回 -->
    <view class="fixed-header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back"></text>
          </view>
          <text class="title">{{ $t('account.addVehicleInfo') }}</text>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-content">
      <!-- 认证车辆部分 -->
      <view class="form-section">
        <view class="section-title">{{ $t('account.certifiedVehicle') }}</view>

        <!-- 扫描按钮 -->
        <view class="scan-button" @click="openScan">
          <image src="/static/images/scan-id.png" class="scan-icon" mode="aspectFit"></image>
          <text class="scan-text">{{ $t('account.scanLicense') }}</text>
        </view>

        <!-- 车牌号输入 -->
        <view class="license-plate-container" @click="focusPlateInput">
          <!-- 显示的格子 -->
          <view class="plate-grid">
            <!-- 欧洲车牌格式: AA-123-BB -->
            <view class="plate-group country-code">
              <view class="plate-char">
                <text v-if="plateNumber.length > 0">{{ plateNumber[0] || '' }}</text>
              </view>
              <view class="plate-char">
                <text v-if="plateNumber.length > 1">{{ plateNumber[1] || '' }}</text>
              </view>
            </view>
            <view class="plate-separator">-</view>
            <view class="plate-group numbers">
              <view class="plate-char">
                <text v-if="plateNumber.length > 2">{{ plateNumber[2] || '' }}</text>
              </view>
              <view class="plate-char">
                <text v-if="plateNumber.length > 3">{{ plateNumber[3] || '' }}</text>
              </view>
              <view class="plate-char">
                <text v-if="plateNumber.length > 4">{{ plateNumber[4] || '' }}</text>
              </view>
            </view>
            <view class="plate-separator">-</view>
            <view class="plate-group region-code">
              <view class="plate-char">
                <text v-if="plateNumber.length > 5">{{ plateNumber[5] || '' }}</text>
              </view>
              <view class="plate-char">
                <text v-if="plateNumber.length > 6">{{ plateNumber[6] || '' }}</text>
              </view>
            </view>
          </view>

          <!-- 隐藏的输入框 -->
          <input
            id="plateInput"
            ref="plateInput"
            v-model="plateNumber"
            maxlength="7"
            type="text"
            class="hidden-input"
            @input="handlePlateInput"
            @focus="handlePlateFocus"
            adjust-position="false"
            hold-keyboard="true"
            cursor-spacing="0"
          />
        </view>
      </view>

      <!-- 车辆类型 -->
      <view class="form-section car-type-section">
        <view class="vehicle-type-row">
          <text class="section-label">{{ $t('account.vehicleType') }}</text>
          <view class="vehicle-type-selector">
            <picker
              :value="carTypeIndex"
              :range="carTypeOptions"
              range-key="label"
              @change="onVehicleTypeChange"
            >
              <view class="vehicle-type-picker">
                <text class="vehicle-type-text">{{ carTypeDisplay }}</text>
                <text class="arrow">▼</text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-button" :class="{ disabled: !isFormValid }" @click="submitForm">
      <text>{{ $t('common.submit') }}</text>
    </view>

    <!-- 自定义弹窗 -->
    <CustomDialog
      :visible="dialogVisible"
      :title="dialogConfig.title"
      :message="dialogConfig.message"
      :type="dialogConfig.type"
      :show-cancel="dialogConfig.showCancel"
      :confirm-text="dialogConfig.confirmText"
      :cancel-text="dialogConfig.cancelText"
      @confirm="handleDialogConfirm"
      @cancel="handleDialogCancel"
      @close="dialogVisible = false"
      @update:visible="dialogVisible = $event"
    />
    <!-- HUD 局部挂载，确保小程序端弹窗可见 -->
    <GlobalHUD />



  </view>
</template>

<script setup>
import GlobalHUD from '@/components/common/GlobalHUD.vue'
import { ref, computed, onMounted, inject, nextTick } from 'vue'
import CheckboxItem from '@/components/CheckboxItem.vue'
import CustomDialog from '@/components/common/CustomDialog.vue'

import { addCar, updateCar } from '@/api'
import { useUserStore } from '@/store/user'
import { useI18n } from 'vue-i18n'

import { useGlobalHud } from '@/composables/useHud'

// 使用国际化
const { t, locale } = useI18n()

// 注入Toast实例
// HUD
const hud = useGlobalHud()

const toast = inject('toast')

// 使用用户状态store
const userStore = useUserStore()

// 响应式数据
const statusBarHeight = ref(20)
const agreedToTerms = ref(true) // 默认同意协议
const isEdit = ref(false)
const carId = ref('')
const carTypeIndex = ref(0)

// 车牌号输入 (7个字符，欧洲格式 AA-123-BB)
const plateNumber = ref('')
const plateInput = ref(null)

// 车辆类型
const carType = ref('0') // 默认小型汽车
const carTypeOptions = [
  { value: '0', label: t('account.smallVehicle') },
  { value: '1', label: t('account.mediumVehicle') },
  { value: '2', label: t('account.largeVehicle') }
]



// 弹窗状态
const dialogVisible = ref(false)
const dialogConfig = ref({
  title: '',
  message: '',
  type: 'default',
  showCancel: true,
  confirmText: '',
  cancelText: ''
})

// 计算属性
const isFormValid = computed(() => {
  // 检查车牌号是否填写完整 (7个字符)
  return plateNumber.value.length >= 7
})

// 计算车牌号
const licensePlate = computed(() => {
  // 格式化为 AA-123-BB 格式
  if (plateNumber.value.length >= 7) {
    const part1 = plateNumber.value.slice(0, 2)
    const part2 = plateNumber.value.slice(2, 5)
    const part3 = plateNumber.value.slice(5, 7)
    return `${part1}-${part2}-${part3}`
  }
  return plateNumber.value
})

// 计算车辆类型显示文本
const carTypeDisplay = computed(() => {
  const option = carTypeOptions.find(opt => opt.value === carType.value)
  return option ? option.label : t('account.smallVehicle')
})

// 生命周期钩子
onMounted(() => {
  // 更新车辆类型选项的国际化文本
  carTypeOptions[0].label = t('account.smallVehicle')
  carTypeOptions[1].label = t('account.mediumVehicle')
  carTypeOptions[2].label = t('account.largeVehicle')

  // 获取状态栏高度
  try {
    const res = uni.getSystemInfoSync()
    statusBarHeight.value = res.statusBarHeight
  } catch (e) {
    console.log('获取状态栏高度失败')
  }

  // 检查用户是否已登录
  if (!userStore.loggedIn || !userStore.getToken) {
    console.log('用户未登录或token不存在，跳转到登录页')
    uni.showToast({
      title: t('error.unauthorized'),
      icon: 'error',
      duration: 2000
    })
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/auth/login/index'
      })
    }, 1500)
    return
  }

  // 获取页面参数
  uni.getStorage({
    key: 'editCarParams',
    success: function(res) {
      const params = res.data
      if (params && params.carId) {
        isEdit.value = true
        carId.value = params.carId

        // 填充车牌号
        if (params.carNum) {
          // 移除破折号，只保留字符
          plateNumber.value = params.carNum.replace(/-/g, '')
        }

        // 设置车辆类型
        if (params.carType) {
          carType.value = params.carType
          carTypeIndex.value = parseInt(params.carType) || 0
        }

        // 清除临时存储
        uni.removeStorage({
          key: 'editCarParams'
        })
      }
    }
  })
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

// 打开扫描
const openScan = () => {
  uni.scanCode({
    success: (res) => {
      // 处理扫描结果
      const scannedPlate = res.result || 'AB123CD'
      // 移除破折号，只保留字符
      plateNumber.value = scannedPlate.replace(/-/g, '').toUpperCase()

      uni.showToast({
        title: t('common.success'),
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: t('error.tryAgain'),
        icon: 'none'
      })
    }
  })
}

// 聚焦车牌号输入框
const focusPlateInput = () => {
  nextTick(() => {
    uni.createSelectorQuery()
      .select('#plateInput')
      .fields({
        context: true
      }, (res) => {
        if (res && res.context) {
          res.context.focus()
        }
      })
      .exec()
  })
}

// 处理车牌号输入
const handlePlateInput = (event) => {
  const value = event.detail.value.toUpperCase() // 自动转为大写
  plateNumber.value = value
}

// 处理车牌号输入框获得焦点
const handlePlateFocus = () => {
  // 输入框获得焦点时的处理
}

// 处理车辆类型选择变化
const onVehicleTypeChange = (event) => {
  const index = event.detail.value
  carTypeIndex.value = index
  carType.value = carTypeOptions[index].value
}

const submitForm = async () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: t('account.pleaseCompleteFields'),
      icon: 'error',
      duration: 2000
    })
    return
  }

  // 检查用户是否已登录
  if (!userStore.loggedIn || !userStore.getToken) {
    console.log('用户未登录或token不存在，跳转到登录页')
    uni.showToast({
      title: t('error.unauthorized'),
      icon: 'error',
      duration: 2000
    })
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/auth/login/index'
      })
    }, 1500)
    return
  }

  // 提交表单逻辑（HUD）
  hud.loading(isEdit.value ? t('account.updating') : t('account.submitting'))

  try {
    let result

    if (isEdit.value) {
      // 更新车辆
      result = await updateCar({
        carId: carId.value,
        carNum: licensePlate.value,
        carType: carType.value
      })
    } else {
      // 添加车辆
      result = await addCar({
        carNum: licensePlate.value,
        carType: carType.value
      })
    }

    // 结束HUD
    hud.done()

    if (result.code === 200) {
      hud.success(isEdit.value ? t('account.vehicleUpdated') : t('account.vehicleAdded'))

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack({
          delta: 1
        })
      }, 1500)
    } else {
      hud.error(result.msg || t('error.unknown'))
    }
  } catch (error) {
    console.error('操作失败', error)

    // 结束HUD
    hud.done()

    // 检查是否是token错误
    if (error.message && (
        error.message.includes('token') ||
        error.message.includes('unauthorized') ||
        error.message.includes('未授权') ||
        error.message.includes('未能读取到有效 token')
      )) {
      hud.error(t('error.unauthorized'))

      // 清除无效的token
      uni.removeStorageSync('token')
      userStore.logout()

      // 跳转到登录页
      setTimeout(() => {
        uni.navigateTo({
          url: '/pages/auth/login/index'
        })
      }, 1500)
    } else {
      hud.error(t('error.unknown'))
    }
  }
}

// 处理弹窗确认
const handleDialogConfirm = () => {
  if (dialogConfig.value.onConfirm) {
    dialogConfig.value.onConfirm()
  }
  dialogVisible.value = false
}

// 处理弹窗取消
const handleDialogCancel = () => {
  if (dialogConfig.value.onCancel) {
    dialogConfig.value.onCancel(new Error('User cancelled'))
  }
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
@import '@/static/iconfont/iconfont.css';

.add-car-page {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: 40rpx;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0,0,0,0.04);
}

.status-bar {
  background-color: #fff;
  width: 100%;
}

.header {
  background: #fff;
  width: 100%;

  .header-content {
    height: 44px;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .back-btn {
      width: 88rpx;
      height: 44px;
      display: flex;
      align-items: center;

      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }
  }
}

.form-content {
  padding: 32rpx;
  flex: 1;
  background-color: #fff;
}

.form-section {
  background: #F7F7F7;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
  }
}

.scan-button {
  width: 100%;
  height: 88rpx;
  background:#1e94ef65;
  border-radius: 34rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding-left: 30rpx;
  box-sizing: border-box;


  .scan-icon {
    width: 36rpx;
    height: 36rpx;
    margin-right: 16rpx;
  }

  .scan-text {
    color: #fff;
    font-size: 28rpx;
    font-weight: 500;
  }
}

.license-plate-container {
  position: relative;
  margin-top: 30rpx;

  .plate-grid {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;

    .plate-group {
      display: flex;
      gap: 6rpx;

      &.country-code, &.region-code {
        .plate-char {
          background-color: #f5f5f5;
        }
      }

      &.numbers {
        .plate-char {
          background-color: #fff;
        }
      }
    }

    .plate-separator {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin: 0 4rpx;
    }

    .plate-char {
      width: 60rpx;
      height: 80rpx;
      border: 1px solid #ddd;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;

      text {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        text-transform: uppercase;
      }
    }
  }

  .hidden-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 1;
  }
}

.car-type-section {
  .vehicle-type-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .section-label {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }

    .vehicle-type-selector {
      .vehicle-type-picker {
        display: flex;
        align-items: center;
        padding: 16rpx 24rpx;
        background: #f8f8f8;
        border-radius: 12rpx;
        border: 1rpx solid #e5e5e5;
        min-width: 200rpx;

        .vehicle-type-text {
          font-size: 28rpx;
          color: #333;
        }

        .arrow {
          font-size: 20rpx;
          color: #999;
          margin-left: 12rpx;
        }
      }
    }
  }
}

.submit-button {
  margin: 40rpx 32rpx;
  height: 100rpx;
  background: #f23030;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 34rpx;
  font-weight: bold;

  &:active {
    background: #d91e1e;
  }

  &.disabled {
    background: #ccc;
    color: #999;
  }
}
</style>