{"name": "Arnio", "appid": "__UNI__0060761", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "renderjs": {"enable": true}, "icons": {"72": "static/logo.png", "96": "static/logo.png", "128": "static/logo.png", "144": "static/logo.png"}, "app-plus": {"icons": {"android": {"hdpi": "static/logo.png", "xhdpi": "static/logo.png", "xxhdpi": "static/logo.png", "xxxhdpi": "static/logo.png"}, "ios": {"appstore": "static/logo.png", "ipad": {"app": "static/logo.png", "app@2x": "static/logo.png", "notification": "static/logo.png", "notification@2x": "static/logo.png", "proapp@2x": "static/logo.png", "settings": "static/logo.png", "settings@2x": "static/logo.png", "spotlight": "static/logo.png", "spotlight@2x": "static/logo.png"}, "iphone": {"app@2x": "static/logo.png", "app@3x": "static/logo.png", "notification@2x": "static/logo.png", "notification@3x": "static/logo.png", "settings@2x": "static/logo.png", "settings@3x": "static/logo.png", "spotlight@2x": "static/logo.png", "spotlight@3x": "static/logo.png"}}}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "renderer": "auto", "nvueCompiler": "uni-app", "optimization": {"subPackages": true}, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Maps": {}, "Webview": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "webView": {"plusrequire": "uni-app", "geolocation": {"enabled": true}}}, "ios": {"privacyDescription": {"NSLocationWhenInUseUsageDescription": "此应用需要访问您的位置信息以显示附近的充电站", "NSLocationAlwaysUsageDescription": "此应用需要访问您的位置信息以显示附近的充电站"}, "dSYMs": false}, "sdkConfigs": {"maps": {"google": {"APIKey_ios": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc", "APIKey_android": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc"}}, "geolocation": {"system": {"__platform__": ["ios", "android"]}, "amap": {"__platform__": ["ios", "android"], "appkey_ios": "", "appkey_android": ""}, "google": {"__platform__": ["ios", "android"], "APIKey_ios": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc", "APIKey_android": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc"}}, "ad": {}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "splashscreen": {"androidStyle": "common"}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于显示附近的充电站"}}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"devServer": {"port": 8080, "disableHostCheck": true}, "title": "French Charging Station", "router": {"mode": "hash"}, "sdkConfigs": {"maps": {"qqmap": {"key": ""}, "google": {"key": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc"}}}}}