/**
 * 充电状态管理器
 * 负责管理充电状态的实时数据获取和更新
 */
import websocketManager from './websocket.js'

class ChargingStatusManager {
  constructor() {
    this.orderId = null
    this.orderNumber = null // 添加orderNumber属性
    this.isMonitoring = false
    this.callbacks = new Map()
    this.lastStatus = null

    // 首包与订阅重试控制
    this.hasReceivedData = false
    this.subscribeAttempts = 0
    this.maxSubscribeAttempts = 5
    this.subscribeRetryTimer = null

    // 添加数据缓存相关属性
    this.cacheKey = 'charging_status_cache'
    this.connectionCacheKey = 'charging_connection_cache'
    this.cacheExpireTime = 30 * 60 * 1000 // 30分钟过期
  }

  /**
   * 开始监控充电状态
   * @param {string} orderId 订单ID
   * @param {object} options 配置选项
   */
  startMonitoring(orderId, options = {}) {
    this.orderId = orderId
    this.orderNumber = options.orderNumber || orderId // 设置orderNumber
    this.isMonitoring = true

    console.log('=== 开始监控充电状态 ===')
    console.log('监控参数 - orderId:', orderId, 'orderNumber:', this.orderNumber)

    // 检查是否已有相同订单的活跃连接
    const wsStatus = websocketManager.getStatus()
    const cachedConnection = this.loadConnectionFromCache()

    if (wsStatus.connected && cachedConnection && cachedConnection.orderId === orderId) {
      console.log('🔄 检测到相同订单的活跃连接，直接恢复监控')
      this.hasReceivedData = cachedConnection.hasReceivedData

      // 立即发送连接状态
      this.emit('connectionStatusChanged', { connected: true, type: 'connected' })

      // 如果有缓存数据，立即发送
      const cachedStatus = this.loadStatusFromCache(orderId)
      if (cachedStatus) {
        console.log('🔄 发送缓存数据到UI')
        this.lastStatus = cachedStatus
        setTimeout(() => {
          this.emit('statusUpdate', cachedStatus)
          this.emit('cacheDataLoaded', cachedStatus)
        }, 100)
      }

      // 重新设置事件监听（因为页面可能重新加载）
      this.setupWebSocketListeners()
      return
    }

    // 尝试从缓存恢复数据
    const cachedStatus = this.loadStatusFromCache(orderId)
    if (cachedStatus) {
      console.log('🔄 使用缓存数据恢复充电状态')
      this.lastStatus = cachedStatus
      // 立即发送缓存数据给UI
      setTimeout(() => {
        this.emit('statusUpdate', cachedStatus)
        this.emit('cacheDataLoaded', cachedStatus)
      }, 100)
    }

    // 重置订阅控制变量
    this.subscribeAttempts = 0
    this.clearSubscribeRetry()

    // 验证必要参数
    if (!orderId && !this.orderNumber) {
      console.error('❌ 缺少订单ID和订单号，无法开始监控')
      this.emit('error', { message: 'Informations de commande manquantes' })
      return
    }

    console.log('✅ 参数验证通过，开始监控充电状态:', orderId)

    // 保存连接状态到缓存
    this.saveConnectionToCache()

    // 先设置为“连接中”状态（等待首包）
    this.emit('connectionStatusChanged', { connected: false, type: 'connecting' })

    // 设置WebSocket事件监听
    this.setupWebSocketListeners()

    // 尝试连接WebSocket
    const wsUrl = options.websocketUrl || `wss://api.example.com/charging/${orderId}`
    websocketManager.connect(wsUrl, {
      header: {
        'Authorization': `Bearer ${options.token || 'mock-token'}`
      }
    })

    // 不再使用模拟数据和轮询模式，只使用WebSocket
    console.log('使用WebSocket实时数据模式')
  }

  /**
   * 停止监控 - 修改为只暂停UI更新，保持连接
   */
  stopMonitoring(clearCache = false) {
    console.log('暂停监控充电状态 (保持连接), clearCache:', clearCache)

    // 保存当前状态到缓存（除非明确要求清除）
    if (!clearCache && this.lastStatus) {
      this.saveStatusToCache(this.lastStatus)
      this.saveConnectionToCache()
    }

    // 如果要求清除缓存，才真正停止连接
    if (clearCache) {
      console.log('清除缓存，完全停止连接')
      this.isMonitoring = false

      // 清理订阅重试
      this.clearSubscribeRetry()

      // 停止WebSocket连接
      websocketManager.close()

      // 清除事件监听
      this.clearWebSocketListeners()

      // 清除缓存和状态
      this.clearStatusCache()
      this.clearConnectionCache()
      this.lastStatus = null
      this.orderId = null
    } else {
      console.log('保持WebSocket连接，只暂停UI更新')
      // 不关闭连接，只标记为暂停状态
      this.isMonitoring = false
    }
  }

  /**
   * 完全停止监控和连接
   */
  forceStopMonitoring() {
    console.log('强制停止监控充电状态')
    this.isMonitoring = false

    // 清理订阅重试
    this.clearSubscribeRetry()

    // 停止WebSocket连接
    websocketManager.close()

    // 清除事件监听
    this.clearWebSocketListeners()

    // 清除所有缓存和状态
    this.clearAllCache()
    this.orderId = null
  }

  /**
   * 获取当前订单号
   * @returns {string|null} 订单号
   */
  getOrderNumber() {
    return this.orderNumber
  }

  /**
   * 手动结束充电
   */
  endCharging() {
    console.log('手动结束充电')

    // 发送结束充电指令（如果WebSocket连接可用）
    if (websocketManager.getStatus().isConnected) {
      websocketManager.send({
        type: 'endCharging',
        orderId: this.orderId,
        timestamp: Date.now()
      })
    }

    // 立即更新状态为已完成
    if (this.lastStatus) {
      this.updateChargingStatus({
        ...this.lastStatus,
        status: 'completed',
        power: {
          ...this.lastStatus.power,
          current: 0
        },
        time: {
          ...this.lastStatus.time,
          remaining: 0
        }
      })
    }

    // 停止监控
    this.stopMonitoring()
  }

  /**
   * 设置WebSocket事件监听
   */
  setupWebSocketListeners() {
    websocketManager.on('connected', this.onWebSocketConnected.bind(this))
    websocketManager.on('message', this.onWebSocketMessage.bind(this))
    websocketManager.on('disconnected', this.onWebSocketDisconnected.bind(this))
    websocketManager.on('error', this.onWebSocketError.bind(this))
    websocketManager.on('reconnecting', this.onWebSocketReconnecting.bind(this))
    websocketManager.on('reconnectFailed', this.onWebSocketReconnectFailed.bind(this))
  }

  /**
   * 清除WebSocket事件监听
   */
  clearWebSocketListeners() {
    websocketManager.off('connected', this.onWebSocketConnected.bind(this))
    websocketManager.off('message', this.onWebSocketMessage.bind(this))
    websocketManager.off('disconnected', this.onWebSocketDisconnected.bind(this))
    websocketManager.off('error', this.onWebSocketError.bind(this))
    websocketManager.off('reconnectFailed', this.onWebSocketReconnectFailed.bind(this))
  }

  /**
   * WebSocket连接成功
   */
  onWebSocketConnected() {
    console.log('🎉 充电状态WebSocket连接成功')

    // 首包未到前，显示“连接中”状态
    this.emit('connectionStatusChanged', { connected: false, type: 'connecting' })

    // 延迟发送订阅消息，确保连接完全建立
    setTimeout(() => {
      // 发送订阅消息 - 根据实际需要调整消息格式
      const subscribeMessage = {
        type: 'subscribe',
        orderId: this.orderId || this.orderNumber, // 优先使用orderId，没有则使用orderNumber
        orderNumber: this.orderNumber,
        dataTypes: ['battery', 'power', 'time', 'cost', 'status'],
        timestamp: Date.now()
      }

      console.log('📤 发送WebSocket订阅消息:', subscribeMessage)
      console.log('订阅参数详情 - orderId:', this.orderId, 'orderNumber:', this.orderNumber)

      if (!this.orderId && !this.orderNumber) {
        console.error('❌ 订阅失败：缺少订单ID和订单号')
        this.emit('error', { message: '订阅失败：订单信息缺失' })
        return
      }

      websocketManager.send(subscribeMessage)

      // 启动订阅重试：若未收到首包数据，每1.5s 重发一次，最多5次
      this.startSubscribeRetry()
    }, 500) // 延迟500ms发送订阅消息
  }
  /**
   * 启动订阅重试
   */
  startSubscribeRetry() {
    this.clearSubscribeRetry()
    this.subscribeRetryTimer = setInterval(() => {
      if (this.hasReceivedData) {
        this.clearSubscribeRetry()
        return
      }
      if (this.subscribeAttempts >= this.maxSubscribeAttempts) {
        console.warn('订阅重试次数达到上限，停止重试')
        this.clearSubscribeRetry()
        return
      }
      this.subscribeAttempts++
      const msg = {
        type: 'subscribe',
        orderId: this.orderId || this.orderNumber,
        orderNumber: this.orderNumber,
        dataTypes: ['battery', 'power', 'time', 'cost', 'status'],
        timestamp: Date.now(),
        retry: this.subscribeAttempts
      }
      console.log(`🔁 重新发送订阅消息(第${this.subscribeAttempts}次):`, msg)
      websocketManager.send(msg)
    }, 1500)
  }

  /**
   * 清理订阅重试
   */
  clearSubscribeRetry() {
    if (this.subscribeRetryTimer) {
      clearInterval(this.subscribeRetryTimer)
      this.subscribeRetryTimer = null
    }
  }

  /**
   * WebSocket进入重连中
   */
  onWebSocketReconnecting(info) {
    console.log('充电状态WebSocket重连中...', info)
    this.emit('connectionStatusChanged', { connected: false, type: 'reconnecting', attempt: info?.attempt })
  }


  /**
   * WebSocket消息处理
   */
  onWebSocketMessage(data) {
    console.log('📨 收到WebSocket消息:', data)

    // 处理心跳消息
    if (data.type === 'ping') {
      console.log('💓 收到心跳ping，发送pong响应')
      websocketManager.send({ type: 'pong', timestamp: Date.now() })
      return
    }

    if (data.type === 'pong') {
      console.log('💓 收到心跳pong响应')
      return
    }

    // 处理订阅确认消息
    if (data.type === 'subscribeConfirm' || data.type === 'subscribe_confirm') {
      console.log('✅ 收到订阅确认消息:', data)
      this.emit('subscribeConfirmed', data)
      return
    }

    // 🆕 处理充电结束响应
    if (data.method === 'stopChargingReply' && data.code === 200) {
      console.log('🏁 收到充电结束响应:', data)
      this.handleChargingEndResponse(data)
      return
    }

    // 检查是否是充电状态回复消息
    // 兼容不同的服务端字段：可能是 method 或 type
    const msgType = data.method || data.type

    if ((msgType === 'ChargingReply' || msgType === 'charging_reply') && (data.code === 200 || data.success) && (data.data || data.payload)) {
      const payload = data.data || data.payload
      console.log('🔋 收到充电状态数据:', payload)
      this.hasReceivedData = true
      this.clearSubscribeRetry()
      // 首包已到，切换为已连接
      this.emit('connectionStatusChanged', { connected: true, type: 'websocket' })
      this.emit('chargingStarted')
      // 将接收到的数据转换为标准格式
      const statusData = this.convertChargingReplyToStatus(payload)
      this.updateChargingStatus(statusData)
    } else {
      console.log('⚠️ 收到非ChargingReply消息，可能仍在等待设备响应:', data)
      // 显示设备连接中弹窗（首包未到前）
      if (!this.hasReceivedData) {
        this.emit('deviceConnecting', data)
      }
    }
  }

  /**
   * 处理充电结束响应
   */
  handleChargingEndResponse(responseData) {
    const message = responseData.message || '充电已结束'
    
    console.log('🏁 处理充电结束:', { message, orderId: this.orderId, orderNumber: this.orderNumber })
    
    // 更新充电状态为已完成
    if (this.lastStatus) {
      const completedStatus = {
        ...this.lastStatus,
        status: 'completed',
        power: { ...this.lastStatus.power, current: 0 },
        endTime: Date.now(),
        endMessage: message
      }
      this.updateChargingStatus(completedStatus)
    }
    
    // 触发充电结束事件，让各页面自行处理
    const eventData = {
      message: message,
      orderId: this.orderId,
      orderNumber: this.orderNumber,
      timestamp: Date.now()
    }
    
    console.log('🎯 准备发送 chargingEnded 事件:', eventData)
    console.log('🎯 当前监听器数量:', this.listeners?.chargingEnded?.length || 0)
    
    this.emit('chargingEnded', eventData)
    
    // 不再延迟清理连接，立即清理
    this.onChargingCompleted()
  }

  /**
   * WebSocket连接断开
   */
  onWebSocketDisconnected() {
    console.log('充电状态WebSocket连接断开')
    this.emit('connectionStatusChanged', { connected: false, type: 'disconnected' })
  }

  /**
   * WebSocket连接错误
   */
  onWebSocketError(error) {
    console.error('充电状态WebSocket错误:', error)
    this.emit('connectionError', error)
  }

  /**
   * WebSocket重连失败
   */
  onWebSocketReconnectFailed() {
    console.error('充电状态WebSocket重连失败')
    this.emit('connectionStatusChanged', { connected: false, type: 'failed' })
  }





  /**
   * 将ChargingReply数据转换为标准充电状态格式
   */
  convertChargingReplyToStatus(replyData) {
    const statusData = {
      battery: {
        current: parseInt(replyData.currentBatteryLevel) || 0, // 当前电池电量百分比
        filled: parseInt(replyData.alreadyFillDegree) || 0 // 已充电量百分比
      },
      power: {
        current: parseFloat(replyData.realTimeCurrent) || 0, // 实时电流
        voltage: parseFloat(replyData.realTimeVoltage) || 0, // 实时电压
        power: parseFloat(replyData.realTimePower) || 0 // 实时功率
      },
      time: {
        elapsed: replyData.alreadyFillTime || '--', // 已充电时间
        remaining: replyData.remainderTime || '--' // 预计剩余时间
      },
      cost: {
        estimated: parseFloat(replyData.estimatedCost) || 0, // 预计费用
        energy: parseFloat(replyData.realTimePower) || 0 // 实时功率作为能耗参考
      },
      order: {
        orderNum: replyData.orderNum || '', // 订单号
        spearNum: replyData.spearNum || '', // 充电枪编号
        updateTime: replyData.updateTime || '' // 更新时间
      },
      status: this.determineChargingStatus(replyData), // 根据数据判断充电状态
      timestamp: Date.now()
    }

    return statusData
  }

  /**
   * 根据充电数据判断充电状态
   */
  determineChargingStatus(replyData) {
    const currentLevel = parseInt(replyData.currentBatteryLevel) || 0
    const realTimePower = parseFloat(replyData.realTimePower) || 0

    // 如果电量达到100%，认为充电完成
    if (currentLevel >= 100) {
      return 'completed'
    }

    // 如果实时功率大于0，认为正在充电
    if (realTimePower > 0) {
      return 'charging'
    }

    // 否则认为充电暂停
    return 'paused'
  }

  /**
   * 更新充电状态
   */
  updateChargingStatus(statusData) {
    this.lastStatus = statusData

    // 保存到缓存
    this.saveStatusToCache(statusData)

    console.log('🔋 [全局] 更新充电状态:', statusData)
    console.log('🔋 [全局] 充电进度:', statusData.percentage + '%')
    console.log('🔋 [全局] 充电功率:', statusData.power + 'kW')
    console.log('🔋 [全局] 当前时间:', new Date().toLocaleTimeString())

    this.emit('statusUpdate', statusData)
  }



  /**
   * 添加事件监听
   */
  on(event, callback) {
    if (!this.callbacks.has(event)) {
      this.callbacks.set(event, [])
    }
    this.callbacks.get(event).push(callback)
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (this.callbacks.has(event)) {
      const callbacks = this.callbacks.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    if (this.callbacks.has(event)) {
      this.callbacks.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`充电状态管理器事件回调执行失败 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 获取最后的状态数据
   */
  getLastStatus() {
    return this.lastStatus
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    const wsStatus = websocketManager.getStatus()
    return {
      isMonitoring: this.isMonitoring,
      websocket: wsStatus,
      polling: !!this.pollingTimer,
      orderId: this.orderId
    }
  }

  /**
   * 保存充电状态到缓存
   */
  saveStatusToCache(statusData) {
    try {
      const cacheData = {
        data: statusData,
        timestamp: Date.now(),
        orderId: this.orderId,
        orderNumber: this.orderNumber,
        expireTime: Date.now() + this.cacheExpireTime
      }

      uni.setStorageSync(this.cacheKey, JSON.stringify(cacheData))
      console.log('✅ 充电状态已保存到缓存:', statusData)
    } catch (error) {
      console.error('❌ 保存充电状态缓存失败:', error)
    }
  }

  /**
   * 从缓存加载充电状态
   */
  loadStatusFromCache(orderId) {
    try {
      const cacheStr = uni.getStorageSync(this.cacheKey)
      if (!cacheStr) {
        console.log('📭 没有找到充电状态缓存')
        return null
      }

      const cacheData = JSON.parse(cacheStr)

      // 检查缓存是否过期
      if (Date.now() > cacheData.expireTime) {
        console.log('⏰ 充电状态缓存已过期，清除缓存')
        this.clearStatusCache()
        return null
      }

      // 检查订单ID是否匹配
      if (cacheData.orderId !== orderId && cacheData.orderNumber !== orderId) {
        console.log('🔄 订单ID不匹配，不使用缓存数据')
        return null
      }

      console.log('✅ 从缓存加载充电状态:', cacheData.data)
      return cacheData.data
    } catch (error) {
      console.error('❌ 加载充电状态缓存失败:', error)
      return null
    }
  }

  /**
   * 清除充电状态缓存
   */
  clearStatusCache() {
    try {
      uni.removeStorageSync(this.cacheKey)
      console.log('🗑️ 充电状态缓存已清除')
    } catch (error) {
      console.error('❌ 清除充电状态缓存失败:', error)
    }
  }

  /**
   * 保存连接状态到缓存
   */
  saveConnectionToCache() {
    try {
      const connectionData = {
        isMonitoring: this.isMonitoring,
        orderId: this.orderId,
        orderNumber: this.orderNumber,
        hasReceivedData: this.hasReceivedData,
        timestamp: Date.now()
      }

      uni.setStorageSync(this.connectionCacheKey, JSON.stringify(connectionData))
      console.log('✅ 连接状态已保存到缓存')
    } catch (error) {
      console.error('❌ 保存连接状态缓存失败:', error)
    }
  }

  /**
   * 从缓存恢复连接状态
   */
  loadConnectionFromCache() {
    try {
      const cacheStr = uni.getStorageSync(this.connectionCacheKey)
      if (!cacheStr) return null

      const connectionData = JSON.parse(cacheStr)

      // 检查缓存时间（连接状态缓存5分钟有效）
      if (Date.now() - connectionData.timestamp > 5 * 60 * 1000) {
        this.clearConnectionCache()
        return null
      }

      return connectionData
    } catch (error) {
      console.error('❌ 加载连接状态缓存失败:', error)
      return null
    }
  }

  /**
   * 清除连接状态缓存
   */
  clearConnectionCache() {
    try {
      uni.removeStorageSync(this.connectionCacheKey)
      console.log('🗑️ 连接状态缓存已清除')
    } catch (error) {
      console.error('❌ 清除连接状态缓存失败:', error)
    }
  }

  /**
   * 手动清除所有缓存数据
   */
  clearAllCache() {
    this.clearStatusCache()
    this.clearConnectionCache()
    this.lastStatus = null
    console.log('🗑️ 所有充电状态缓存已清除')
  }

  /**
   * 检查是否有有效的缓存数据
   */
  hasCachedData(orderId) {
    const cachedStatus = this.loadStatusFromCache(orderId)
    return !!cachedStatus
  }

  /**
   * 充电完成时的清理
   */
  onChargingCompleted() {
    console.log('充电已完成，清理连接和缓存')
    this.forceStopMonitoring()
  }

  /**
   * 获取当前监控状态
   */
  getMonitoringStatus() {
    return {
      isMonitoring: this.isMonitoring,
      orderId: this.orderId,
      orderNumber: this.orderNumber,
      hasData: !!this.lastStatus,
      wsConnected: websocketManager.getStatus().connected
    }
  }
}

// 创建单例实例
const chargingStatusManager = new ChargingStatusManager()

export default chargingStatusManager
