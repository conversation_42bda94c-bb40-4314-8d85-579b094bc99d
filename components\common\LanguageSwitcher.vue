<template>
  <view class="language-switcher">
    <!-- 简单按钮样式 -->
    <view v-if="type === 'button'" class="language-button" @click="toggleLanguage">
      <text class="language-text">{{ currentLanguage.flag }} {{ currentLanguage.name }}</text>
    </view>
    
    <!-- 下拉选择样式 -->
    <view v-else-if="type === 'select'" class="language-select">
      <picker 
        :value="currentIndex" 
        :range="languageOptions" 
        range-key="name"
        @change="onLanguageChange"
      >
        <view class="language-picker">
          <text class="language-text">{{ currentLanguage.flag }} {{ currentLanguage.name }}</text>
          <text class="arrow">▼</text>
        </view>
      </picker>
    </view>
    
    <!-- 列表样式 -->
    <view v-else-if="type === 'list'" class="language-list">
      <view 
        v-for="(lang, index) in languages" 
        :key="lang.code"
        class="language-item"
        :class="{ active: lang.code === currentLanguage.code }"
        @click="switchToLanguage(lang.code)"
      >
        <text class="language-flag">{{ lang.flag }}</text>
        <text class="language-name">{{ lang.name }}</text>
        <text v-if="lang.code === currentLanguage.code" class="check-mark">✓</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { switchLanguage, getCurrentLanguage, getSupportedLanguages } from '@/locale/index.js'

// 定义props
const props = defineProps({
  type: {
    type: String,
    default: 'button', // button, select, list
    validator: (value) => ['button', 'select', 'list'].includes(value)
  },
  showFlag: {
    type: Boolean,
    default: true
  },
  showName: {
    type: Boolean,
    default: true
  }
})

// 定义emits
const emit = defineEmits(['change'])

// 使用i18n
const { locale } = useI18n()

// 响应式数据
const languages = ref(getSupportedLanguages())
const currentLanguageCode = ref(getCurrentLanguage())

// 计算属性
const currentLanguage = computed(() => {
  return languages.value.find(lang => lang.code === currentLanguageCode.value) || languages.value[0]
})

const currentIndex = computed(() => {
  return languages.value.findIndex(lang => lang.code === currentLanguageCode.value)
})

const languageOptions = computed(() => {
  return languages.value.map(lang => ({
    name: `${lang.flag} ${lang.name}`,
    code: lang.code
  }))
})

// 方法
const switchToLanguage = (langCode) => {
  if (langCode !== currentLanguageCode.value) {
    currentLanguageCode.value = langCode
    switchLanguage(langCode)
    
    // 触发change事件
    emit('change', langCode)
    
    // 移除切换成功提示
    // uni.showToast({
    //   title: locale.value === 'fr' ? 'Langue modifiée' : 'Language changed',
    //   icon: 'success',
    //   duration: 1500
    // })
  }
}

const toggleLanguage = () => {
  const currentIndex = languages.value.findIndex(lang => lang.code === currentLanguageCode.value)
  const nextIndex = (currentIndex + 1) % languages.value.length
  const nextLanguage = languages.value[nextIndex]
  switchToLanguage(nextLanguage.code)
}

const onLanguageChange = (e) => {
  const selectedIndex = e.detail.value
  const selectedLanguage = languages.value[selectedIndex]
  switchToLanguage(selectedLanguage.code)
}

// 生命周期
onMounted(() => {
  currentLanguageCode.value = getCurrentLanguage()
})
</script>

<style lang="less" scoped>
.language-switcher {
  .language-button {
    padding: 8px 16px;
    background: #ffffff;
    border-radius: 8rpx;
    
    .language-text {
      font-size: 32rpx;
      color: #333;
    }
    
    &:active {
      background: #f5f5f5;
    }
  }
  
  .language-select {
    .language-picker {
      display: flex;
      align-items: center;
      
      .language-text {
        font-size: 32rpx;
        color: #333;
      }
      
      .arrow {
        font-size: 24rpx;
        color: #ccc;
        margin-left: 8rpx;
      }
    }
  }
  
  .language-list {
    .language-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      
      .language-flag {
        font-size: 32rpx;
        margin-right: 12px;
      }
      
      .language-name {
        flex: 1;
        font-size: 32rpx;
        color: #333;
      }
      
      .check-mark {
        font-size: 32rpx;
        color: #168CFA;
        font-weight: bold;
      }
      
      &.active {
        background: #f0f8ff;
      }
      
      &:active {
        background: #e0e0e0;
      }
    }
  }
}
</style>
