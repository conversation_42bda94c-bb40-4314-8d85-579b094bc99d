import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import i18n from './locale/index.js'
import Request from './utils/request'
import env from './config/env'
import { useToast, useGlobalToast } from './composables/useToast'
import { initAppVersion } from './utils/version.js'
import { initializePerformanceOptimizer } from './utils/performance-optimizer.js'
import { startScannerPreload } from './utils/scanner-preloader.js'

import pinia from './store'
// 导入并安装polyfill
import installPolyfills from './utils/polyfill'

// 安装polyfill
// 注册 HUD 模块 shim，提供全局访问
import './composables/__hudModuleShim'
// 注册简化版网络异常处理模块
import './composables/useSimpleNetworkError'

// 开发环境加载网络异常测试工具
// #ifdef H5
import './utils/network-error-test.js'
// #endif

installPolyfills();

// 初始化性能优化器
initializePerformanceOptimizer().catch(error => {
  console.warn('性能优化器初始化失败:', error);
});

// 启动扫码预加载（异步，不阻塞应用启动）
setTimeout(() => {
  startScannerPreload().catch(error => {
    console.warn('扫码预加载失败:', error);
  });
}, 1000); // 延迟1秒启动，避免影响应用启动性能

export function createApp() {
  const app = createSSRApp(App)

  // 使用Pinia
  app.use(pinia)

  // 使用国际化插件
  app.use(i18n)

  // 确保i18n在全局可用
  app.config.globalProperties.$t = i18n.global.t

  // 初始化应用版本号
  initAppVersion()

  // 不再设置全局ClientId，将在请求拦截器中动态获取

  // 注册全局Toast实例
  const toast = useGlobalToast()
  app.provide('toast', toast)

  // 确保应用启动时Toast状态是干净的
  if (toast && toast.clearToastState) {
    toast.clearToastState()
  }

  return {
    app
  }
}
// #endif