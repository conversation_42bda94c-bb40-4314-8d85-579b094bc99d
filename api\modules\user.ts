/**
 * 用户相关API接口
 */
import Request from '../../utils/request'

// 用户相关类型定义
export interface UserInfo {
  userId: string
  openId: string
  username: string
  nickname: string
  nickName?: string // 新增nickName字段，与接口保持一致
  avatar?: string
  phonenumber: string
  accountPhone?: string // 新增accountPhone字段，与接口保持一致
  accountPhotoUrl?: string // 新增accountPhotoUrl字段
  accountName?: string // 新增accountName字段
  email?: string
  sex?: 0 | 1 | 2 // 0-未知, 1-男, 2-女
  status: number
  createTime: string
  updateTime?: string
  lastLoginTime?: string
  loginCount?: number
  realName?: string
  idCard?: string
  isVerified: boolean
}

export interface UserBalance {
  userId: string
  balance: number
  frozenAmount: number
  availableAmount: number
  totalRecharge: number
  totalConsumption: number
  currency: string
  updateTime: string
}

export interface UpdateUserData {
  nickname?: string
  avatar?: string
  email?: string
  sex?: 0 | 1 | 2
  realName?: string
  idCard?: string
}

// 账户流水记录
export interface BalanceStatement {
  statementId: string
  statementType: string // 0待支付 1已完成 4微信退款 5赠送金额
  amount: string
  remainingBalance: string
  statementTime: string
  orderId: string
  openId: string
  remark: string
  statementStatus?: string // 0待支付 1已完成
}

// 账户余额详情响应
export interface BalanceDetailsResponse {
  total: number
  rows: BalanceStatement[]
  code: number
  msg: string
}

export interface UserInfoParams {
  openId?: string
}

export interface ChargingPileMonitorData {
  totalPiles: number
  onlinePiles: number
  chargingPiles: number
  availablePiles: number
  offlinePiles: number
  maintenancePiles: number
  totalPower: number
  currentPower: number
  todayEnergy: number
  monthEnergy: number
  yearEnergy: number
  totalEnergy: number
  todayOrders: number
  monthOrders: number
  yearOrders: number
  totalOrders: number
  todayRevenue: number
  monthRevenue: number
  yearRevenue: number
  totalRevenue: number
  updateTime: string
  // 充电状态相关字段
  orderNum?: string
  currentBatteryLevel?: number  // 当前电池电量百分比
  remainingTime?: string        // 剩余充电时间
  chargingTime?: string         // 充电时间
}

// 导入通用类型，避免重复定义
import type { ApiResponse } from '../types'

/**
 * 获取用户信息
 * @param {UserInfoParams} params 请求参数
 * @param {object} config 请求配置
 * @returns {Promise<ApiResponse<UserInfo>>} 用户信息
 */
export function getUserInfo(params: UserInfoParams = {}, config: any = {}): Promise<ApiResponse<UserInfo>> {
  const openId = params?.openId || '';
  return Request.get(`/account/user/openId/${openId}`, config)
}

/**
 * 获取用户余额
 * @returns {Promise<ApiResponse<UserBalance>>} 用户余额信息
 */
export function getUserBalance(): Promise<ApiResponse<UserBalance>> {
  return Request.get('/account/user/balance')
}

/**
 * 获取账户余额详情列表
 * @returns {Promise<BalanceDetailsResponse>} 账户流水详情
 */
export function getBalanceDetails(): Promise<BalanceDetailsResponse> {
  return Request.get('/account/statement/queryAccountStatement')
}

/**
 * 更新用户信息
 * @param {UpdateUserData} data 用户信息
 * @returns {Promise<ApiResponse<boolean>>} 更新结果
 */
export function updateUserInfo(data: UpdateUserData): Promise<ApiResponse<boolean>> {
  return Request.post('/account/user/update', data)
}

/**
 * 获取用户充电桩监控信息
 * @returns {Promise<ApiResponse<ChargingPileMonitorData>>} 充电桩监控数据
 */
export function getUserChargingPileInfo(): Promise<ApiResponse<ChargingPileMonitorData>> {
  return Request.post('/statistics/monitorData/query/recharging')
}

/**
 * 获取用户详细资料
 * @param {string} userId 用户ID
 * @returns {Promise<ApiResponse<UserInfo>>} 用户详细信息
 */
export function getUserProfile(userId: string): Promise<ApiResponse<UserInfo>> {
  if (!userId) {
    return Promise.reject(new Error('用户ID不能为空'));
  }
  return Request.get(`/account/user/profile/${userId}`)
}

/**
 * 修改用户密码
 * @param {Object} data 密码修改数据
 * @param {string} data.oldPassword 旧密码
 * @param {string} data.newPassword 新密码
 * @param {string} data.confirmPassword 确认密码
 * @returns {Promise<ApiResponse<boolean>>} 修改结果
 */
export function changePassword(data: {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}): Promise<ApiResponse<boolean>> {
  return Request.post('/account/user/changePassword', data)
}

/**
 * 绑定手机号
 * @param {Object} data 绑定数据
 * @param {string} data.phonenumber 手机号
 * @param {string} data.smsCode 短信验证码
 * @returns {Promise<ApiResponse<boolean>>} 绑定结果
 */
export function bindPhone(data: {
  phonenumber: string
  smsCode: string
}): Promise<ApiResponse<boolean>> {
  return Request.post('/account/user/bindPhone', data)
}

/**
 * 解绑手机号
 * @param {Object} data 解绑数据
 * @param {string} data.smsCode 短信验证码
 * @returns {Promise<ApiResponse<boolean>>} 解绑结果
 */
export function unbindPhone(data: {
  smsCode: string
}): Promise<ApiResponse<boolean>> {
  return Request.post('/account/user/unbindPhone', data)
}

/**
 * 实名认证
 * @param {Object} data 认证数据
 * @param {string} data.realName 真实姓名
 * @param {string} data.idCard 身份证号
 * @returns {Promise<ApiResponse<boolean>>} 认证结果
 */
export function realNameAuth(data: {
  realName: string
  idCard: string
}): Promise<ApiResponse<boolean>> {
  return Request.post('/account/user/realNameAuth', data)
}

/**
 * 注销账户
 * @param {Object} data 注销数据
 * @param {string} data.reason 注销原因
 * @param {string} data.smsCode 短信验证码
 * @returns {Promise<ApiResponse<boolean>>} 注销结果
 */
export function deleteAccount(data: {
  reason: string
  smsCode: string
}): Promise<ApiResponse<boolean>> {
  return Request.post('/account/user/delete', data)
}

/**
 * 修改用户账户信息（昵称和手机号）
 * @param {Object} data 用户账户信息
 * @param {string} data.nickName 昵称
 * @param {string} data.accountPhone 手机号
 * @returns {Promise<ApiResponse<boolean>>} 修改结果
 */
export function editAccountInfo(data: {
  nickName: string
  accountPhone: string
}): Promise<ApiResponse<boolean>> {
  return Request.post('/account/user/edit/accountInfo', data)
}

/**
 * 上传用户头像
 * @param {string|File} avatarfile 头像文件路径或File对象
 * @returns {Promise<ApiResponse<{imgUrl: string}>>} 上传结果，返回头像URL
 */
export function uploadAvatar(avatarfile: string | File): Promise<ApiResponse<{ imgUrl: string }>> {
  // 如果是字符串路径（小程序环境），使用uni.uploadFile
  if (typeof avatarfile === 'string') {
    return new Promise((resolve, reject) => {
      const baseURL = Request.getBaseURL()
      const token = (uni as any).getStorageSync('token')

      // 获取ClientId，与其他接口保持一致的逻辑
      let clientId = ''
      const userInfo = (uni as any).getStorageSync('userInfo')
      if (userInfo) {
        try {
          const userInfoObj = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
          if (userInfoObj && userInfoObj.client_id) {
            clientId = userInfoObj.client_id
          }
        } catch (e) {
          console.error('解析用户信息失败:', e)
        }
      }

      // 如果没有clientId，使用默认值（与登录接口一致）
      if (!clientId) {
        clientId = "10e2f22a9910c1393b3027f1ecbf3b6c"
        console.log('头像上传接口使用默认ClientId:', clientId)
      }

      ;(uni as any).uploadFile({
        url: `${baseURL}/app/personal/avatar`,
        filePath: avatarfile,
        name: 'avatarfile',
        header: {
          'Authorization': token ? `Bearer ${token}` : '',
          'ClientId': clientId,
          'Accept': 'application/json'
        },
          success: (res: any) => {
            try {
              const data = JSON.parse(res.data)
              if (data.code === 200) {
                resolve(data)
              } else {
                reject(new Error(data.msg || 'Upload failed'))
              }
            } catch (e) {
              reject(new Error('Response parse error'))
            }
          },
          fail: (err: any) => {
            reject(err)
          }
        })
    })
  } else {
    // 如果是File对象（H5环境），使用FormData
    const formData = new FormData()
    formData.append('avatarfile', avatarfile)

    return Request.post('/app/personal/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
