<template>
    <view class="about-us-container">
        <!-- 头部导航栏 -->
        <view class="fixed-header" :style="{ paddingTop: statusBarHeight + 'px' }">
            <view class="header-content">
                <view class="back-btn" @click="goBack">
                    <text class="iconfont icon-back"></text>
                </view>
                <text class="title">{{ $t('nav.about') }}</text>
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-wrapper" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
            <view class="content-area">
                <!-- Logo区域 -->
                <view class="logo-container">
                    <image class="logo-image" src="/static/images/ARNIO.png" mode="aspectFit"></image>
                </view>

                <!-- 版本信息 -->
                <view class="version-info">
                    <text>{{ $t('app.currentVersion') }} {{ currentVersion }}</text>
                </view>

                <!-- 检测版本按钮 -->
                <view class="check-version-section">
                    <button
                        class="check-version-btn"
                        @click="handleCheckVersion"
                        :disabled="checking"
                    >
                        <view class="btn-content">
                            <text v-if="checking" class="loading-icon"></text>
                            <text class="btn-text">{{ $t('update.checkVersion') }}</text>
                        </view>
                    </button>
                </view>
            </view>
        </view>

        <!-- 自定义提示弹窗（最新/错误） -->
        <NiceAlert
          :visible="niceAlert.visible"
          :title="niceAlert.title"
          :message="niceAlert.message"
          :type="niceAlert.type"
          :confirm-text="$t('common.confirm') || 'OK'"
          @close="niceAlert.visible=false"
        />

        <!-- 更新弹窗 -->
        <UpdateDialog
            :visible="updateDialogVisible"
            :version-num="updateInfo?.versionNum || ''"
            :version-description="updateInfo?.versionDescription || ''"
            :download-url="updateInfo?.programPackageUrl || ''"
            :is-mandatory-update="updateInfo?.isMandatoryUpdate || false"
            @close="closeUpdateDialog"
            @update="performUpdate"
        />
    </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import UpdateDialog from '../../components/common/UpdateDialog.vue'
import NiceAlert from '../../components/common/NiceAlert.vue'
import { useVersionUpdate } from '../../composables/useVersionUpdate'
import { useVersionStore } from '@/store/version.js'


// 简易 i18n 助手，避免脚本中直接使用 $t 报未定义
const t = (key) => {
  try {
    // @ts-ignore
    if (uni && uni.$t) return uni.$t(key)
  } catch (e) {}
  return key
}

const statusBarHeight = ref(0)
const versionStore = useVersionStore()

// 来自 Pinia 的实时版本号（更新后会自动刷新）
const currentVersion = computed(() => versionStore.currentVersion)

const {
    updateDialogVisible,
    updateInfo,
    checking,
    checkVersionUpdate,
    closeUpdateDialog,
    performUpdate
} = useVersionUpdate()

const niceAlert = ref({
  visible: false,
  title: '',
  message: '',
  type: 'success'
})

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}

const handleCheckVersion = () => {
    checkVersionUpdate(false, {
      onLatest: () => {
        niceAlert.value = {
          visible: true,
          title: t('update.title') || 'Update',
          message: t('update.latestVersion'),
          type: 'success'
        }
      },
      onError: (msg) => {
        niceAlert.value = {
          visible: true,
          title: t('update.title') || 'Update',
          message: msg,
          type: 'error'
        }
      }
    })
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.about-us-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);

    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;

        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;

            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }

        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 40rpx;
}

.logo-container {
    margin-bottom: 60rpx;

    .logo-image {
        width: 200rpx;
        height: 200rpx;
    }
}

.version-info {
    margin-bottom: 80rpx;

    text {
        font-size: 32rpx;
        color: #666;
    }
}

.check-version-section {
    width: 100%;
    max-width: 400rpx;

    .check-version-btn {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #ff4757, #ff3742);
        border: none;
        border-radius: 44rpx;
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;

        &:disabled {
            opacity: 0.7;
        }

        .btn-content {
            display: flex;
            align-items: center;

            .loading-icon {
                width: 32rpx;
                height: 32rpx;
                border: 3rpx solid transparent;
                border-top: 3rpx solid #fff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-right: 16rpx;
            }

            .btn-text {
                color: #fff;
            }
        }
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>