<template>
  <view class="checkbox-container" @click="toggleCheck">
    <view class="checkbox" :class="{ checked: modelValue }">
      <text v-if="modelValue" class="checkbox-icon">✓</text>
    </view>
    <text class="checkbox-label">{{ label }}</text>
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'CheckboxItem',
  props: {
    // 复选框标签文本
    label: {
      type: String,
      default: ''
    },
    // 复选框值（v-model）
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  methods: {
    toggleCheck() {
      // 切换复选框状态
      const newValue = !this.modelValue
      
      // 更新v-model值
      this.$emit('update:modelValue', newValue)
      
      // 发送change事件
      this.$emit('change', newValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.checkbox-container {
  display: flex;
  align-items: center;
  
  .checkbox {
    width: 40rpx;
    height: 40rpx;
    border: 2rpx solid #ddd;
    border-radius: 4rpx;
    margin-right: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    &.checked {
      background: #ff4d4f;
      border-color: #ff4d4f;
    }
    
    .checkbox-icon {
      color: #fff;
      font-size: 28rpx;
      line-height: 28rpx;
      font-weight: bold;
    }
  }
  
  .checkbox-label {
    font-size: 28rpx;
    color: #666;
  }
}
</style> 