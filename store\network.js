import { defineStore } from 'pinia'

export const useNetworkStore = defineStore('network', {
  state: () => ({
    isConnected: true,
    networkType: 'unknown',
    lastConnectedTime: null,
    showReconnectToast: false,
    showOfflineModal: false, // 全局断网弹窗状态
    reconnectAttempts: 0, // 重连尝试次数
    offlineStartTime: null, // 断网开始时间
    offlineModalTimer: null, // 断网弹窗延迟显示定时器
    networkPollingTimer: null, // 网络状态轮询定时器
    isUpdating: false, // 防止并发更新
    lastUpdateTime: 0, // 上次更新时间戳
    consecutiveOfflineCount: 0, // 连续断网检测次数
    lastNetworkErrorTime: 0, // 上次网络错误时间
    enableConnectivityTest: false, // 是否启用连通性测试（默认关闭，减少网络请求）
  }),

  getters: {
    // 获取网络状态描述
    networkStatusText() {
      if (!this.isConnected) {
        return 'networkDisconnected'
      }
      
      switch (this.networkType) {
        case 'wifi':
          return 'wifiConnected'
        case '5g':
          return '5gConnected'
        case '4g':
          return '4gConnected'
        case '3g':
          return '3gConnected'
        case '2g':
          return '2gConnected'
        case 'none':
          return 'noNetwork'
        default:
          return 'unknownNetwork'
      }
    },

    // 是否为弱网络
    isWeakNetwork() {
      return this.isConnected && ['2g', '3g'].includes(this.networkType)
    }
  },

  actions: {
    // 更新网络状态
    updateNetworkStatus(isConnected, networkType) {
      // 防抖：500ms内只允许一次更新
      const now = Date.now()
      if (this.isUpdating || (now - this.lastUpdateTime < 500)) {
        console.log('🌐 [NetworkStore] 网络状态更新被忽略（防抖或正在更新）')
        return
      }
      
      const wasConnected = this.isConnected
      
      console.log('🌐 [NetworkStore] 网络状态变化:', {
        from: wasConnected ? '已连接' : '已断开',
        to: isConnected ? '已连接' : '已断开',
        networkType,
        timestamp: now
      })
      
      // 标记正在更新
      this.isUpdating = true
      this.lastUpdateTime = now
      
      // 更新状态
      this.isConnected = isConnected
      this.networkType = networkType

      if (isConnected && !wasConnected) {
        // 网络恢复
        console.log('✅ [NetworkStore] 网络已恢复')
        this.lastConnectedTime = now
        this.showReconnectToast = true
        this.showOfflineModal = false
        this.reconnectAttempts = 0
        this.consecutiveOfflineCount = 0
        
        // 清除延迟显示定时器
        if (this.offlineModalTimer) {
          clearTimeout(this.offlineModalTimer)
          this.offlineModalTimer = null
        }
        
        // 3秒后隐藏重连提示
        setTimeout(() => {
          this.showReconnectToast = false
        }, 3000)
        
      } else if (!isConnected && wasConnected) {
        // 网络断开
        console.log('❌ [NetworkStore] 网络断开')
        this.showReconnectToast = false
        this.offlineStartTime = now
        this.consecutiveOfflineCount = 1
        
        // 延迟显示断网弹窗，避免短暂的网络波动
        this.showOfflineModalWithDelay()
        
      } else if (!isConnected && !wasConnected) {
        // 持续断网状态
        this.consecutiveOfflineCount++
        console.log('🌐 [NetworkStore] 持续断网状态，计数:', this.consecutiveOfflineCount)
        
        // 如果持续断网但弹窗未显示，确保显示弹窗
        if (!this.showOfflineModal) {
          // 如果断网时间超过2秒，立即显示弹窗
          if (now - this.offlineStartTime > 2000) {
            console.log('🔔 [NetworkStore] 断网时间超过2秒，立即显示弹窗')
            this.showOfflineModalImmediately()
          }
        }
      }
      
      // 解除更新锁定
      setTimeout(() => {
        this.isUpdating = false
      }, 100)
    },
    
    // 立即显示断网弹窗
    showOfflineModalImmediately() {
      // 清除之前的定时器
      if (this.offlineModalTimer) {
        clearTimeout(this.offlineModalTimer)
        this.offlineModalTimer = null
      }
      
      console.log('🔔 [NetworkStore] 显示断网弹窗')
      this.showOfflineModal = true
    },
    
    // 延迟显示断网弹窗，避免短暂的网络波动
    showOfflineModalWithDelay() {
      // 清除之前的定时器
      if (this.offlineModalTimer) {
        clearTimeout(this.offlineModalTimer)
        this.offlineModalTimer = null
      }
      
      // 延迟1.5秒显示，给网络恢复的机会
      this.offlineModalTimer = setTimeout(() => {
        // 再次检查网络状态
        if (!this.isConnected) {
          console.log('🔔 [NetworkStore] 网络断开持续1.5秒，显示断网弹窗')
          this.showOfflineModal = true
        }
        this.offlineModalTimer = null
      }, 1500)
    },



    // 初始化网络状态监听
    initNetworkListener() {
      console.log('🌐 [NetworkStore] 初始化网络状态监听')
      
      // 防止重复初始化
      if (this.networkPollingTimer) {
        console.log('🌐 [NetworkStore] 网络监听已经初始化，跳过')
        return
      }
      
      // 获取当前网络状态
      uni.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none'
          console.log('🌐 [NetworkStore] 初始网络状态:', { networkType: res.networkType, isConnected })
          
          // 更新网络状态
          this.isConnected = isConnected
          this.networkType = res.networkType
          
          // 如果初始状态就是断网，直接显示断网弹窗
          if (!isConnected) {
            console.log('🌐 [NetworkStore] 初始状态为断网，显示断网弹窗')
            this.offlineStartTime = Date.now()
            this.showOfflineModalImmediately()
          }
        },
        fail: (err) => {
          console.error('🌐 [NetworkStore] 获取网络状态失败:', err)
          this.updateNetworkStatus(false, 'none')
        }
      })
      
      // 延迟执行连通性测试，等待系统稳定
      setTimeout(() => {
        this.performConnectivityTest()
      }, 1000)

      // 监听网络状态变化
      uni.onNetworkStatusChange((res) => {
        console.log('🌐 [NetworkStore] 网络状态变化事件触发:', { 
          isConnected: res.isConnected, 
          networkType: res.networkType 
        })
        
        // 系统监听器可能不准确，需要进一步验证
        this.validateNetworkStatus(res.isConnected, res.networkType)
      })
      
      // 启动轮询作为备用方案，确保网络状态检测的可靠性
      this.startNetworkPolling()
      
      console.log('🌐 [NetworkStore] 网络监听初始化完成')
    },
    
    // 验证网络状态 - 主要依赖系统API
    async validateNetworkStatus(systemIsConnected, systemNetworkType) {
      console.log('🌐 [NetworkStore] 验证网络状态:', { systemIsConnected, systemNetworkType })
      
      // 默认情况下，完全依赖系统 API，不发送额外的网络请求
      // 如果需要连通性测试，可以通过设置 enableConnectivityTest = true 来启用
      if (!this.enableConnectivityTest) {
        // 直接使用系统 API 的结果
        this.updateNetworkStatus(systemIsConnected, systemNetworkType)
        return
      }
      
      // 如果系统显示断开，直接采用系统结果
      if (!systemIsConnected) {
        this.updateNetworkStatus(false, systemNetworkType)
        return
      }
      
      // 只有在启用连通性测试且需要时才进行实际测试
      if (this.enableConnectivityTest && !this.isConnected) {
        try {
          const actualConnected = await this.testActualConnectivity()
          
          if (actualConnected) {
            this.updateNetworkStatus(true, systemNetworkType)
          } else {
            console.log('🌐 [NetworkStore] 检测到假连接状态')
            this.updateNetworkStatus(false, systemNetworkType)
          }
        } catch (error) {
          console.error('🌐 [NetworkStore] 连通性测试异常:', error)
          this.updateNetworkStatus(false, systemNetworkType)
        }
      } else {
        this.updateNetworkStatus(true, systemNetworkType)
      }
    },
    
    // 测试实际网络连通性 - 使用自己的 API 接口
    testActualConnectivity() {
      return new Promise((resolve) => {
        // 设置超时时间
        const timeout = 3000
        let timeoutId
        
        console.log('🌐 [NetworkStore] 执行连通性检测（仅在启用时执行）')
        
        // 使用项目自己的 API 接口进行检测
        // 从环境配置中获取 API 地址
        const testUrl = 'https://charge.arnioci.com/prod-api/ping'
        
        const requestTask = uni.request({
          url: testUrl,
          method: 'HEAD',
          timeout: timeout,
          success: () => {
            clearTimeout(timeoutId)
            console.log('🌐 [NetworkStore] 连通性测试成功')
            resolve(true)
          },
          fail: (err) => {
            clearTimeout(timeoutId)
            console.log('🌐 [NetworkStore] 连通性测试失败:', err.errMsg || '未知错误')
            resolve(false)
          }
        })
        
        // 设置超时
        timeoutId = setTimeout(() => {
          requestTask && requestTask.abort()
          console.log('🌐 [NetworkStore] 连通性测试超时')
          resolve(false)
        }, timeout)
      })
    },
    
    // 执行连通性测试 - 兼容旧方法
    performConnectivityTest() {
      console.log('🌐 [NetworkStore] 执行连通性测试')
      this.validateNetworkStatus(this.isConnected, this.networkType)
    },
    
    // 启动网络状态轮询（轻量级备用方案）
    startNetworkPolling() {
      // 清除已存在的轮询
      if (this.networkPollingTimer) {
        clearInterval(this.networkPollingTimer)
      }
      
      // 创建轻量级的网络检测函数 - 主要依赖系统 API
      const checkNetworkLightweight = async () => {
        try {
          // 只使用系统 API，避免发送实际网络请求
          const networkType = await new Promise((resolve) => {
            uni.getNetworkType({
              success: (res) => resolve(res.networkType),
              fail: () => resolve('none')
            })
          })
          
          const systemIsConnected = networkType !== 'none'
          
          // 如果系统状态与当前状态不一致，才进行验证
          if (systemIsConnected !== this.isConnected) {
            console.log('🔄 [NetworkStore] 检测到网络状态变化，进行验证')
            await this.validateNetworkStatus(systemIsConnected, networkType)
          }
          
          // 如果持续断网但弹窗未显示，确保显示弹窗
          if (!this.isConnected && !this.showOfflineModal) {
            const offlineDuration = Date.now() - (this.offlineStartTime || Date.now())
            if (offlineDuration > 2000) { // 断网超过2秒才显示
              console.log('🔄 [NetworkStore] 检测到持续断网，显示弹窗')
              this.showOfflineModalImmediately()
            }
          }
          
        } catch (error) {
          console.error('🔄 [NetworkStore] 网络检查失败:', error)
        }
      }
      
      // 立即执行一次检查
      checkNetworkLightweight()
      
      // 降低轮询频率到10秒，减少资源消耗
      this.networkPollingTimer = setInterval(checkNetworkLightweight, 10000)
    },
    
    // 停止网络状态轮询
    stopNetworkPolling() {
      if (this.networkPollingTimer) {
        clearInterval(this.networkPollingTimer)
        this.networkPollingTimer = null
        console.log('🛑 [NetworkStore] 网络状态轮询已停止')
      }
    },

    // 手动检查网络状态
    checkNetworkStatus() {
      console.log('🔍 [NetworkStore] 手动检查网络状态')
      uni.getNetworkType({
        success: (res) => {
          const isConnected = res.networkType !== 'none'
          console.log('🔍 [NetworkStore] 手动检查结果:', { 
            networkType: res.networkType, 
            isConnected,
            当前状态: this.isConnected,
            弹窗状态: this.showOfflineModal
          })
          
          if (this.isConnected !== isConnected) {
            // 状态变化，使用updateNetworkStatus处理
            this.updateNetworkStatus(isConnected, res.networkType)
          } else if (!isConnected && !this.showOfflineModal) {
            // 持续断网但弹窗未显示
            console.log('🔍 [NetworkStore] 手动检查发现持续断网状态，但弹窗未显示')
            this.offlineStartTime = Date.now()
            this.showOfflineModalImmediately()
          }
        },
        fail: (err) => {
          console.error('🔍 [NetworkStore] 手动检查网络状态失败:', err)
          
          if (this.isConnected) {
            // 状态变化，使用updateNetworkStatus处理
            this.updateNetworkStatus(false, 'none')
          } else if (!this.showOfflineModal) {
            // 持续断网但弹窗未显示
            console.log('🔍 [NetworkStore] 手动检查失败，确认断网状态，但弹窗未显示')
            this.showOfflineModalImmediately()
          }
        }
      })
    },

    // 移除网络状态监听
    removeNetworkListener() {
      // 移除系统网络监听
      uni.offNetworkStatusChange()
      
      // 停止轮询
      this.stopNetworkPolling()
      
      // 清理定时器
      if (this.offlineModalTimer) {
        clearTimeout(this.offlineModalTimer)
        this.offlineModalTimer = null
      }
      
      // 重置状态
      this.isUpdating = false
      this.lastUpdateTime = 0
      this.consecutiveOfflineCount = 0
      
      console.log('🌐 [NetworkStore] 网络状态监听已移除')
    },


    

  }
})

// 注册到全局，供网络异常处理使用
if (typeof globalThis !== 'undefined') {
  (globalThis).__networkStore = null
  
  // 延迟注册，等待store实例化
  setTimeout(() => {
    try {
      // 直接使用当前模块导出的useNetworkStore，避免循环引用
      const networkStore = useNetworkStore()
      ;(globalThis).__networkStore = networkStore
      console.log('✅ [NetworkStore] 已注册到全局')
    } catch (error) {
      console.warn('⚠️ [NetworkStore] 全局注册失败:', error)
    }
  }, 100)
}