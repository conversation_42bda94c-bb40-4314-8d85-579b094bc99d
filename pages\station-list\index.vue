<template>
  <view class="station-list">
    <!-- Top navigation bar -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <!-- Header -->
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="handleBack">
          <text class="iconfont icon-back"></text>
        </view>
        <text class="title">{{ $t('station.chargingStation') || 'Station de recharge' }}</text>
      </view>
    </view>

    <!-- Search bar -->
    <view class="search-section">
      <view class="search-bar">
        <image src="/static/images/search.png" class="search-icon" />
        <input
          type="text"
          v-model="searchKeyword"
          :placeholder="$t('station.shellStationAddress') || 'Adresse de la station Shell'"
          class="search-input"
          @input="handleSearchInput"
          @confirm="handleSearchConfirm"
          @focus="onSearchFocus"
          @blur="onSearchBlur"
        />
        <view class="clear-btn" v-if="searchKeyword" @click="clearSearch">
          <image src="/static/images/close.png" />
        </view>
      </view>
      <view class="menu-btn" @tap="handleMapView">
        <image src="/static/images/map-mode.png" class="menu-icon" />
      </view>
    </view>

    <!-- Filter options -->
    <view class="filter-options">
      <view class="filter-dropdowns">
        <view 
          class="filter-btn" 
          :class="{ 'active': activeFilter === 'charging' }" 
          @tap="toggleChargingMethod"
        >
          <text class="label">{{ $t('station.chargingMethod') || 'Méthode de recharge' }}</text>
          <text class="iconfont icon-arrow-down"></text>
        </view>
        <view 
          class="filter-btn" 
          :class="{ 'active': activeFilter === 'parking' }" 
          @tap="toggleParkingFee"
        >
          <text class="label">{{ $t('station.parkingFee') || 'Frais de stationnement' }}</text>
          <text class="iconfont icon-arrow-down"></text>
        </view>
        <view class="filter-button" @tap="handleFilter">
          <text>{{ $t('station.filter') || 'Filtres' }}</text>
          <image src="/static/images/filter.png" class="filter-icon" />
        </view>
      </view>
    </view>

    <!-- 充电方式选择弹窗 -->
    <view class="popup-mask" v-if="showChargingMethodPopup" @tap="closeChargingMethodPopup"></view>
    <view class="popup-container charging-method-popup" v-if="showChargingMethodPopup">
      <view class="popup-content">
        <!-- 充电方式选项 -->
        <view class="charging-options">
          <view
            class="charging-option"
            :class="{ 'active': selectedChargingMethod === 'fast' }"
            @tap="selectChargingMethod('fast')"
          >
            <text>{{ $t('charging.fastCharging') }}</text>
          </view>
          <view
            class="charging-option"
            :class="{ 'active': selectedChargingMethod === 'slow' }"
            @tap="selectChargingMethod('slow')"
          >
            <text>{{ $t('charging.slowCharging') }}</text>
          </view>
        </view>
        
        <!-- 分隔线 -->
        <view class="divider"></view>
        
        <!-- 按钮区域 -->
        <view class="popup-actions">
          <view class="popup-btn reset-btn" @tap="resetChargingMethod">
            <text>{{ $t('common.reset') || 'Réinitialiser' }}</text>
          </view>
          <view class="popup-btn confirm-btn" @tap="confirmChargingMethod">
            <text>{{ $t('common.confirm') || 'Confirmer' }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 站点列表 -->
    <scroll-view scroll-y class="station-list-scroll" refresher-enabled @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
      @scrolltolower="onLoadMore">
      <view class="station-cards">
        <!-- 加载中状态 -->
        <view class="loading-container" v-if="loading && stations.length === 0">
          <view class="loading-spinner"></view>
          <text class="loading-text">{{ $t('common.loading') || 'Chargement...' }}</text>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-else-if="stations.length === 0">
          <image src="/static/images/no-results.png" class="empty-icon" />
          <text class="empty-title">{{ $t('station.noStationsFound') || 'Aucune station trouvée' }}</text>
          <text class="empty-desc">{{ $t('common.tryAgain') || 'Veuillez réessayer' }}</text>
        </view>
        
        <!-- 站点列表 -->
        <view 
          class="station-card" 
          v-for="(station, index) in stations" 
          :key="index"
          @tap="handleStationSelect(station)"
          :class="{'animate-in': showAnimation}"
        >
          <view class="station-info">
            <!-- 站点名称和距离 -->
            <view class="station-header">
              <text class="station-name">{{ station.name }}</text>
              <view class="distance-wrapper">
                <image src="/static/images/address.png" class="distance-icon" />
                <text class="distance">{{ station.distance }}km</text>
              </view>
            </view>
            <!-- 充电类型和停车信息 -->
            <view class="charging-info">
              <view class="parking-tag">
                <text class="parking-icon">P</text>
                <text>{{ station.parkingInfo }}</text>
              </view>
              <text class="charging-type">{{ station.chargingType }}</text>
              <view class="status-info">
                <text class="status" :class="station.status">{{ getStatusText(station.status) }}</text>
                <text class="count">{{ station.available }}/{{ station.total }}</text>
              </view>
            </view>
            <!-- 价格信息 -->
            <view class="price-section">
              <view class="regular-price">
                <text class="amount">{{ formatPrice(station.price) }}</text>
                <text class="unit">F/kWh</text>
              </view>
              <view class="vip-price" v-if="station.vipPrice">
                <image src="/static/images/VIP.png" class="vip-icon" />
                <text class="amount">{{ formatPrice(station.vipPrice) }}</text>
                <text class="unit">F/kWh</text>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 加载更多指示器 -->
        <view class="loading-more" v-if="loading && stations.length > 0">
          <view class="loading-spinner"></view>
          <text>{{ $t('common.loading') || 'Chargement...' }}</text>
        </view>
        
        <!-- 没有更多数据提示 -->
        <view class="no-more" v-if="!loading && !pagination.hasMore && stations.length > 0">
          <text>{{ $t('common.noMoreData') || 'Aucune donnée supplémentaire' }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue'
import { getChargingStationList } from '@/api'
import { useUserStore } from '@/store/user'
import { useI18n } from '@/composables/useI18n.js'

// 使用国际化
const { t } = useI18n()

// 状态栏高度
const statusBarHeight = ref(0)
// 下拉刷新状态
const isRefreshing = ref(false)
// 当前激活的筛选按钮
const activeFilter = ref('charging') // 可选值：'charging' 或 'parking'
// 充电方式弹窗状态
const showChargingMethodPopup = ref(false)
// 选中的充电方式
const selectedChargingMethod = ref('')
// 最终确认的充电方式（用于筛选）
const confirmedChargingMethod = ref('')
// 搜索关键词
const searchKeyword = ref('')
// 加载状态
const loading = ref(false)
// 用户位置
const userLocation = reactive({
  lat: 48.8566, // 默认位置（巴黎）
  lng: 2.3522
})
// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  hasMore: true
})
// 动画标志
const showAnimation = ref(false)

// 充电站列表
const stations = ref([])

// 用户状态
const userStore = useUserStore()

// 页面加载时获取状态栏高度和初始化数据
onMounted(async () => {
  const sysInfo = uni.getSystemInfoSync()
  statusBarHeight.value = sysInfo.statusBarHeight
  
  // 获取用户位置
  await getCurrentLocation()
  
  // 获取充电站列表
 await fetchChargingStationList()
  
  // 启用动画
  setTimeout(() => {
    showAnimation.value = true
  }, 100)
})

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  fetchChargingStationList(true)
}

// 搜索框获得焦点
const onSearchFocus = () => {
  // 处理搜索框获得焦点
}

// 搜索框失去焦点
const onSearchBlur = () => {
  // 处理搜索框失去焦点
}

// 获取当前位置（针对国际用户优化）
const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    console.log('开始获取用户位置（国际坐标系）...')

    // 使用WGS84坐标系（国际标准）
    uni.getLocation({
      type: 'wgs84', // 使用国际标准坐标系
      isHighAccuracy: true, // 启用高精度定位
      timeout: 15000, // 增加超时时间
      maximumAge: 0, // 不使用缓存位置
      success: (res) => {
        console.log('WGS84定位成功:', res)

        // 验证定位精度和合理性
        const isHighAccuracy = res.accuracy && res.accuracy < 1000
        const isValidCoordinate = Math.abs(res.latitude) <= 90 && Math.abs(res.longitude) <= 180

        if (isHighAccuracy && isValidCoordinate) {
          userLocation.lat = res.latitude
          userLocation.lng = res.longitude
          console.log('✅ 高精度定位成功，精度:', res.accuracy + 'm')
          resolve(res)
        } else {
          console.warn('⚠️ 定位精度不足，尝试浏览器GPS定位')
          tryBrowserGeolocation(resolve, reject)
        }
      },
      fail: (err) => {
        console.error('WGS84定位失败:', err)
        tryBrowserGeolocation(resolve, reject)
      }
    })
  })
}

// 备用定位方案
const tryBrowserGeolocation = (resolve, reject) => {
  console.log('尝试浏览器GPS定位...')

  if (navigator && navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('浏览器GPS定位成功:', position)
        const lat = position.coords.latitude
        const lng = position.coords.longitude
        const accuracy = position.coords.accuracy

        // 验证定位合理性
        const isReasonable = lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180

        if (isReasonable && accuracy < 5000) {
          userLocation.lat = lat
          userLocation.lng = lng
          console.log('✅ 浏览器GPS定位验证通过，精度:', accuracy + 'm')
          resolve({ latitude: lat, longitude: lng, accuracy })
        } else {
          console.warn('⚠️ 浏览器定位结果异常，使用默认位置')
          useDefaultLocation(resolve)
        }
      },
      (error) => {
        console.error('浏览器定位失败:', error)
        useDefaultLocation(resolve)
      },
      {
        enableHighAccuracy: true, // 强制使用GPS
        timeout: 20000,
        maximumAge: 0
      }
    )
  } else {
    useDefaultLocation(resolve)
  }
}

// 默认定位方案（保持默认坐标）
const useDefaultLocation = (resolve) => {
  console.log('使用默认定位方案，保持默认坐标')
  console.log('🌍 使用默认位置:', userLocation.lat, userLocation.lng)
  resolve({
    latitude: userLocation.lat,
    longitude: userLocation.lng,
    useDefaultLocation: true,
    message: 'Using default location (Paris)'
  })
}

// 获取充电站列表
const fetchChargingStationList = async (reset = true) => {
  try {
    loading.value = true

    if (reset) {
      pagination.pageNum = 1
      pagination.hasMore = true
    }

    if (!pagination.hasMore) {
      loading.value = false
      return
    }

    // 确保有有效的用户位置
    if (!userLocation.lat || !userLocation.lng ||
        userLocation.lat === null || userLocation.lng === null) {
      console.log('📍 用户位置无效，重新获取定位...')
      try {
        await getCurrentLocation()
      } catch (error) {
        console.warn('📍 重新获取定位失败:', error)
      }
    }

    // 构建请求参数
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      isAsc: 'asc',
      orderByColumn: 'distance',
      _t: Date.now() // 添加时间戳，确保每次请求都是唯一的
    }

    // 验证坐标有效性并添加coordinate参数
    const isValidLat = userLocation.lat !== null && userLocation.lat !== undefined && !isNaN(userLocation.lat)
    const isValidLng = userLocation.lng !== null && userLocation.lng !== undefined && !isNaN(userLocation.lng)

    if (isValidLat && isValidLng) {
      params.coordinate = `${userLocation.lng},${userLocation.lat}`
      console.log('✅ 使用用户坐标:', params.coordinate)
    } else {
      console.log('⚠️ 用户坐标无效，详细信息:')
      console.log('  - userLocation.lat:', userLocation.lat, typeof userLocation.lat, 'isValid:', isValidLat)
      console.log('  - userLocation.lng:', userLocation.lng, typeof userLocation.lng, 'isValid:', isValidLng)
      console.log('⚠️ 不传递coordinate参数')
    }
    
    // 如果有搜索关键词，添加到请求参数
    if (searchKeyword.value) {
      params.plotName = searchKeyword.value
    }
    
    // 如果有选中的充电方式，添加到请求参数
    if (confirmedChargingMethod.value) {
      params.chargingMethod = confirmedChargingMethod.value === 'fast' ? '1' : '0'
    }
    
    console.log('获取充电站列表参数:', params)
    
    // 调用API获取充电站列表
    const response = await getChargingStationList(params, {
      cancelToken: null // 不使用默认的取消令牌
    })
    
    console.log('充电站列表响应:', response)
    
    if (response && response.code === 200 && response.rows) {
      const newStations = response.rows.map(item => ({
        id: item.plotId,
        name: item.plotName || 'Charging Station',
        distance: formatDistance(item.distance),
        status: item.plotStatus === '0' ? 'available' : 'occupied',
        available: item.countMap?.fastCount || 0,
        total: item.countMap?.fastSumCount || 0,
        price: item.price || '0',
        vipPrice: item.vipPrice || '0',
        chargingType: `GBT DC ${item.pilePowerType === '5' ? '50KW' : '40KW'}`,
        parkingInfo: item.freeTime ? `Free${item.freeTime} hour` : t('station.parkingAvailable') || 'Parking disponible',
        plotNum: item.plotNum
      }))
      
      if (reset) {
        stations.value = newStations
      } else {
        stations.value = [...stations.value, ...newStations]
      }
      
      pagination.total = response.total || 0
      pagination.hasMore = stations.value.length < pagination.total
      pagination.pageNum++
    } else {
      uni.showToast({
        title: t('toast.getStationListFailed') || 'Échec de l\'obtention de la liste des stations de recharge',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取充电站列表失败:', error)
    uni.showToast({
      title: t('toast.getStationListFailed') || 'Échec de l\'obtention de la liste des stations de recharge',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 格式化距离
const formatDistance = (distance) => {
  if (distance === undefined || distance === null) return '0'
  
  // 如果已经是字符串且包含单位，直接返回
  if (typeof distance === 'string' && (distance.includes('km') || distance.includes('m'))) {
    return distance.replace('km', '')
  }
  
  // 转换为数字
  const distanceNum = parseFloat(distance)
  
  if (isNaN(distanceNum)) return '0'
  
  // 如果大于等于1公里，显示为公里
  if (distanceNum >= 1000) {
    return (distanceNum / 1000).toFixed(2)
  }
  
  // 否则显示为米，但转换为公里
  return (distanceNum / 1000).toFixed(2)
}

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 切换到地图视图
const handleMapView = () => {
  uni.navigateTo({
    url: '/pages/map/index'
  })
}

// 切换充电方式下拉
const toggleChargingMethod = () => {
  activeFilter.value = 'charging'
  showChargingMethodPopup.value = true
}

// 关闭充电方式弹窗
const closeChargingMethodPopup = () => {
  showChargingMethodPopup.value = false
}

// 选择充电方式
const selectChargingMethod = (method) => {
  selectedChargingMethod.value = method
}

// 重置充电方式选择
const resetChargingMethod = () => {
  selectedChargingMethod.value = ''
  confirmedChargingMethod.value = ''
  closeChargingMethodPopup()
  fetchChargingStationList(true)
}

// 确认充电方式选择
const confirmChargingMethod = () => {
  confirmedChargingMethod.value = selectedChargingMethod.value
  closeChargingMethodPopup()
  fetchChargingStationList(true)
}

// 切换停车费用下拉
const toggleParkingFee = () => {
  activeFilter.value = 'parking'
  // 这里可以添加显示停车费用弹窗的逻辑
}

// 处理筛选按钮点击
const handleFilter = () => {
  uni.navigateTo({
    url: '/pages/filter/index'
  })
}

// 处理站点选择
const handleStationSelect = (station) => {
  console.log('station',station);
  
  uni.navigateTo({
    url: `/pages/station-detail/index?plotId=${station.id}&plotNum=${station.plotNum}`
  })
}

// 处理搜索输入
const handleSearchInput = (e) => {
  searchKeyword.value = e.detail.value
}

// 处理搜索确认
const handleSearchConfirm = () => {
  fetchChargingStationList(true)
}

// 处理下拉刷新
const onRefresh = async () => {
  isRefreshing.value = true
  try {
    await fetchChargingStationList(true)
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    isRefreshing.value = false
  }
}

// 处理上拉加载更多
const onLoadMore = async () => {
  if (loading.value || !pagination.hasMore) return
  
  await fetchChargingStationList(false)
}

// 处理用户中心点击
const handleUserCenter = () => {
  uni.navigateTo({
    url: '/pages/user/index'
  })
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'available':
      return t('station.idle') || 'Complet'
    case 'occupied':
      return t('station.occupied') || 'Occupé'
    case 'offline':
      return t('station.offline') || 'Hors ligne'
    default:
      return status
  }
}

// 格式化价格，去除小数点
const formatPrice = (price) => {
  if (!price) return '0'
  const numPrice = parseFloat(price)
  if (isNaN(numPrice)) return '0'
  return Math.round(numPrice).toString()
}
</script>

<style lang="scss" scoped>
.station-list {
  min-height: 100vh;
  background: #F8F8F8;
}

.status-bar {
  background-color: #fff;
}

.header {
  padding: 20rpx 32rpx 0;
  background-color: #fff;

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;

    .back-btn {
      position: absolute;
      left: 0;
      font-size: 32rpx;
      color: #000;
      cursor: pointer;
    }

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #000;
    }
  }
}

/* 搜索栏样式 */
.search-section {
  padding: 0 32rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 0;
  background: #FFFFFF;

  .search-bar {
    flex: 1;
    height: 80rpx;
    background-color: #F5F5F5;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    cursor: pointer;

    .search-icon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 12rpx;
    }

    .search-input {
      flex: 1;
      height: 100%;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }

  .menu-btn {
    width: 80rpx;
    height: 80rpx;
    background-color: #F5F5F5;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .menu-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}

/* 筛选选项样式 */
.filter-options {
  padding: 12rpx 32rpx;
  background: #FFFFFF;
  border-bottom: 1px solid #F5F5F5;

  .filter-dropdowns {
    display: flex;
    align-items: center;
  }

  .filter-btn {
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 24rpx;
    background: #FFFFFF;
    border-radius: 32rpx;
    gap: 12rpx;
    margin-right: 16rpx;
    min-width: 180rpx;
    transition: all 0.2s ease;
    
    .label {
      font-size: 28rpx;
      color: #333;
      transition: color 0.2s ease;
    }

    .icon-arrow-down {
      font-size: 24rpx;
      color: #333;
      transition: color 0.2s ease;
    }

    &.active {
      background: #E8F4FF;
      .label {
        color: #0088FF;
      }
      .icon-arrow-down {
        color: #0088FF;
      }
    }
  }

  .filter-button {
    display: flex;
    align-items: center;
    gap: 8rpx;
    margin-left: auto;
    
    text {
      font-size: 28rpx;
      color: #333;
    }

    .filter-icon {
      width: 28rpx;
      height: 28rpx;
    }
  }
}

/* 站点列表滚动区域 */
.station-list-scroll {
  height: calc(100vh - 340rpx); // 调整高度以适应新的头部布局
}

/* 站点卡片列表样式 */
.station-cards {
  padding: 16rpx 32rpx;
}

/* 站点卡片样式 */
.station-card {
  background: #FFFFFF;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

  .station-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
  }

  .station-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .distance-wrapper {
    display: flex;
    align-items: center;
    gap: 4rpx;
  }

  .distance-icon {
    width: 24rpx;
    height: 24rpx;
  }

  .distance {
    font-size: 28rpx;
    color: #666;
  }

  .charging-info {
    display: flex;
    align-items: center;
    gap: 24rpx;
    margin-bottom: 24rpx;
  }

  .parking-tag {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 4rpx 12rpx;
    background: #F5F5F5;
    border-radius: 8rpx;
  }

  .parking-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    background: #666;
    color: #FFF;
    border-radius: 4rpx;
    font-size: 20rpx;
  }

  .parking-tag text {
    font-size: 24rpx;
    color: #666;
  }

  .charging-type {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
  }

  .status-info {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8rpx;
  }

  .status {
    font-size: 28rpx;
    color: #4CAF50;
  }

  .status.occupied {
    color: #FF9800;
  }

  .status.offline {
    color: #999;
  }

  .status.available {
    color: #4CAF50;
  }

  .count {
    font-size: 28rpx;
    color: #666;
  }

  .price-section {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
  }

  .regular-price {
    display: flex;
    align-items: baseline;
    gap: 8rpx;
  }

  .regular-price .amount {
    font-size: 48rpx;
    font-weight: 600;
    color: #FF0000;
  }

  .regular-price .unit {
    font-size: 24rpx;
    color: #666;
  }

  .vip-price {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 4rpx 12rpx;
    background: rgba(184, 134, 11, 0.1);
    border-radius: 8rpx;
    width: fit-content;
  }

  .vip-price .vip-icon {
    width: 32rpx;
    height: 20rpx;
  }

  .vip-price .amount {
    font-size: 28rpx;
    font-weight: 500;
    color: #B8860B;
  }

  .vip-price .unit {
    font-size: 24rpx;
    color: #B8860B;
  }
}

/* 弹窗遮罩层 */
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* 弹窗容器 */
.popup-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  z-index: 1000;
}

.charging-method-popup {
.popup-content {
    padding: 32rpx;
}

.charging-options {
  display: flex;
    flex-direction: column;
    gap: 24rpx;
  margin-bottom: 32rpx;
}

.charging-option {
    height: 100rpx;
  display: flex;
  align-items: center;
    padding: 0 32rpx;
  border-radius: 16rpx;
    background-color: #FFFFFF;
    border: 2rpx solid #EEEEEE;
    transition: all 0.3s ease;

  text {
      font-size: 34rpx;
    color: #333;
      font-weight: 400;
  }

  &.active {
      background-color: #E8F4FF;
      border-color: #E8F4FF;
    
    text {
      color: #0088FF;
        font-weight: 600;
    }
  }
}

.divider {
    height: 2rpx;
    background-color: #EEEEEE;
    margin: 0 0 32rpx;
}

.popup-actions {
  display: flex;
    gap: 24rpx;
}

  .popup-btn {
  flex: 1;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;

  text {
      font-size: 34rpx;
      font-weight: 500;
  }
}

.reset-btn {
    background-color: #F5F5F5;
  
  text {
      color: #666;
  }
}

.confirm-btn {
    background-color: #FF5B57;
  
  text {
      color: #FFFFFF;
    }
  }
}

/* 移除之前添加的分隔线样式 */
.charging-method-popup .popup-content::after {
  display: none;
}

/* 加载指示器和空状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF0000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  width: 320rpx;
  height: 320rpx;
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #868E96;
  text-align: center;
  margin-bottom: 32rpx;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-btn image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

.station-card {
  transform: translateY(20rpx);
  opacity: 0;
  transition: all 0.3s;
}

.station-card.animate-in {
  transform: translateY(0);
  opacity: 1;
}

.station-card:active {
  transform: scale(0.98);
}

.loading-more {
  text-align: center;
  padding: 32rpx;
  color: #999;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-spinner {
    width: 32rpx;
    height: 32rpx;
    margin: 0 16rpx 0 0;
  }
}

.no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>