<template>
    <view class="coupon-container">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="'height:' + statusBarHeight + 'px'"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <view class="header-title">
                        {{ t('coupon.title') }}
                    </view>
                    <view class="placeholder"></view>
                </view>
            </view>
        </view>

        <!-- Tab切换区域 -->
        <view class="tab-switch">
            <view :class="['tab-btn', currentTab === 'unused' ? 'active' : '']" @click="switchTab('unused')">{{ t('coupon.unused') }}</view>
            <view :class="['tab-btn', currentTab === 'used' ? 'active' : '']" @click="switchTab('used')">{{ t('coupon.used') }}</view>
        </view>

        <!-- 主内容卡片 -->
        <view class="main-card">
            <!-- 优惠券列表 -->
            <view class="coupon-list">
                <view 
                    v-for="(coupon, index) in coupons" 
                    :key="coupon.id || coupon.cid" 
                    :class="['coupon-item', getCouponClass(coupon)]"
                >
                    <view :class="['coupon-left', getCouponLeftClass(coupon)]">
                        <text class="coupon-amount">{{ getCouponAmount(coupon) }}</text>
                        <text class="coupon-currency">{{ getCouponCurrency(coupon) }}</text>
                        <text class="coupon-condition">{{ getCouponCondition(coupon) }}</text>
                    </view>
                    <view class="coupon-right">
                        <view class="coupon-top">
                            <text class="coupon-title">{{ getCouponTitle(coupon) }}</text>
                        </view>
                        <view class="coupon-bottom">
                            <!-- Unused页面显示格式化时间，Used页面显示原始endTime -->
                            <text class="coupon-validity" v-if="currentTab === 'unused'">{{ getCouponValidity(coupon) }}</text>
                            <text class="coupon-validity" v-else-if="currentTab === 'used'">{{ coupon.endTime || '' }}</text>
                            <!-- 领取中心Tab - 根据hasTaken字段和过期状态判断 -->
                            <template v-if="currentTab === 'unused'">
                                <!-- 过期了显示sold-out图标 -->
                                <view v-if="isCouponExpired(coupon)" class="all-gone-icon" style="background-image:url('/static/images/sold-out.png')"></view>
                                <!-- hasTaken=true 已领取显示received_already图标 -->
                                <view v-else-if="coupon.hasTaken === true" class="claimed-icon" style="background-image:url('/static/images/received_already.png')"></view>
                                <!-- hasTaken=false 未领取显示领取按钮 -->
                                <button v-else-if="coupon.hasTaken === false" class="claim-btn" @click="claimCoupon(coupon)">Claim</button>
                            </template>
                            <!-- 我的优惠券Tab - 优先判断过期时间，再判断status -->
                            <template v-if="currentTab === 'used'">
                                <!-- 过期了显示sold-out图标 (优先级最高) -->
                                <view v-if="isCouponExpired(coupon)" class="all-gone-icon" style="background-image:url('/static/images/sold-out.png')"></view>
                                <!-- status=1 已使用显示received_already图标 -->
                                <view v-else-if="coupon.status === '1' || coupon.status === 1" class="claimed-icon" style="background-image:url('/static/images/received_already.png')"></view>
                                <!-- status=0 未使用不显示任何按钮或图标 -->
                            </template>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 一键领取按钮，仅在Unused下显示 -->
            <!-- <button v-if="currentTab === 'unused'" class="claim-all-btn" @click="claimAllCoupons">One - click Claim of Coupons</button> -->
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import Request from '@/utils/request'

// 国际化
const { t } = useI18n()

const statusBarHeight = ref(20)
const currentTab = ref('unused')
const coupons = ref([])

onMounted(() => {
    try {
        const systemInfo = uni.getSystemInfoSync()
        if (systemInfo && systemInfo.statusBarHeight) {
            statusBarHeight.value = systemInfo.statusBarHeight
        }
    } catch (e) {
        console.error('获取系统信息失败', e)
    }
    fetchCoupons()
})

function switchTab(tab) {
    if (currentTab.value !== tab) {
        currentTab.value = tab
        fetchCoupons()
    }
}

function fetchCoupons() {
    if (currentTab.value === 'unused') {
        // 领取中心
        Request.get('/coupon/storeCoupon/receive/list')
            .then(res => {
                coupons.value = Array.isArray(res.data) ? res.data : []
            })
            .catch(() => {
                coupons.value = []
                uni.showToast({ title: 'Failed to load coupons', icon: 'none' })
            })
    } else {
        // 我的优惠券
        Request.get('/app/coupon/page', { params: { pageNum: 1, pageSize: 50, isAsc: 'asc' } })
            .then(res => {
                coupons.value = Array.isArray(res.rows) ? res.rows : []
            })
            .catch(() => {
                coupons.value = []
                uni.showToast({ title: 'Failed to load coupons', icon: 'none' })
            })
    }
}

function claimCoupon(coupon) {
    if (!coupon.id) return
    Request.get('/app/coupon/receiveCoupon', { params: { couponId: coupon.id } })
        .then(res => {
            uni.showToast({ title: 'Coupon Claimed', icon: 'success' })
            fetchCoupons()
        })
        .catch(err => {
            uni.showToast({ title: err.message || 'Claim failed', icon: 'none' })
        })
}

function claimAllCoupons() {
    const canClaim = coupons.value.filter(c => !isCouponTaken(c) && !isCouponExpired(c))
    if (canClaim.length === 0) {
        uni.showToast({ title: 'No coupons to claim', icon: 'none' })
        return
    }
    Promise.all(canClaim.map(c => Request.get('/app/coupon/receiveCoupon', { params: { couponId: c.id } })))
        .then(() => {
            uni.showToast({ title: 'All Coupons Claimed', icon: 'success' })
            fetchCoupons()
        })
        .catch(() => {
            uni.showToast({ title: 'Some claims failed', icon: 'none' })
            fetchCoupons()
        })
}

// 工具函数：状态判断和字段兼容
function isCouponTaken(coupon) {
    // 领取中心接口
    if (currentTab.value === 'unused') {
        return coupon.hasTaken === true
    }
    // 我的券接口
    return coupon.status === '1' || coupon.status === 1
}
function isCouponExpired(coupon) {
    // 统一根据endTime判断过期状态
    if (coupon.endTime) {
        return new Date(coupon.endTime.replace(/-/g, '/')).getTime() < Date.now()
    }
    // 如果没有endTime字段，对于我的券接口，fallback到status=2
    if (currentTab.value === 'used') {
        return coupon.status === '2' || coupon.status === 2
    }
    return false
}
function isCouponUsed(coupon) {
    // 我的券接口 - status=1为已使用
    return coupon.status === '1' || coupon.status === 1
}
function getCouponClass(coupon) {
    if (isCouponExpired(coupon)) return 'gone'
    if (isCouponTaken(coupon) || isCouponUsed(coupon)) return 'claimed'
    return ''
}
function getCouponLeftClass(coupon) {
    return isCouponExpired(coupon) ? 'inactive' : ''
}
function getCouponAmount(coupon) {
    return coupon.couponPrice || coupon.amount || 0
}
function getCouponCurrency(coupon) {
    return coupon.currency || 'F'
}
function getCouponCondition(coupon) {
    return coupon.useMinPrice ? `Valid for Orders Over ${coupon.useMinPrice} Yuan` : (coupon.condition || '')
}
function getCouponTitle(coupon) {
    return coupon.title || 'Coupon'
}
function getCouponValidity(coupon) {
    if (coupon.endTime) {
        return `Valid Until ${coupon.endTime.split(' ')[0]}`
    }
    if (coupon.validity) return coupon.validity
    return ''
}
// 方法
const goBack = () => {
  uni.navigateBack()
}

</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.coupon-container {
    min-height: 100vh;
    background-color: #FFF9E8;
    background-image: url('/static/images/coupon-illustration.png');
    background-repeat: no-repeat;
    background-size: 80% auto;
    background-position: top -20px center;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 0 0 32rpx;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: transparent;
}

.status-bar {
    background-color: transparent;
    width: 100%;
}

.header {
    background: transparent;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 32rpx;
        
        .back-btn {
            width: 60rpx;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .header-title {
            font-size: 34rpx;
            font-weight: 600;
            color: #333;
        }
        
        .placeholder {
            width: 60rpx;
        }
    }
}

/* Tab切换样式 */
.tab-switch {
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;
    margin-top: 180rpx;
    margin-bottom: 0;
    z-index: 101;
}
.tab-btn {
    font-size: 36rpx;
    font-weight: 600;
    color: #222;
    margin: 0 40rpx;
    padding-bottom: 8rpx;
    position: relative;
    transition: color 0.2s;
    cursor: pointer;
}
.tab-btn.active {
    color: #000;
    border-bottom: 6rpx solid #FF4D4F;
    border-radius: 0 0 8rpx 8rpx;
}

.main-card {
    flex: 1;
    margin: 20rpx 32rpx 32rpx;
    padding: 40rpx 32rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.coupon-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    margin-bottom: 40rpx;
}

.coupon-item {
    background-color: #FFF5F5;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
    display: flex;
    overflow: hidden;
    position: relative;
    
    &.claimed {
        .claimed-icon {
            display: block;
        }
    }
    
    &.gone {
        opacity: 0.8;
        
        .all-gone-icon {
            display: block;
        }
    }
    
    .coupon-left {
        background-color: #FFF0F0;
        padding: 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 180rpx;
        position: relative;
        
        &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 20rpx;
            background: radial-gradient(circle at right, transparent 0, transparent 10rpx, #FFF5F5 10rpx, #FFF5F5 20rpx);
            background-size: 20rpx 20rpx;
            background-position: right center;
        }
        
        &.inactive {
            opacity: 0.7;
            
            .coupon-amount,
            .coupon-currency,
            .coupon-condition {
                color: #999;
            }
        }
        
        .coupon-amount {
            font-size: 60rpx;
            font-weight: 700;
            color: #FF4D4F;
            line-height: 1;
        }
        
        .coupon-currency {
            font-size: 24rpx;
            color: #FF4D4F;
            margin-top: 4rpx;
        }
        
        .coupon-condition {
            font-size: 20rpx;
            color: #666;
            margin-top: 12rpx;
            text-align: center;
            width: 120rpx;
            line-height: 1.4;
        }
    }
    
    .coupon-right {
        flex: 1;
        padding: 30rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        
        .coupon-top {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24rpx;
            
            .coupon-title {
                font-size: 32rpx;
                font-weight: 600;
                color: #333;
            }
        }
        
        .coupon-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .coupon-validity {
                font-size: 24rpx;
                color: #999;
            }
        }
        
        .claim-btn {
            background: #FF4D4F;
            color: white;
            font-size: 28rpx;
            padding: 12rpx 40rpx;
            border-radius: 40rpx;
            border: none;
            line-height: 1.5;
            min-width: 160rpx;
            text-align: center;
            box-shadow: 0 4rpx 8rpx rgba(255, 77, 79, 0.3);
            transition: transform 0.2s;

            &:active {
                transform: scale(0.96);
            }
        }


        
        .claimed-icon {
            width: 100rpx;
            height: 100rpx;
            background-image: url('/static/images/received_already.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            transform: rotate(-15deg);
        }
        
        .all-gone-icon {
            width: 100rpx;
            height: 100rpx;
            background-image: url('/static/images/sold-out.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            transform: rotate(-15deg);
            display: none;
        }
    }
}

.claim-all-btn {
    margin-top: 40rpx;
    background-color: #FF4D4F;
    color: white;
    font-size: 32rpx;
    font-weight: 500;
    padding: 28rpx 0;
    border-radius: 50rpx;
    border: none;
    line-height: 1.5;
    text-align: center;
    box-shadow: 0 6rpx 12rpx rgba(255, 77, 79, 0.3);
    transition: transform 0.2s;
    margin-left: 40rpx;
    margin-right: 40rpx;
    
    &:active {
        transform: scale(0.98);
    }
}
</style> 