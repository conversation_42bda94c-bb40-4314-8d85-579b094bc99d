/**
 * 版本管理工具
 * 用于获取和管理应用版本号
 */

// 从manifest.json中读取版本号
// 注意：在实际打包时，这个值会被构建工具替换为真实的版本号
let MANIFEST_VERSION = '1.0.0'

// 尝试动态读取manifest.json中的版本号
try {
  // 在uni-app中，可以通过以下方式获取应用信息
  // #ifdef APP-PLUS
  if (typeof plus !== 'undefined' && plus.runtime) {
    // 直接使用同步可用的 plus.runtime.version 更可靠
    if (plus.runtime.version) {
      MANIFEST_VERSION = plus.runtime.version
      console.log('从APP运行时获取版本号:', MANIFEST_VERSION)
    }
  }
  // #endif

  // #ifdef H5
  // 在H5环境中，可以从package.json或其他方式获取
  // 这里保持默认值
  // #endif

  // #ifdef MP
  // 在小程序环境中，可以从小程序的配置获取
  // 这里保持默认值
  // #endif
} catch (error) {
  console.warn('动态获取版本号失败，使用默认版本:', error)
}

/**
 * 获取应用版本号
 * 优先级：本地存储 > manifest.json > 默认值
 */
export function getAppVersion() {
  try {
    // 仅使用“可修改”的动态值：本地存储
    const storedVersion = uni.getStorageSync('app_version')
    if (storedVersion) return storedVersion

    // 本地尚未初始化时，返回最小默认值，避免与服务端不一致
    return '0.0.0'
  } catch (error) {
    console.warn('获取应用版本号失败:', error)
    return '0.0.0'
  }
}

/**
 * 设置应用版本号到本地存储
 */
export function setAppVersion(version) {
  try {
    uni.setStorageSync('app_version', version)
    console.log('应用版本号已设置:', version)
    return true
  } catch (error) {
    console.error('设置应用版本号失败:', error)
    return false
  }
}

/**
 * 初始化应用版本号
 * 在应用启动时调用
 * 规则：
 * - 若本地已有 app_version，则直接使用它（绝不回退/覆盖为更低的 manifest 值）
 * - 若本地没有，则尝试使用 plus.runtime.version；否则用 MANIFEST_VERSION
 */
export function initAppVersion() {
  try {
    const storedVersion = uni.getStorageSync('app_version')
    if (storedVersion) {
      console.log('当前版本号(存储):', storedVersion)
      return storedVersion
    }

    const runtimeVersion = (typeof plus !== 'undefined' && plus.runtime && plus.runtime.version)
      ? plus.runtime.version
      : null

    const manifestVersion = runtimeVersion || MANIFEST_VERSION
    setAppVersion(manifestVersion)
    console.log('初始化并保存版本号:', manifestVersion)
    return manifestVersion
  } catch (error) {
    console.error('初始化版本号失败:', error)
    // 设置默认版本号
    setAppVersion(MANIFEST_VERSION)
    return MANIFEST_VERSION
  }
}

/**
 * 比较版本号
 * @param {string} version1 
 * @param {string} version2 
 * @returns {number} 1: version1 > version2, 0: 相等, -1: version1 < version2
 */
export function compareVersion(version1, version2) {
  try {
    const v1Parts = version1.split('.').map(Number)
    const v2Parts = version2.split('.').map(Number)
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length)
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0
      const v2Part = v2Parts[i] || 0
      
      if (v1Part > v2Part) return 1
      if (v1Part < v2Part) return -1
    }
    
    return 0
  } catch (error) {
    console.error('版本号比较失败:', error)
    return 0
  }
}

/**
 * 检查是否有新版本
 * @param {string} newVersion 新版本号
 * @returns {boolean} true: 有新版本, false: 无新版本
 */
export function hasNewVersion(newVersion) {
  const currentVersion = getAppVersion()
  return compareVersion(newVersion, currentVersion) > 0
}
