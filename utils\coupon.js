/**
 * 优惠券工具函数
 *
 * 我的优惠券状态码说明:
 * - status: 0 = 未使用
 * - status: 1 = 已使用
 * - status: 2 = 已过期
 */

/**
 * 优惠券状态枚举
 */
export const COUPON_STATUS = {
  AVAILABLE: 'available',    // 可领取
  TAKEN: 'taken',           // 已领取
  USED: 'used',             // 已使用
  EXPIRED: 'expired'        // 已过期
}

/**
 * 判断优惠券是否已领取
 * @param {Object} coupon - 优惠券对象
 * @param {string} tabType - 当前tab类型 ('unused' | 'used')
 * @returns {boolean}
 */
export const isCouponTaken = (coupon, tabType) => {
  if (tabType === 'unused') {
    // 领取中心接口
    return coupon.hasTaken === true
  }
  // 我的券接口
  return coupon.status === '1' || coupon.status === 1
}

/**
 * 判断优惠券是否已过期
 * @param {Object} coupon - 优惠券对象
 * @param {string} tabType - 当前tab类型 ('unused' | 'used')
 * @returns {boolean}
 */
export const isCouponExpired = (coupon, tabType) => {
  if (tabType === 'unused') {
    // 领取中心接口 - 根据结束时间判断
    if (coupon.endTime) {
      return new Date(coupon.endTime.replace(/-/g, '/')).getTime() < Date.now()
    }
    return false
  }
  // 我的券接口 - status=2为已过期
  return coupon.status === '2' || coupon.status === 2
}

/**
 * 判断优惠券是否已使用
 * @param {Object} coupon - 优惠券对象
 * @returns {boolean}
 */
export const isCouponUsed = (coupon) => {
  return coupon.status === '1' || coupon.status === 1
}

/**
 * 获取优惠券状态
 * @param {Object} coupon - 优惠券对象
 * @param {string} tabType - 当前tab类型
 * @returns {string} 状态
 */
export const getCouponStatus = (coupon, tabType) => {
  if (isCouponExpired(coupon, tabType)) {
    return COUPON_STATUS.EXPIRED
  }
  if (isCouponUsed(coupon)) {
    return COUPON_STATUS.USED
  }
  if (isCouponTaken(coupon, tabType)) {
    return COUPON_STATUS.TAKEN
  }
  return COUPON_STATUS.AVAILABLE
}

/**
 * 获取优惠券CSS类名
 * @param {Object} coupon - 优惠券对象
 * @param {string} tabType - 当前tab类型
 * @returns {string} CSS类名
 */
export const getCouponClass = (coupon, tabType) => {
  const status = getCouponStatus(coupon, tabType)
  switch (status) {
    case COUPON_STATUS.EXPIRED:
      return 'expired'
    case COUPON_STATUS.TAKEN:
    case COUPON_STATUS.USED:
      return 'claimed'
    default:
      return ''
  }
}

/**
 * 获取优惠券金额
 * @param {Object} coupon - 优惠券对象
 * @returns {string|number} 金额
 */
export const getCouponAmount = (coupon) => {
  return coupon.couponPrice || coupon.amount || '0'
}

/**
 * 获取优惠券货币单位
 * @param {Object} coupon - 优惠券对象
 * @returns {string} 货币单位
 */
export const getCouponCurrency = (coupon) => {
  return coupon.currency || 'FCFA'
}

/**
 * 获取优惠券使用条件
 * @param {Object} coupon - 优惠券对象
 * @returns {string} 使用条件
 */
export const getCouponCondition = (coupon) => {
  if (coupon.useMinPrice) {
    return `Valid for Orders Over ${coupon.useMinPrice} Yuan`
  }
  return coupon.condition || ''
}

/**
 * 获取优惠券标题
 * @param {Object} coupon - 优惠券对象
 * @returns {string} 标题
 */
export const getCouponTitle = (coupon) => {
  return coupon.title || 'Coupon'
}

/**
 * 获取优惠券有效期
 * @param {Object} coupon - 优惠券对象
 * @returns {string} 有效期
 */
export const getCouponValidity = (coupon) => {
  if (coupon.endTime) {
    return `Valid Until ${coupon.endTime.split(' ')[0]}`
  }
  return coupon.validity || ''
}

/**
 * 过滤可领取的优惠券
 * @param {Array} coupons - 优惠券列表
 * @param {string} tabType - 当前tab类型
 * @returns {Array} 可领取的优惠券列表
 */
export const getClaimableCoupons = (coupons, tabType) => {
  return coupons.filter(coupon => {
    const status = getCouponStatus(coupon, tabType)
    return status === COUPON_STATUS.AVAILABLE
  })
}
