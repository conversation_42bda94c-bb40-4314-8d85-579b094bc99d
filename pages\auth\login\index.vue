<template>
  <view class="login-page">
    <!-- Logo图片 -->
    <image class="logo" src="/static/images/login_logo.png" mode="aspectFit"></image>

    <!-- 登录方式选项卡 -->
    <view class="login-tabs">
      <text class="tab" :class="{ active: loginType === 'password' }" @click="switchLoginType('password')">{{
        $t('user.password') }}</text>
      <text class="tab" :class="{ active: loginType === 'code' }" @click="switchLoginType('code')">{{
        $t('user.verificationCode') || 'Verification Code Login' }}</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 邮箱/手机号输入框 -->
      <view class="input-item">
        <input type="let" v-model="phoneNumber" :placeholder="$t('user.emailOrPhone')" class="input" />
      </view>

      <!-- 密码输入框 -->
      <view class="input-item" v-if="loginType === 'password'">
        <input :type="showPassword ? 'text' : 'password'" v-model="password" :placeholder="$t('user.password')"
          class="input" />
        <text class="iconfont eye-icon" :class="showPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
          @click="togglePassword"></text>
      </view>

      <!-- 验证码输入框 -->
      <view class="input-item" v-if="loginType === 'code'">
        <input type="number" v-model="verificationCode"
          :placeholder="$t('user.verificationCode') || 'Verification Code'" maxlength="6" class="input" />
        <view v-if="countdown > 0" class="countdown-display">
          <text class="countdown">{{ countdown }}s</text>
        </view>
        <view v-else class="code-btn" :class="{ disabled: isSendingCode }" @click="sendVerificationCode($event)">
          <text>{{ $t('user.send') || 'Send' }}</text>
        </view>
      </view>

      <!-- 忘记密码链接 -->
      <view class="forgot-password" v-if="loginType === 'password'">
        <text @click="goToForgotPassword">{{ $t('user.forgotPassword') }}</text>
      </view>

      <!-- 用户协议 -->
      <view class="agreement">
        <checkbox :checked="agreedToTerms" @click="toggleAgreement" value="agree" />
        <text class="agreement-text" @click="toggleAgreement">
          {{ $t('user.agreementText1') || 'I understand and agree to the Arnio' }}
          <text class="link">{{ $t('settings.privacy') }}</text>
          {{ $t('user.agreementText2') || 'and' }}
          <text class="link">{{ $t('user.termsOfService') || 'Terms of Service' }}</text>
        </text>
      </view>

      <!-- 登录按钮 -->
      <button type="button" class="login-btn" @click="loginbtn">{{ $t('user.login')
      }}</button>

      <!-- 注册链接 -->
      <view class="register">
        <text>{{ $t('user.noAccount') || 'Vous n\'avez pas de compte ?' }}</text>
        <text class="link" @click="goToRegister">{{ $t('user.register') }}</text>
      </view>

      <!-- 第三方登录 -->
      <view class="third-party">
        <view class="title">{{ $t('user.thirdPartyLogin') || 'Third-party software authorized login' }}</view>
        <view class="icons">
          <view class="icon-item">
            <image src="/static/images/google-small.png" mode="aspectFit"></image>
            <text>Google</text>
          </view>
          <view class="icon-item">
            <image src="/static/images/facebook-small.png" mode="aspectFit"></image>
            <text>Facebook</text>
          </view>
          <view class="icon-item">
            <image src="/static/images/apple-small.png" mode="aspectFit"></image>
            <text>Apple</text>
          </view>
        </view>
      </view>
      <!-- 只在本页挂载 HUD，便于先看效果 -->
      <GlobalHUD />
    </view>
  </view>
</template>

<script setup>
import { ref, computed, inject, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { sendSmsCode, loginBySmsCode, passwordLogin } from '@/api'
import { useUserStore } from '@/store/user'
import { useGlobalHud } from '@/composables/useHud'
import GlobalHUD from '@/components/common/GlobalHUD.vue'


// 使用国际化
const { t } = useI18n()


// 全局HUD
const hud = useGlobalHud()


// 使用用户状态store
const userStore = useUserStore()

// 登录类型：password 或 code
const loginType = ref('password')
// 是否显示密码
const showPassword = ref(false)
// 是否记住密码
const rememberPassword = ref(false)
// 是否同意协议
const agreedToTerms = ref(false)
// 是否启用快速登录
const enableQuickLogin = ref(true)

// 表单数据
const phoneNumber = ref('')
const password = ref('')
const verificationCode = ref('')

// 验证码发送状态
const isSendingCode = ref(false)
const countdown = ref(0)
let countdownTimer = null

// 登录状态
const isLoggingIn = ref(false)

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  console.log('验证表单:', {
    agreedToTerms: agreedToTerms.value,
    loginType: loginType.value,
    phoneNumber: phoneNumber.value,
    password: password.value,
    verificationCode: verificationCode.value
  });

  // 始终需要同意协议
  if (!agreedToTerms.value) {
    console.log('未同意协议，表单无效');
    return false;
  }

  if (loginType.value === 'password') {
    // 密码登录需要手机号和密码
    const valid = Boolean(phoneNumber.value && password.value);
    console.log('密码登录表单验证结果:', valid);
    return valid;
  } else {
    // 验证码登录需要手机号和验证码
    const valid = Boolean(phoneNumber.value && verificationCode.value);
    console.log('验证码登录表单验证结果:', valid);
    return valid;
  }
})

// 切换登录类型
const switchLoginType = (type) => {
  loginType.value = type
}

// 切换显示/隐藏密码
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 切换用户协议
const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value
}

// 发送验证码
const sendVerificationCode = async (e) => {
  // 阻止事件冒泡和默认行为
  if (e) {
    e.stopPropagation()
    e.preventDefault()
  }

  // 检查手机号是否为空
  if (!phoneNumber.value) {
    uni.showToast({
      title: t('user.phoneRequired'),
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 验证手机号格式（科特迪瓦格式，以225开头）
  // 科特迪瓦手机号通常是 +225 开头，后面跟8-10位数字
  const phoneRegex = /^225\d{8,10}$/
  if (!phoneRegex.test(phoneNumber.value)) {
    uni.showToast({
      title: t('user.phoneFormatError'),
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 如果正在发送或倒计时中，不允许再次发送
  if (isSendingCode.value || countdown.value > 0) {
    console.log('无法发送验证码：', isSendingCode.value ? '正在发送中' : `倒计时中(${countdown.value}s)`)
    return
  }

  try {
    console.log('开始发送验证码...')
    isSendingCode.value = true

    // 显示轻量HUD
    hud.loading(t('loading.sending'))

    // 调用发送短信验证码API
    console.log('发送验证码到:', phoneNumber.value)
    const response = await sendSmsCode(phoneNumber.value)
    console.log('验证码发送响应:', response)

    // 收起loading并提示成功
    hud.success(t('toast.codeSent'))

    console.log('开始倒计时...')
    startCountdown()
    console.log('倒计时状态:', countdown.value)
  } catch (error) {
    // 隐藏加载提示
    hud.done()
    console.error('发送验证码失败:', error)

    // 显示错误提示
    let errorMessage = t('toast.codeSendFailed')

    if (error.response) {
      console.error('错误响应:', error.response)
      errorMessage = error.response.data?.message || errorMessage
    } else if (error.request) {
      console.error('请求错误:', error.request)
      errorMessage = t('toast.requestFailed')
    } else if (error.message) {
      errorMessage = error.message
    }

    // 显示错误提示（HUD）
    hud.error(errorMessage)
  } finally {
    isSendingCode.value = false
    console.log('发送状态重置，当前倒计时:', countdown.value)
  }
}

// 开始倒计时
const startCountdown = () => {
  // 设置初始倒计时时间
  countdown.value = 60

  // 清除可能存在的定时器
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }

  // 设置新的定时器
  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      // 倒计时结束，清除定时器
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 登录方法
const loginbtn = async () => {

  // 检查是否同意协议
  if (!agreedToTerms.value) {
    console.log('用户协议未勾选');
    uni.showToast({
      title: t('toast.agreeTermsRequired'),
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 检查手机号
  if (!phoneNumber.value) {
    uni.showToast({
      title: t('user.phoneRequired'),
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 根据登录类型执行不同的验证
  if (loginType.value === 'password') {
    // 密码登录，检查密码
    if (!password.value) {
      uni.showToast({
        title: t('user.passwordRequired'),
        icon: 'none',
        duration: 2000
      })
      return
    }
  } else {
    // 验证码登录，检查验证码
    if (!verificationCode.value) {
      uni.showToast({
        title: t('user.verificationCodeRequired'),
        icon: 'none',
        duration: 2000
      })
      return
    }
  }

  // 防止重复登录
  if (isLoggingIn.value) {
    console.log('正在登录中，请勿重复点击');
    return
  }

  isLoggingIn.value = true

  try {
    // HUD加载
    hud.loading(t('loading.authenticating'))

    // 根据登录类型执行不同的登录逻辑
    if (loginType.value === 'password') {
      // 密码登录 - 按照文档接口规范
      console.log('执行密码登录:', { phoneNumber: phoneNumber.value, password: password.value })

      try {
        // 调用密码登录API
        const response = await passwordLogin({
          clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
          grantType: "accountPassword",
          password: password.value,
          rememberMe: false,
          tenantId: "000000",
          username: phoneNumber.value
        })
        console.log('密码登录响应:', response)

        // 处理登录响应
        if (response && (response.code === 200 || response.code === "200") && response.data) {
          // 使用Pinia存储用户信息
          userStore.loginSuccess(response.data)
          
          // 登录成功后根据用户选择保存手机号用于快速登录
          if (phoneNumber.value && enableQuickLogin.value) {
            userStore.savePhoneNumber(phoneNumber.value)
            console.log('手机号已保存用于快速登录:', phoneNumber.value)
          }

          // 同时也保存到本地存储，以便下次启动时恢复
          uni.setStorageSync('userInfo', JSON.stringify(response.data))

          // 如果响应中包含token，单独保存token
          if (response.data.access_token) {
            uni.setStorageSync('token', response.data.access_token)
          } else if (response.data.token) {
            uni.setStorageSync('token', response.data.token)
          }

          console.log('用户信息已保存')
        } else {
          console.log('密码登录响应格式不符合预期:', response)
          // 尝试从其他位置获取token
          if (response && response.token) {
            uni.setStorageSync('token', response.token)
            console.log('Token已保存')
          }
        }
      } catch (apiError) {
        console.error('密码登录API调用失败:', apiError);
        throw apiError;
      }

    } else {
      // 验证码登录
      console.log('执行验证码登录:', { phoneNumber: phoneNumber.value, code: verificationCode.value })

      try {
        // 调用验证码登录API
        const response = await loginBySmsCode(phoneNumber.value, verificationCode.value)
        console.log('登录响应:', response)

        // 检查响应格式
        if (response && (response.code === 200 || response.code === "200") && response.data) {
          // 使用Pinia存储用户信息
          userStore.loginSuccess(response.data)
          
          // 验证码登录成功后根据用户选择保存手机号用于快速登录
          if (phoneNumber.value && enableQuickLogin.value) {
            userStore.savePhoneNumber(phoneNumber.value)
            console.log('手机号已保存用于快速登录:', phoneNumber.value)
          }

          // 同时也保存到本地存储，以便下次启动时恢复
          uni.setStorageSync('userInfo', JSON.stringify(response.data))

          // 如果响应中包含token，单独保存token
          if (response.data.access_token) {
            uni.setStorageSync('token', response.data.access_token)
          } else if (response.data.token) {
            uni.setStorageSync('token', response.data.token)
          }

          console.log('用户信息已保存')
        } else {
          console.log('登录响应格式不符合预期:', response)
          // 尝试从其他位置获取token
          if (response && response.token) {
            uni.setStorageSync('token', response.token)
            console.log('Token已保存')
          }
        }
      } catch (apiError) {
        console.error('API调用失败:', apiError);
        throw apiError;
      }
    }

    // 登录成功提示（HUD）
    hud.success(t('toast.loginSuccess'), 1000)

    console.log('登录成功，准备跳转到首页');

    // 登录成功后跳转
    setTimeout(() => {
      console.log('执行页面跳转');
      uni.reLaunch({
        url: '/pages/home/<USER>',
        success: () => {
          console.log('页面跳转成功');
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
        }
      })
    }, 1500)

  } catch (error) {
    // 隐藏加载提示
    hud.done()
    console.error('登录失败:', error)

    // 显示错误提示
    let errorMessage = t('user.loginFailed')

    if (error.response) {
      console.error('错误响应:', error.response)
      errorMessage = error.response.data?.msg || error.response.data?.message || t('user.loginFailed')
    } else if (error.request) {
      console.error('请求错误:', error.request)
      errorMessage = t('errors.networkError')
    } else if (error.message) {
      errorMessage = error.message
    }

    // 显示错误提示（HUD）
    hud.error(errorMessage)
  } finally {
    console.log('登录流程结束，重置登录状态');
    isLoggingIn.value = false
  }
}

// 跳转到注册页面
const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/auth/register/phone'
  })
}

// 跳转到忘记密码页面
const goToForgotPassword = () => {
  uni.navigateTo({
    url: '/pages/auth/forgot-password/index'
  })
}

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})
</script>

<style lang="less">
// Less 变量定义
@primary-color: #f23030;
@text-color: #000000;
@text-color-secondary: #999999;
@font-size-small: 24rpx;
@font-size-base: 28rpx;
@font-size-large: 32rpx;
@font-size-xlarge: 36rpx;

// Less 混入定义
.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-page {
  min-height: 100vh;
  height: 100vh;
  background-color: #fff;
  padding: 0 40rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: fixed; // 固定定位
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

  .logo {
    width: 200rpx;
    height: 160rpx;
    margin: 120rpx auto 60rpx; // 增加上边距，让整体内容往下移
    display: block;
    flex-shrink: 0;
  }

  .login-tabs {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 60rpx; // 增加间距，配合整体下移
    position: relative;
    flex-shrink: 0; // 防止压缩

    .tab {
      font-size: @font-size-large;
      color: @text-color-secondary;
      padding: 20rpx 16rpx;
      position: relative;
      transition: all 0.3s;
      white-space: nowrap;

      &.active {
        color: @text-color;
        font-size: @font-size-xlarge;
        font-weight: 600;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background-color: @text-color;
        }
      }

      &:first-child {
        margin-right: 40rpx;
      }
    }
  }

  .login-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative; // 相对定位
    overflow: hidden; // 防止内容溢出

    .input-item {
      position: relative;
      margin-bottom: 20rpx; // 减少间距
      box-sizing: border-box;
      flex-shrink: 0; // 防止压缩

      .input {
        width: 100%;
        height: 96rpx; // 调整输入框高度
        font-size: @font-size-large;
        color: @text-color;
        padding: 0 112rpx 0 32rpx;
        border: 2rpx solid #E5E5E5;
        border-radius: 16rpx;
        background: transparent;
        box-sizing: border-box;

        &::placeholder {
          color: @text-color-secondary;
          font-size: @font-size-large;
        }
      }

      .eye-icon {
        position: absolute;
        right: 64rpx;
        top: 50%;
        transform: translateY(-50%);
        color: @text-color-secondary;
        font-size: @font-size-xlarge;
        z-index: 1;
        .flex-center();
        width: 48rpx;
        height: 48rpx;
      }

      .code-btn {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        color: @primary-color;
        font-size: @font-size-base;
        padding: 20rpx 30rpx;
        cursor: pointer;
        min-width: 100rpx;
        text-align: center;

        &.disabled {
          color: #ccc;
          cursor: not-allowed;
        }

        .countdown {
          font-size: @font-size-base;
          font-weight: bold;
          color: #999;
        }
      }

      .countdown-display {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        padding: 20rpx 30rpx;
        min-width: 100rpx;
        text-align: center;
        z-index: 1;

        .countdown {
          font-size: @font-size-base;
          font-weight: bold;
          color: #999;
        }
      }
    }

    .forgot-password {
      text-align: right;
      margin: 16rpx 0 30rpx; // 调整间距
      flex-shrink: 0; // 防止压缩

      text {
        color: #666;
        font-size: @font-size-base;
      }
    }

    .agreement {
      display: flex;
      align-items: flex-start;
      margin-bottom: 30rpx; // 调整间距
      flex-shrink: 0; // 防止压缩

      checkbox {
        transform: scale(0.8);
        margin-top: 6rpx;
      }

      .agreement-text {
        font-size: @font-size-small;
        color: #666;
        margin-left: 10rpx;
        line-height: 1.6;

        .link {
          color: @text-color;
          text-decoration: underline;
        }
      }
    }

    .login-btn {
      width: 100%;
      height: 90rpx;
      background: @primary-color;
      color: #fff;
      border-radius: 45rpx;
      font-size: @font-size-large;
      .flex-center();
      border: none;
      margin: 40rpx 0; // 调整间距
      flex-shrink: 0; // 防止压缩
      padding: 0;
      position: relative; // 添加相对定位
      z-index: 10; // 确保按钮在最上层

      &:disabled {
        background: #ffcccb;
        opacity: 0.7;
      }

      &:active {
        opacity: 0.9; // 点击效果
      }
    }

    .register {
      text-align: center;
      margin-bottom: 40rpx; // 减少底部间距
      flex-shrink: 0;

      text {
        font-size: @font-size-base;
        color: #666;
      }

      .link {
        color: @primary-color;
        margin-left: 10rpx;
      }
    }

    .third-party {
      position: relative;
      margin-top: auto; // 推到底部
      padding-bottom: 60rpx; // 增加底部间距

      .title {
        text-align: center;
        font-size: @font-size-small;
        color: #999;
        margin-bottom: 30rpx;
        position: relative;

        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          width: 120rpx;
          height: 1rpx;
          background: #eee;
        }

        &::before {
          left: 60rpx;
        }

        &::after {
          right: 60rpx;
        }
      }

      .icons {
        display: flex;
        justify-content: center;
        gap: 120rpx; // 增加图标间距

        .icon-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          image {
            width: 52rpx; // 调整图标大小
            height: 52rpx;
            margin-bottom: 12rpx;
          }

          text {
            font-size: 24rpx;
            color: #333;
          }
        }
      }
    }
  }
}
</style>