<template>
  <view class="name-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
        <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题文本 -->
      <view class="title-section">
        <text class="main-title">{{ $t('auth.enterFullName') }}</text>
        <text class="subtitle">{{ $t('auth.nameWillBeUsedFor') }}</text>
      </view>

      <!-- 姓名输入 -->
      <view class="name-input-section">
        <!-- 姓氏输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <input 
              type="text" 
              class="name-input" 
              :class="{ error: errors.firstName }"
              v-model="firstName"
              :placeholder="$t('user.firstName')" 
              @input="validateFirstName"
              @focus="clearError('firstName')"
              maxlength="30"
            />
            <view v-if="firstName && !errors.firstName" class="success-icon">
              <text class="iconfont icon-check"></text>
            </view>
          </view>
          <text v-if="errors.firstName" class="error-text">{{ errors.firstName }}</text>
        </view>

        <!-- 名字输入 -->
        <view class="input-group">
          <view class="input-wrapper">
            <input 
              type="text" 
              class="name-input" 
              :class="{ error: errors.lastName }"
              v-model="lastName"
              :placeholder="$t('user.lastName')" 
              @input="validateLastName"
              @focus="clearError('lastName')"
              maxlength="30"
            />
            <view v-if="lastName && !errors.lastName" class="success-icon">
              <text class="iconfont icon-check"></text>
            </view>
          </view>
          <text v-if="errors.lastName" class="error-text">{{ errors.lastName }}</text>
        </view>
      </view>

      <!-- 预览显示名 -->
      <view v-if="fullName" class="preview-section">
        <text class="preview-label">{{ $t('auth.displayName') }}:</text>
        <text class="preview-name">{{ fullName }}</text>
      </view>

      <!-- 继续按钮 -->
      <view class="continue-section">
        <button 
          class="continue-btn" 
          :class="{ active: isNameValid, disabled: !isNameValid }"
          :disabled="!isNameValid"
          @click="continueToPassword"
        >
          {{ $t('common.continue') }}
        </button>
      </view>
    </view>

    <!-- 全局HUD -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { useGlobalHud } from '@/composables/useHud'
import GlobalHUD from '@/components/common/GlobalHUD.vue'

// 国际化
const { t: $t } = useI18n()

// 全局HUD
const hud = useGlobalHud()

// 状态栏高度
const statusBarHeight = ref(0)

// 注册数据
const registerData = ref({})

// 表单数据
const firstName = ref('')
const lastName = ref('')

// 错误信息
const errors = ref({
  firstName: '',
  lastName: ''
})

// 完整姓名
const fullName = computed(() => {
  if (firstName.value && lastName.value) {
    return `${firstName.value} ${lastName.value}`
  }
  return ''
})

// 姓名是否有效
const isNameValid = computed(() => {
  return firstName.value && lastName.value && 
         !errors.value.firstName && !errors.value.lastName
})

// 验证姓氏
const validateFirstName = () => {
  const value = firstName.value.trim()
  
  if (!value) {
    errors.value.firstName = $t('auth.firstNameRequired')
  } else if (!/^[a-zA-ZÀ-ÿ\s]+$/.test(value)) {
    errors.value.firstName = $t('auth.nameOnlyLetters')
  } else if (value.length < 2) {
    errors.value.firstName = $t('auth.nameMinLength')
  } else {
    errors.value.firstName = ''
  }
}

// 验证名字
const validateLastName = () => {
  const value = lastName.value.trim()
  
  if (!value) {
    errors.value.lastName = $t('auth.lastNameRequired')
  } else if (!/^[a-zA-ZÀ-ÿ\s]+$/.test(value)) {
    errors.value.lastName = $t('auth.nameOnlyLetters')
  } else if (value.length < 2) {
    errors.value.lastName = $t('auth.nameMinLength')
  } else {
    errors.value.lastName = ''
  }
}

// 清除错误
const clearError = (field) => {
  errors.value[field] = ''
}

// 继续到密码页面
const continueToPassword = () => {
  if (!isNameValid.value) {
    return
  }

  // 更新注册数据
  const updatedData = {
    ...registerData.value,
    firstName: firstName.value.trim(),
    lastName: lastName.value.trim(),
    nickName: fullName.value,
    step: 3
  }
  
  uni.setStorageSync('registerData', JSON.stringify(updatedData))
  
  // 跳转到密码页面
  uni.navigateTo({
    url: '/pages/auth/register/password'
  })
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 组件挂载
onMounted(() => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })

  // 获取注册数据
  try {
    const data = uni.getStorageSync('registerData')
    if (data) {
      registerData.value = JSON.parse(data)
      
      // 如果已有姓名数据，预填充
      if (registerData.value.firstName) {
        firstName.value = registerData.value.firstName
      }
      if (registerData.value.lastName) {
        lastName.value = registerData.value.lastName
      }
    }
  } catch (error) {
    console.error('获取注册数据失败:', error)
    // 如果没有注册数据，返回上一页
    uni.navigateBack()
  }
})
</script>

<style lang="less">
.name-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 40rpx;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    height: 88rpx;

    .back-icon {
      font-size: 32rpx;
      color: #000;
      font-weight: bold;
      padding: 20rpx;
      margin-left: -20rpx;
    }
  }

  .main-content {
    flex: 1;
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;

    .title-section {
      text-align: center;
      margin: 60rpx 0 80rpx;

      .main-title {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
        line-height: 1.3;
      }

      .subtitle {
        display: block;
        font-size: 32rpx;
        color: #666;
        line-height: 1.4;
      }
    }

    .name-input-section {
      margin-bottom: 40rpx;

      .input-group {
        margin-bottom: 32rpx;

        .input-wrapper {
          position: relative;

          .name-input {
            width: 100%;
            height: 88rpx;
            background: #fff;
            border: 1rpx solid #E5E5E5;
            border-radius: 8rpx;
            padding: 0 32rpx;
            font-size: 28rpx;
            color: #333;
            box-sizing: border-box;

            &::placeholder {
              color: #999;
              font-size: 26rpx;
            }

            &:focus {
              border-color: #f23030;
            }

            &.error {
              border-color: #f23030;
            }
          }

          .success-icon {
            position: absolute;
            right: 32rpx;
            top: 50%;
            transform: translateY(-50%);
            
            .iconfont {
              font-size: 32rpx;
              color: #4CAF50;
            }
          }
        }

        .error-text {
          color: #f23030;
          font-size: 24rpx;
          margin-top: 16rpx;
          margin-left: 16rpx;
        }
      }
    }

    .preview-section {
      background: #F8F9FA;
      padding: 32rpx;
      border-radius: 16rpx;
      margin-bottom: 60rpx;
      text-align: center;

      .preview-label {
        display: block;
        font-size: 28rpx;
        color: #666;
        margin-bottom: 16rpx;
      }

      .preview-name {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .continue-section {
      margin-bottom: 60rpx;

      .continue-btn {
        width: 100%;
        height: 88rpx;
        background: #ccc;
        color: #999;
        border-radius: 56rpx;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;

        &.active {
          background: #f23030;
          color: #fff;
        }

        &.disabled {
          opacity: 0.6;
        }
      }
    }
  }
}
</style>
