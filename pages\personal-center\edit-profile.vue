<template>
    <view class="edit-profile-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ $t('user.profile') }}</text>
                </view>
            </view>
        </view>

        <!-- 个人信息编辑区域 -->
        <view class="profile-card">
            <!-- 头像部分 -->
            <view class="avatar-section" @click="changeAvatar" :class="{ 'uploading': avatarUploading }">
                <view class="avatar-container">
                    <image :src="userInfo.accountPhotoUrl || '/static/images/avatar-red.png'" class="avatar" mode="aspectFill"></image>
                    <view v-if="avatarUploading" class="upload-overlay">
                        <view class="upload-spinner"></view>
                    </view>
                </view>
                <text class="avatar-hint">{{ avatarUploading ? (t('user.uploadingAvatar') || 'Uploading...') : (t('user.clickAvatarToModify') || 'Click to modify') }}</text>
            </view>

            <!-- 账户名称 -->
            <view class="info-item" @click="editAccountName">
                <text class="info-label">{{ $t('user.name') }}</text>
                <view class="info-value-container">
                    <text class="info-value">{{ userInfo.accountName }}</text>
                    <text class="arrow-icon">></text>
                </view>
            </view>

            <!-- 手机号码 -->
            <view class="info-item" @click="editPhoneNumber">
                <text class="info-label">{{ $t('user.phone') }}</text>
                <view class="info-value-container">
                    <text class="info-value">{{ userInfo.accountPhone }}</text>
                    <text class="arrow-icon">></text>
                </view>
            </view>
        </view>

        <!-- 保存按钮 -->
        <view class="save-btn" @click="saveProfile">
            <text>{{ $t('common.save') }}</text>
        </view>

        <!-- 优化后的“编辑账户名称”弹窗 -->
        <CustomDialog
            :visible="nameDialogVisible"
            :title="t('user.editAccountName')"
            :type="'default'"
            :show-cancel="true"
            :confirm-text="t('common.confirm')"
            :cancel-text="t('common.cancel')"
            :loading="nameSubmitting"
            :mask-close="true"
            @confirm="handleNameConfirm"
            @cancel="handleNameCancel"
            @close="nameDialogVisible = false"
            @update:visible="(v)=> nameDialogVisible = v"
        >
            <view class="dialog-input-container">
                <view class="input-field-wrapper">
                    <view class="input-label">
                        <text class="label-text">{{ t('user.accountName') || '账户名称' }}</text>
                        <text class="label-required">*</text>
                    </view>
                    <view class="input-wrapper" :class="{ 'input-error': nameError, 'input-focus': nameInputFocus }">
                        <input
                            class="modern-input"
                            v-model="nameInput"
                            :placeholder="t('user.enterNewAccountName') || '请输入新的账户名称'"
                            maxlength="30"
                            @focus="nameInputFocus = true"
                            @blur="nameInputFocus = false"
                            @input="clearNameError"
                        />
                        <view class="input-counter">
                            <text class="counter-text">{{ nameInput.length }}/30</text>
                        </view>
                    </view>
                    <view v-if="nameError" class="error-message">
                        <text class="error-icon">⚠</text>
                        <text class="error-text">{{ nameError }}</text>
                    </view>
                </view>
            </view>
        </CustomDialog>

        <!-- 照片选择弹窗 -->
        <PhotoActionSheet
            v-model:visible="photoActionVisible"
            :title="t('user.selectPhoto')"
            :take-photo-text="t('user.takePhoto')"
            :choose-from-album-text="t('user.chooseFromAlbum')"
            :cancel-text="t('common.cancel')"
            @take-photo="handleTakePhoto"
            @choose-from-album="handleChooseFromAlbum"
        />

        <!-- 编辑手机号弹窗 -->
        <CustomDialog
            :visible="phoneDialogVisible"
            :title="t('user.editPhoneNumber')"
            :type="'default'"
            :show-cancel="true"
            :confirm-text="t('common.confirm')"
            :cancel-text="t('common.cancel')"
            :loading="phoneSubmitting"
            :mask-close="true"
            @confirm="handlePhoneConfirm"
            @cancel="handlePhoneCancel"
            @close="phoneDialogVisible = false"
            @update:visible="(v)=> phoneDialogVisible = v"
        >
            <view class="dialog-input-container">
                <view class="input-field-wrapper">
                    <view class="input-label">
                        <text class="label-text">{{ t('user.phoneNumber') || '手机号码' }}</text>
                        <text class="label-required">*</text>
                    </view>
                    <view class="input-wrapper" :class="{ 'input-error': phoneError, 'input-focus': phoneInputFocus }">
                        <input
                            class="modern-input"
                            v-model="phoneInput"
                            :placeholder="t('user.enterNewPhoneNumber') || '请输入新的手机号码'"
                            maxlength="20"
                            type="number"
                            @focus="phoneInputFocus = true"
                            @blur="phoneInputFocus = false"
                            @input="clearPhoneError"
                        />
                        <view class="input-counter">
                            <text class="counter-text">{{ phoneInput.length }}/20</text>
                        </view>
                    </view>
                    <view v-if="phoneError" class="error-message">
                        <text class="error-icon">⚠</text>
                        <text class="error-text">{{ phoneError }}</text>
                    </view>
                </view>
            </view>
        </CustomDialog>

        <!-- HUD 局部挂载，确保弹窗可见 -->
        <GlobalHUD />
    </view>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useUserStore } from '@/store/user';
import { useI18n } from '@/composables/useI18n';
import CustomDialog from '@/components/common/CustomDialog.vue'
import GlobalHUD from '@/components/common/GlobalHUD.vue'
import PhotoActionSheet from '@/components/common/PhotoActionSheet.vue'
import { useGlobalHud } from '@/composables/useHud'
import { uploadAvatar, editAccountInfo } from '@/api/modules/user'


// 使用用户状态store
const userStore = useUserStore();
// 使用国际化
const hud = useGlobalHud()
const nameDialogVisible = ref(false)
const nameSubmitting = ref(false)
const nameInput = ref('')
const nameError = ref('')
const nameInputFocus = ref(false)
const avatarUploading = ref(false)
const photoActionVisible = ref(false)
const phoneDialogVisible = ref(false)
const phoneSubmitting = ref(false)
const phoneInput = ref('')
const phoneError = ref('')
const phoneInputFocus = ref(false)

const { t } = useI18n();

function validateName(v){
    const s = (v||'').trim()
    if(!s) return t('error.required')
    if(s.length < 2) return t('error.tooShort')
    if(s.length > 30) return t('error.tooLong')
    // 禁止纯空格/表情
    if(/^[\s]+$/.test(s)) return t('error.invalid')
    return ''
}

function validatePhone(v){
    const s = (v||'').trim()
    if(!s) return t('error.required')
    // 简单的手机号验证（可根据需要调整）
    if(!/^[\d\+\-\s\(\)]+$/.test(s)) return t('error.invalidPhone')
    if(s.length < 8) return t('error.tooShort')
    if(s.length > 20) return t('error.tooLong')
    return ''
}


const statusBarHeight = ref(0)
// 用户信息对象
const userInfo = reactive({
    accountName: '',
    accountPhone: '',
    accountPhotoUrl: ''
})

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight;
    getInfo()
})

//从store里面获取用户信息
const getInfo = () => {
    // 从store获取用户信息
    const storeUserInfo = userStore.getUserInfo || {};

    // 更新用户信息，优先使用nickName字段
    userInfo.accountName = storeUserInfo.nickName || storeUserInfo.accountName || storeUserInfo.username || storeUserInfo.nickname || t('common.notSet');
    userInfo.accountPhone = storeUserInfo.accountPhone || storeUserInfo.phonenumber || t('common.notSet');
    userInfo.accountPhotoUrl = storeUserInfo.accountPhotoUrl || storeUserInfo.avatar || '';
}

const goBack = () => {
    uni.navigateBack()
}

const changeAvatar = () => {
    if (avatarUploading.value) {
        hud.error(t('user.uploadingAvatar') || 'Avatar is uploading, please wait...')
        return
    }

    photoActionVisible.value = true
}

// 处理拍照
const handleTakePhoto = () => {
    uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        sizeType: ['compressed'], // 压缩图片
        success: function (res) {
            uploadAvatarFile(res.tempFilePaths[0])
        }
    })
}

// 处理从相册选择
const handleChooseFromAlbum = () => {
    uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        sizeType: ['compressed'], // 压缩图片
        success: function (res) {
            uploadAvatarFile(res.tempFilePaths[0])
        }
    })
}

// 上传头像文件
const uploadAvatarFile = async (filePath) => {
    try {
        avatarUploading.value = true
        hud.loading(t('user.uploadingAvatar') || 'Uploading avatar...')

        // 直接使用封装的API上传头像（传入文件路径）
        const uploadResult = await uploadAvatar(filePath)

        // 上传成功，更新头像URL
        if (uploadResult.data && uploadResult.data.imgUrl) {
            userInfo.accountPhotoUrl = uploadResult.data.imgUrl
            // 更新store中的用户信息
            userStore.updateUserInfo({
                accountPhotoUrl: uploadResult.data.imgUrl
            })
            hud.success(t('user.avatarUploadSuccess') || 'Avatar uploaded successfully!')
        } else {
            throw new Error('No avatar URL returned')
        }

    } catch (error) {
        console.error('头像上传失败:', error)
        hud.error(error?.response?.data?.message || error?.message || t('user.avatarUploadFailed') || 'Avatar upload failed')
    } finally {
        avatarUploading.value = false
    }
}

const editAccountName = () => {
    nameInput.value = userInfo.accountName || ''
    nameError.value = ''
    nameDialogVisible.value = true
}

async function handleNameConfirm(){
    const err = validateName(nameInput.value)
    if(err){ nameError.value = err; return }
    nameSubmitting.value = true
    try{
        // 调用修改用户信息接口
        await editAccountInfo({
            nickName: nameInput.value.trim(),
            accountPhone: userInfo.accountPhone
        })

        // 更新本地用户信息
        userInfo.accountName = nameInput.value.trim()

        // 更新store中的用户信息
        userStore.updateUserInfo({
            accountName: nameInput.value.trim()
        })

        nameDialogVisible.value = false
        hud.success(t('success.updateSuccess'))
    }catch(e){
        hud.error(e?.response?.data?.message || e?.message || t('error.unknown'))
    }finally{
        nameSubmitting.value = false
    }
}

function handleNameCancel(){
    nameDialogVisible.value = false
}

function clearNameError(){
    if(nameError.value) nameError.value = ''
}

async function handlePhoneConfirm(){
    const err = validatePhone(phoneInput.value)
    if(err){ phoneError.value = err; return }
    phoneSubmitting.value = true
    try{
        // 调用修改用户信息接口
        await editAccountInfo({
            nickName: userInfo.accountName,
            accountPhone: phoneInput.value.trim()
        })

        // 更新本地用户信息
        userInfo.accountPhone = phoneInput.value.trim()

        // 更新store中的用户信息
        userStore.updateUserInfo({
            accountPhone: phoneInput.value.trim()
        })

        phoneDialogVisible.value = false
        hud.success(t('success.updateSuccess'))
    }catch(e){
        hud.error(e?.response?.data?.message || e?.message || t('error.unknown'))
    }finally{
        phoneSubmitting.value = false
    }
}

function handlePhoneCancel(){
    phoneDialogVisible.value = false
}

function clearPhoneError(){
    if(phoneError.value) phoneError.value = ''
}

const editPhoneNumber = () => {
    phoneInput.value = userInfo.accountPhone || ''
    phoneError.value = ''
    phoneDialogVisible.value = true
}

const saveProfile = async () => {
    try {
        hud.loading(t('common.saving') || 'Saving...')

        // 调用修改用户信息接口
        await editAccountInfo({
            nickName: userInfo.accountName,
            accountPhone: userInfo.accountPhone
        })

        // 保存用户信息到store
        userStore.updateUserInfo({
            accountName: userInfo.accountName,
            accountPhone: userInfo.accountPhone,
            accountPhotoUrl: userInfo.accountPhotoUrl
        });

        hud.success(t('success.updateSuccess'))

        // 延迟返回上一页
        setTimeout(() => {
            uni.navigateBack()
        }, 1500)

    } catch (error) {
        console.error('保存用户信息失败:', error)
        hud.error(error?.response?.data?.message || error?.message || t('error.saveFailed') || 'Save failed')
    }
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.edit-profile-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;

    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;

        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;

            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }

        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.profile-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .avatar-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 48rpx 0;
        transition: opacity 0.3s ease;

        &.uploading {
            opacity: 0.8;
            pointer-events: none;
        }

        .avatar-container {
            position: relative;
            width: 160rpx;
            height: 160rpx;
            margin-bottom: 24rpx;

            .avatar {
                width: 100%;
                height: 100%;
                border-radius: 80rpx;
                border: 2rpx solid #f0f0f0;
                background-color: #f8f8f8;
            }

            .upload-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                border-radius: 80rpx;
                display: flex;
                align-items: center;
                justify-content: center;

                .upload-spinner {
                    width: 40rpx;
                    height: 40rpx;
                    border: 4rpx solid rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    border-top-color: #fff;
                    animation: spin 1s linear infinite;
                }
            }
        }

        .avatar-hint {
            font-size: 28rpx;
            color: #999;
            transition: color 0.3s ease;
        }

        &.uploading .avatar-hint {
            color: #666;
        }
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    .divider {
        height: 1rpx;
        background-color: #f0f0f0;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        transition: background-color 0.2s;

        &:active {
            background-color: #f8f8f8;
        }

        &:last-child {
            border-bottom: none;
        }

        .info-label {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
        }

        .info-value-container {
            display: flex;
            align-items: center;
            flex: 1;
            justify-content: flex-end;

            .info-value {
                font-size: 32rpx;
                color: #999;
                margin-right: 16rpx;
                max-width: 400rpx;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }

            .iconfont {
                color: #ccc;
                font-size: 32rpx;
            }
        }
    }
}

.save-btn {
    margin: 64rpx 32rpx;
    background: #FF3B30;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.2);
    transition: all 0.2s;

    text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
    }

    &:active {
        transform: scale(0.98);
        opacity: 0.9;
    }
}

/* 简化的弹窗输入样式 */
.dialog-input-container {
    padding: 20rpx 0;
}

.input-field-wrapper {
    padding: 0 32rpx;
}

.input-label {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;

    .label-text {
        font-size: 26rpx;
        color: #333;
        font-weight: normal;
    }

    .label-required {
        color: #007AFF;
        font-size: 26rpx;
        margin-left: 4rpx;
    }
}

.input-wrapper {
    position: relative;
    background: #fff;
    border-radius: 8rpx;
    border: 1rpx solid #ddd;
    transition: border-color 0.2s ease;

    &.input-focus {
        border-color: #007AFF;
    }

    &.input-error {
        border-color: #FF3B30;
    }
}

.modern-input {
    width: 100%;
    height: 88rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #333;
    background: transparent;
    border: none;
    outline: none;
    box-sizing: border-box;
    padding-right: 80rpx; /* 为计数器留出空间 */

    &::placeholder {
        color: #999;
        font-size: 26rpx;
    }
}

.input-counter {
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);

    .counter-text {
        font-size: 22rpx;
        color: #999;
    }
}

.error-message {
    display: flex;
    align-items: center;
    margin-top: 12rpx;
    padding: 12rpx 16rpx;
    background: rgba(255, 59, 48, 0.08);
    border-radius: 6rpx;

    .error-icon {
        font-size: 24rpx;
        color: #FF3B30;
        margin-right: 8rpx;
    }

    .error-text {
        font-size: 24rpx;
        color: #FF3B30;
        line-height: 1.3;
        flex: 1;
    }
}


</style>