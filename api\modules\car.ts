/**
 * 车辆相关API接口
 */
import Request from '../../utils/request'

// 车辆相关类型定义
export interface CarInfo {
  carId: string
  carNum: string
  carType: string
  carBrand?: string
  carModel?: string
  carColor?: string
  isDefault: boolean
  createTime: string
  updateTime?: string
}

export interface CarListParams {
  pageNum?: number
  pageSize?: number
}

export interface AddCarData {
  carNum: string
  carType: string
  carBrand?: string
  carModel?: string
  carColor?: string
  isDefault?: boolean
}

export interface UpdateCarData {
  carId: string
  carNum: string
  carType: string
  carBrand?: string
  carModel?: string
  carColor?: string
  isDefault?: boolean
}

export interface CarListResponse {
  code: number
  message: string
  data: {
    total: number
    list: CarInfo[]
    pageNum: number
    pageSize: number
  }
}

export interface CarOperationResponse {
  code: number
  message: string
  data: boolean
}

export interface CarDetailResponse {
  code: number
  message: string
  data: CarInfo
}

/**
 * 获取我的车辆列表
 * @param {CarListParams} params - 分页参数
 * @returns {Promise<CarListResponse>} - 返回Promise对象
 */
export function getMyCarList(params: CarListParams = {}): Promise<CarListResponse> {
  const defaultParams: Required<CarListParams> = {
    pageNum: 1,
    pageSize: 20
  }
  
  return Request.get('/app/account/car/page', { 
    params: { ...defaultParams, ...params } 
  })
}

/**
 * 添加车辆
 * @param {AddCarData} data - 车辆信息
 * @returns {Promise<CarOperationResponse>} - 返回Promise对象
 */
export function addCar(data: AddCarData): Promise<CarOperationResponse> {
  return Request.post('/app/account/car/add', data)
}

/**
 * 修改车辆信息
 * @param {UpdateCarData} data - 车辆信息
 * @returns {Promise<CarOperationResponse>} - 返回Promise对象
 */
export function updateCar(data: UpdateCarData): Promise<CarOperationResponse> {
  return Request.put('/app/account/car/update', data)
}

/**
 * 删除车辆
 * @param {string} carId - 车辆ID
 * @returns {Promise<CarOperationResponse>} - 返回Promise对象
 */
export function removeCar(carId: string): Promise<CarOperationResponse> {
  if (!carId) {
    return Promise.reject(new Error('车辆ID不能为空'))
  }
  return Request.delete(`/app/account/car/remove?carId=${carId}`)
}

/**
 * 获取车辆详情
 * @param {string} carId - 车辆ID
 * @returns {Promise<CarDetailResponse>} - 返回Promise对象
 */
export function getCarDetail(carId: string): Promise<CarDetailResponse> {
  if (!carId) {
    return Promise.reject(new Error('车辆ID不能为空'))
  }
  return Request.get(`/app/account/car/detail?carId=${carId}`)
}

/**
 * 设置默认车辆
 * @param {string} carId - 车辆ID
 * @returns {Promise<CarOperationResponse>} - 返回Promise对象
 */
export function setDefaultCar(carId: string): Promise<CarOperationResponse> {
  if (!carId) {
    return Promise.reject(new Error('车辆ID不能为空'))
  }
  return Request.post(`/app/account/car/setDefault?carId=${carId}`)
}
