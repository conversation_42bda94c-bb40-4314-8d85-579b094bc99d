import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getAppVersion, setAppVersion } from '../utils/version.js'

export const useVersionStore = defineStore('version', () => {
  // 当前应用版本号（来源：本地存储优先，其次 manifest）
  const currentVersion = ref(getAppVersion())

  // 初始化：从存储/manifest 读取并同步到状态
  const initVersion = () => {
    try {
      currentVersion.value = getAppVersion()
      console.log('[VersionStore] 初始化版本:', currentVersion.value)
    } catch (e) {
      console.warn('[VersionStore] 初始化版本失败:', e)
    }
  }

  // 更新版本：同时写入存储和内存状态
  const updateVersion = (version) => {
    try {
      setAppVersion(version)
      currentVersion.value = version
      console.log('[VersionStore] 版本已更新为:', version)
    } catch (e) {
      console.error('[VersionStore] 更新版本失败:', e)
    }
  }

  return { currentVersion, initVersion, updateVersion }
})

