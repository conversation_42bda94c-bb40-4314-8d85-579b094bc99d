import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

// 提供定位失败时两个有用操作按钮：重试定位、打开系统设置
export function useLocationActions() {
  const { t } = useI18n()
  const trying = ref(false)

  const retryLocate = async (fn?: () => Promise<any>) => {
    if (trying.value) return
    trying.value = true
    try {
      if (fn) await fn()
      uni.showToast({ title: t('toast.completed') || 'Completed', icon: 'success', duration: 1000 })
    } catch (e) {
      uni.showToast({ title: t('toast.locationFailed') || 'Location failed', icon: 'none' })
    } finally {
      trying.value = false
    }
  }

  const openSettings = () => {
    // H5 没有统一设置入口，提示用户手动开启
    // APP/小程序可使用 openSetting
    // #ifdef MP
    uni.openSetting({})
    // #endif
    // #ifdef APP-PLUS
    try {
      if (typeof plus !== 'undefined' && plus.runtime) {
        plus.runtime.openURL('app-settings://')
      }
    } catch (_) {}
    // #endif
    // H5
    // eslint-disable-next-line no-alert
    // #ifdef H5
    uni.showToast({ title: t('toast.locationFailed') || 'Please enable location in settings', icon: 'none' })
    // #endif
  }

  return { trying, retryLocate, openSettings }
}

