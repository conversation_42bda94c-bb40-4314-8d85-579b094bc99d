<template>
  <view class="scan-button" @click="handleScan">
    <view class="scan-icon">
      <image :src="iconSrc" class="scan-image"></image>
    </view>
    <text class="scan-text">{{ text }}</text>
  </view>
</template>

<script>
export default {
  name: 'ScanButton',
  props: {
    // 扫描按钮文本
    text: {
      type: String,
              default: t('common.scan') || 'Scanner'
    },
    // 扫描图标路径
    iconSrc: {
      type: String,
      default: '/static/images/scan-id.png'
    },
    // 扫描类型
    scanType: {
      type: Array,
      default: () => ['qrCode', 'barCode', 'datamatrix', 'pdf417']
    },
    // 是否只解码一个条码
    onlyFromCamera: {
      type: Boolean,
      default: false
    },
    // 是否显示相册选择按钮
    showAlbum: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleScan() {
      try {
        // 调用扫码API
        uni.scanCode({
          scanType: this.scanType,
          onlyFromCamera: this.onlyFromCamera,
          success: (res) => {
            // 扫描成功，发送结果到父组件
            this.$emit('success', res)
          },
          fail: (err) => {
            // 扫描失败，发送错误到父组件
            this.$emit('fail', err)
            
            // 如果扫码API不可用且允许从相册选择，则使用相册选择
            if (this.showAlbum) {
              this.chooseFromAlbum()
            }
          }
        })
      } catch (error) {
        // 捕获可能的API调用错误
        console.error('Scan function error:', error)
        this.$emit('error', error)
        
        // 如果允许从相册选择，则使用相册选择
        if (this.showAlbum) {
          this.chooseFromAlbum()
        }
      }
    },
    
    chooseFromAlbum() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          // 从相册选择图片成功，发送结果到父组件
          this.$emit('chooseImage', res)
        },
        fail: (err) => {
          // 从相册选择图片失败，发送错误到父组件
          this.$emit('fail', err)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.scan-button {
  background: #e6f7ff;
  border-radius: 50rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .scan-icon {
    margin-right: 16rpx;
    
    .scan-image {
      width: 40rpx;
      height: 40rpx;
    }
  }
  
  .scan-text {
    font-size: 28rpx;
    color: #1890ff;
  }
}
</style> 