<template>
  <view class="charging-pile-3d-container">
    <!-- Loading动画效果 -->
    <view v-if="isLoading" class="loading-overlay">
      <view class="loading-spinner"></view>
    </view>

    <!-- 性能提示 -->
    <view v-if="performanceInfo.showTip" class="performance-tip">
      <view class="tip-content">
        <text class="tip-icon">⚡</text>
        <text class="tip-text">{{ $t('performance.optimizing') || '正在优化性能以提升流畅度' }}</text>
      </view>
    </view>

    <!-- 性能指示器（开发模式下显示） -->
    <view v-if="false" class="performance-indicator" :class="performanceInfo.level">
      <text class="fps-text">{{ performanceInfo.fps }} FPS</text>
      <text class="level-text">{{ performanceInfo.level.toUpperCase() }}</text>
    </view>

    <div id="threejs-container" ref="container" class="w-full h-full" :data-model-type="currentModelType"></div>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch, onMounted, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  modelType: {
    type: String,
    default: 'default',
    validator: (v) => ['default', 'arnio'].includes(v)
  }
});

const emit = defineEmits(['loaded']);

// 使用国际化
const { t } = useI18n();

// 当前模型类型
const currentModelType = ref(props.modelType);

// 容器引用
const container = ref(null);

// Loading状态
const isLoading = ref(true);

// 性能状态
const performanceInfo = ref({
  level: 'high',
  fps: 60,
  showTip: false
});

// 监听props变化
watch(() => props.modelType, (newType) => {
  console.log('🔄 props变化:', newType);
  currentModelType.value = newType;
  showLoading('Loading...');
  // 2秒后自动关闭loading
  setTimeout(() => {
    hideLoading();
  }, 2000);
});

// 暴露方法给父组件调用
const reloadModel = (newType) => {
  console.log('🎯 reloadModel调用:', newType);
  currentModelType.value = newType;
  showLoading('Loading...');
  // 2秒后自动关闭loading
  setTimeout(() => {
    hideLoading();
  }, 2000);
};

// 显示loading
const showLoading = (title = 'Loading...') => {
  console.log('🔄 显示loading动画');
  isLoading.value = true;
};

// 隐藏loading
const hideLoading = () => {
  console.log('🔄 隐藏loading动画');
  isLoading.value = false;
};

// 组件挂载后设置事件监听
onMounted(() => {
  console.log('🚀 ChargingPile3DApp_new 组件挂载');

  // 立即显示loading
  showLoading('Loading 3D Model...');

  nextTick(() => {
    const containerEl = container.value;
    if (containerEl) {
      // 监听renderjs发送的loading状态变化事件
      containerEl.addEventListener('loadingChange', (event) => {
        const { loading, text } = event.detail;
        console.log('📡 Vue收到loading状态变化:', loading, text);

        if (loading) {
          console.log('🔄 Vue显示loading:', text);
          showLoading(text);
        } else {
          console.log('🔄 Vue隐藏loading');
          hideLoading();
          console.log('✅ Vue已调用hideLoading()');
          emit('loaded');
        }
      });

      // 监听性能状态变化事件
      containerEl.addEventListener('performanceChange', (event) => {
        const { level, fps } = event.detail;
        console.log('📊 Vue收到性能状态变化:', level, fps);
        
        performanceInfo.value = {
          level,
          fps: Math.round(fps),
          showTip: level === 'low'
        };
        
        // 低性能时显示提示，3秒后自动隐藏
        if (level === 'low') {
          setTimeout(() => {
            performanceInfo.value.showTip = false;
          }, 3000);
        }
      });

      console.log('✅ Vue组件事件监听器已设置');
    }
  });

  // 3秒后强制关闭loading（防止卡住）
  setTimeout(() => {
    console.log('⏰ 3秒超时，强制关闭loading');
    hideLoading();
  }, 3000);
});

// 暴露给父组件
defineExpose({
  reloadModel,
  showLoading,
  hideLoading
});
</script>

<script module="renderjs" lang="renderjs">
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'

let scene, camera, renderer, controls, model, frameId
let isInit = false
let renderModelType = 'default'  // 重命名避免冲突
let isLowEndDevice = false
let lastFrameTime = 0
const targetFPS = 30  // 目标帧率
let frameCount = 0
let fpsCheckTime = 0
let currentFPS = 60
let performanceLevel = 'high'  // high, medium, low
let renderSkipCounter = 0
let isUserInteracting = false
let interactionTimeout = null
let lastRenderTime = 0
let adaptiveQualityEnabled = true
let isVisible = true
let visibilityObserver = null
let memoryCheckInterval = null
let lastMemoryUsage = 0

// 检测设备性能
function checkDevicePerformance() {
  // 检测设备内存
  const memory = navigator.deviceMemory || 4  // 默认4GB
  
  // 检测CPU核心数
  const cores = navigator.hardwareConcurrency || 4  // 默认4核
  
  // 检测用户代理字符串中的低端设备标识
  const userAgent = navigator.userAgent.toLowerCase()
  const isLowEndUA = userAgent.includes('android') && 
                     (userAgent.includes('go') || userAgent.includes('lite'))
  
  // 检测屏幕分辨率 - 低分辨率通常对应低配设备
  const screenPixels = window.screen.width * window.screen.height
  const isLowResolution = screenPixels < 1920 * 1080  // 小于1080p
  
  // 检测WebGL支持情况
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
  const webglLimited = !gl || !gl.getExtension('OES_texture_float')
  
  // 更宽松的低配设备判断，优先保证流畅度
  const isLowEnd = memory <= 3 || cores <= 4 || isLowEndUA || (isLowResolution && webglLimited)
  
  console.log('🔍 设备性能检测:', {
    memory: memory + 'GB',
    cores: cores + '核',
    screenPixels: screenPixels,
    isLowEndUA,
    isLowResolution,
    webglLimited,
    isLowEnd
  })
  
  return isLowEnd
}

// 获取模型路径列表
function getModelPaths(modelType) {
  console.log('🎯 getModelPaths调用，模型类型:', modelType)
  if (modelType === 'arnio') {
    return [
      '_www/static/arnioNoir.glb',
      '_www/static/models/arnioNoir.glb',
      'static/arnioNoir.glb',
      'static/models/arnioNoir.glb'
    ]
  } else {
    return [
      '_www/static/arnioRougeNew.glb',
      '_www/static/models/arnioRougeNew.glb',
      'static/arnioRougeNew.glb',
      'static/models/arnioRougeNew.glb'
    ]
  }
}

// 加载模型
function loadModel(modelType, showLoading = true) {
  console.log('🚀 开始加载模型:', modelType)

  // 显示loading
  setVueLoading(true, 'Loading 3D Model...')

  const paths = getModelPaths(modelType)
  tryLoadFromPaths(paths, 0, modelType)
}

// 尝试从多个路径加载模型
function tryLoadFromPaths(paths, index, modelType) {
  if (index >= paths.length) {
    console.error('❌ 所有路径都尝试失败，无法加载模型')
    console.log('🔄 所有路径失败，准备隐藏loading...')
    setVueLoading(false)
    console.log('✅ 已调用setVueLoading(false) - 所有路径失败')
    return
  }

  const currentPath = paths[index]
  console.log(`📁 尝试路径 ${index + 1}/${paths.length}:`, currentPath)

  plus.io.resolveLocalFileSystemURL(currentPath, entry => {
    console.log('✅ 文件存在，开始加载:', currentPath)
    const loader = new GLTFLoader()
    const reader = new plus.io.FileReader()

    reader.onloadend = function (e) {
      try {
        const result = e.target.result
        console.log('📄 文件读取成功，数据长度:', result.length)
        const arrayBuffer = base64ToArrayBuffer(result.split(',')[1])
        console.log('🔄 转换为ArrayBuffer，长度:', arrayBuffer.byteLength)
        
        loader.parse(arrayBuffer, '', 
          // 成功回调
          (gltf) => {
            console.log('🎉 GLB解析成功!', gltf)
            console.log('📦 GLTF场景对象:', gltf.scene)
            console.log('🔍 场景子对象数量:', gltf.scene.children.length)
            
            // 移除旧模型并清理内存
            if (model) {
              scene.remove(model)
              disposeModel(model)
            }

            // 设置新模型
            model = gltf.scene
            
            // 检查模型尺寸并调整
            const modelBox = new THREE.Box3().setFromObject(gltf.scene)
            const modelSize = modelBox.getSize(new THREE.Vector3())
            const modelCenter = modelBox.getCenter(new THREE.Vector3())
            console.log('📏 模型原始尺寸:', modelSize)
            console.log('📍 模型中心点:', modelCenter)
            
            // 根据模型尺寸动态调整缩放
            const modelMaxDim = Math.max(modelSize.x, modelSize.y, modelSize.z)
            const targetSize = 2.5 // 目标显示尺寸 - 增大到3.0
            const scale = targetSize / modelMaxDim
            gltf.scene.scale.set(scale, scale, scale)
            console.log('🔧 应用缩放:', scale)
            
            // 调整位置
            gltf.scene.position.set(0, -modelSize.y * scale * 0.5, 0)  // 让模型底部对齐地面
            gltf.scene.rotation.y = 0  // 重置Y轴旋转为0，让模型正面朝向用户
            gltf.scene.rotation.x = 0  // 重置X轴旋转

            // 隐藏底座部分 - 更精确的判断条件
            gltf.scene.traverse((child) => {
              if (child.isMesh) {
                console.log('检查子对象:', child.name, '位置:', child.position)

                // 获取子对象的世界位置
                const worldPos = new THREE.Vector3()
                child.getWorldPosition(worldPos)

                // 通过多种条件判断是否为底座
                const isBase = child.position.y < -1.5 ||  // 位置很低
                              worldPos.y < -1.5 ||         // 世界坐标很低
                              child.name.toLowerCase().includes('base') ||
                              child.name.toLowerCase().includes('底座') ||
                              child.name.toLowerCase().includes('platform') ||
                              child.name.toLowerCase().includes('ground') ||
                              child.name.toLowerCase().includes('floor')

                if (isBase) {
                  child.visible = false  // 隐藏底座部分
                  console.log('✅ 隐藏底座部分:', child.name, '位置:', child.position)
                } else {
                  // 为非底座部分启用阴影 - 低配设备禁用阴影
                  if (!isLowEndDevice) {
                    child.castShadow = true
                    child.receiveShadow = true
                  }
                }
              }
            })

            // 优化材质渲染 - 根据设备性能调整材质质量
            const optimizeMaterialsForText = (object3d) => {
              object3d.traverse((node) => {
                if (!node.isMesh || !node.material) return

                const applyTextOptimization = (mat) => {
                  if (!mat) return

                  // 参考ChargingPile3D.vue的材质优化
                  if (mat.isMeshPhongMaterial || mat.isMeshStandardMaterial) {
                    mat.shininess = isLowEndDevice ? 50 : 100;
                    mat.specular = new THREE.Color(0xffffff);
                  }

                  // 获取材质的基础颜色来判断材质类型
                  const baseColor = mat.color
                  if (!baseColor) return

                  // 检查是否是白色/浅色材质（可能是ARNIO文字）
                  const isLightMaterial = (baseColor.r + baseColor.g + baseColor.b) / 3 > 0.7
                  
                  // 检查是否是红色主体材质
                  const isRedMaterial = baseColor.r > 0.6 && baseColor.g < 0.4 && baseColor.b < 0.4

                  // 检查是否是深色材质（可能是边框或细节）
                  const isDarkMaterial = (baseColor.r + baseColor.g + baseColor.b) / 3 < 0.3

                  if (isLightMaterial) {
                    // 白色/浅色材质 - 清晰无毛刺优化
                    mat.roughness = 0.2       // 适中粗糙度，保持清晰
                    mat.metalness = 0.0       // 非金属
                    mat.emissive = new THREE.Color(0x333333)  // 适度自发光保持清晰

                    // 确保白色材质清晰亮丽
                    if ((baseColor.r + baseColor.g + baseColor.b) / 3 > 0.7) {
                      mat.color.setHex(0xffffff)  // 纯白色
                      mat.emissive = new THREE.Color(0x444444)  // 增强自发光
                    }

                    // 平衡纹理设置，兼顾清晰度和性能
                    if (mat.map) {
                      mat.map.anisotropy = Math.min(4, renderer.capabilities.getMaxAnisotropy())
                      mat.map.minFilter = THREE.LinearFilter
                      mat.map.magFilter = THREE.LinearFilter
                      mat.map.generateMipmaps = false  // 禁用mipmap提升性能
                    }

                    console.log('⚪ 白色材质清晰优化完成')

                  } else if (isRedMaterial) {
                    // 红色主体 - 清晰哑光质感
                    mat.roughness = 0.3       // 适中粗糙度，保持清晰
                    mat.metalness = 0.0       // 非金属
                    mat.color.setHex(0xee2020) // 保持饱和度

                    // 轻微环境反射，增强立体感
                    if (mat.envMapIntensity !== undefined) {
                      mat.envMapIntensity = 0.1
                    }

                    console.log('🔴 红色材质清晰优化完成')

                  } else if (isDarkMaterial) {
                    // 深色材质 - 清晰对比
                    mat.roughness = 0.4       // 适中粗糙度
                    mat.metalness = 0.1       // 轻微金属感增强对比

                    console.log('⚫ 深色材质清晰优化完成')

                  } else {
                    // 其他材质 - 通用清晰优化
                    mat.roughness = 0.3       // 适中粗糙度
                    mat.metalness = 0.05      // 轻微金属感

                    console.log('🔧 通用材质清晰优化完成')
                  }

                  mat.needsUpdate = true
                }

                if (Array.isArray(node.material)) {
                  node.material.forEach(applyTextOptimization)
                } else {
                  applyTextOptimization(node.material)
                }
              })
            }

            // 高质量材质优化，提升清晰度
            optimizeMaterialsForText(gltf.scene)

            // 添加模型标识
            gltf.scene.userData.modelType = modelType
            gltf.scene.userData.loadPath = currentPath

            scene.add(gltf.scene)

            // 调整相机位置 - 让模型显示更大
            const cameraBox = new THREE.Box3().setFromObject(gltf.scene)
            const cameraSize = cameraBox.getSize(new THREE.Vector3())
            const cameraCenter = cameraBox.getCenter(new THREE.Vector3())
            const cameraMaxDim = Math.max(cameraSize.x, cameraSize.y, cameraSize.z)
            const fov = camera.fov * Math.PI / 180
            let z = Math.abs(cameraMaxDim / Math.sin(fov / 2)) * 0.7  // 进一步减小距离系数，让相机更近
            camera.position.set(z * 0.3, z * 0.4, z)
            controls.target.set(cameraCenter.x, cameraCenter.y + cameraSize.y * 0.1, cameraCenter.z)
            controls.update()

            // 强制渲染
            renderer.render(scene, camera)

            console.log('✅ 模型加载成功:', modelType)
            renderModelType = modelType

            // 模型加载完成，立即隐藏loading
            console.log('🔄 准备隐藏loading...')
            setVueLoading(false)
            console.log('✅ 已调用setVueLoading(false)')
          },
          // 错误回调
          (error) => {
            console.error('❌ GLB解析错误:', error)
            tryLoadFromPaths(paths, index + 1, modelType)
          }
        )
      } catch (err) {
        console.error('❌ 文件读取错误:', err)
        tryLoadFromPaths(paths, index + 1, modelType)
      }
    }

    reader.readAsDataURL(entry)
  }, e => {
    console.log(`❌ 路径失败，尝试下一个:`, e)
    tryLoadFromPaths(paths, index + 1, modelType)
  })
}

// Base64 转 ArrayBuffer
function base64ToArrayBuffer(base64) {
  const binaryString = atob(base64)
  const len = binaryString.length
  const bytes = new Uint8Array(len)
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes.buffer
}

// 设置Vue组件的loading状态
function setVueLoading(loading, text = 'Loading...') {
  console.log('🔄 renderjs调用setVueLoading:', loading, text)
  try {
    const container = document.querySelector('#threejs-container')
    if (container) {
      console.log('📡 renderjs发送loadingChange事件:', { loading, text })
      // 通过DOM事件通知Vue组件
      container.dispatchEvent(new CustomEvent('loadingChange', {
        detail: { loading, text }
      }))
      console.log('✅ renderjs已发送loadingChange事件')
    } else {
      console.log('❌ 未找到container元素')
    }
  } catch (error) {
    console.log('❌ 设置loading状态失败:', error)
  }
}

// 动画循环 - 深度性能优化
function animate(currentTime) {
  frameId = requestAnimationFrame(animate)

  // 性能监控和自适应调整
  monitorPerformance(currentTime)

  // 智能帧率控制
  const shouldRender = shouldRenderFrame(currentTime)
  if (!shouldRender) {
    return  // 跳过这一帧
  }

  // 确保控制器更新
  if (controls) {
    controls.update()
  }

  // 渲染场景
  if (renderer && scene && camera) {
    renderer.render(scene, camera)
    lastRenderTime = currentTime
  }
}

// 智能渲染判断
function shouldRenderFrame(currentTime) {
  // 暂时禁用可见性检测，确保模型能正常显示
  // if (!isVisible && isInit) {
  //   return false
  // }
  
  // 用户交互时保持高帧率
  if (isUserInteracting) {
    return true
  }
  
  // 根据性能等级和用户状态调整渲染频率
  const deltaTime = currentTime - lastFrameTime
  let targetInterval
  
  switch (performanceLevel) {
    case 'low':
      // 低性能：静止时20fps，交互时30fps
      targetInterval = isUserInteracting ? 1000 / 30 : 1000 / 20
      break
    case 'medium':
      // 中性能：静止时25fps，交互时40fps
      targetInterval = isUserInteracting ? 1000 / 40 : 1000 / 25
      break
    case 'high':
      // 高性能：静止时30fps，交互时50fps
      targetInterval = isUserInteracting ? 1000 / 50 : 1000 / 30
      break
    default:
      targetInterval = 1000 / 30
  }
  
  if (deltaTime < targetInterval) {
    return false
  }
  
  lastFrameTime = currentTime
  return true
}

// 性能监控函数 - 优先流畅度
function monitorPerformance(currentTime) {
  frameCount++
  
  if (fpsCheckTime === 0) {
    fpsCheckTime = currentTime
  }
  
  // 每3秒检查一次性能，更快响应性能变化
  if (currentTime - fpsCheckTime >= 3000) {
    currentFPS = (frameCount * 1000) / (currentTime - fpsCheckTime)
    
    // 更积极的性能调整策略，优先保证流畅度
    const oldLevel = performanceLevel
    if (currentFPS < 20) {
      performanceLevel = 'low'
    } else if (currentFPS < 35) {
      performanceLevel = 'medium'
    } else {
      performanceLevel = 'high'
    }
    
    if (oldLevel !== performanceLevel) {
      console.log(`📊 性能等级调整: ${oldLevel} -> ${performanceLevel} (FPS: ${currentFPS.toFixed(1)})`)
      adaptRenderQuality()
      
      // 通知Vue组件性能状态变化
      notifyPerformanceChange(performanceLevel, currentFPS)
    }
    
    // 重置计数器
    frameCount = 0
    fpsCheckTime = currentTime
  }
}

// 极限省电模式
function enableUltraLowPowerMode() {
  if (!adaptiveQualityEnabled) return
  
  console.log('🔋 启用极限省电模式')
  
  // 进一步降低渲染质量
  if (renderer) {
    renderer.setPixelRatio(0.75)  // 极低像素比
    renderer.shadowMap.enabled = false
    
    // 禁用一些渲染特性
    renderer.localClippingEnabled = false
    renderer.sortObjects = false
  }
  
  // 简化场景
  if (scene && model) {
    model.traverse(child => {
      if (child.isMesh && child.material) {
        // 使用最简单的材质
        if (Array.isArray(child.material)) {
          child.material.forEach(mat => {
            mat.roughness = 0.8
            mat.metalness = 0
            if (mat.normalMap) mat.normalMap = null
            if (mat.roughnessMap) mat.roughnessMap = null
            if (mat.metalnessMap) mat.metalnessMap = null
          })
        } else {
          child.material.roughness = 0.8
          child.material.metalness = 0
          if (child.material.normalMap) child.material.normalMap = null
          if (child.material.roughnessMap) child.material.roughnessMap = null
          if (child.material.metalnessMap) child.material.metalnessMap = null
        }
      }
    })
  }
  
  adaptiveQualityEnabled = false  // 防止重复执行
}

// 根据性能等级调整渲染质量
function adaptRenderQuality() {
  if (!renderer) return
  
  switch (performanceLevel) {
    case 'low':
      // 低质量 - 优先流畅度
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5))
      renderer.shadowMap.enabled = false  // 禁用阴影提升性能
      console.log('🔧 切换到低质量模式（优先流畅）')
      break
      
    case 'medium':
      // 中等质量设置
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.8))
      renderer.shadowMap.enabled = true
      console.log('🔧 切换到中等质量模式')
      break
      
    case 'high':
      // 高质量设置 - 平衡清晰度和性能
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2.0))
      renderer.shadowMap.enabled = true
      console.log('🔧 切换到高质量模式（平衡性能）')
      break
  }
}

// 内存清理函数
function disposeModel(model) {
  if (!model) return
  
  model.traverse(object => {
    // 清理几何体
    if (object.geometry) {
      object.geometry.dispose()
    }
    
    // 清理材质
    if (object.material) {
      if (Array.isArray(object.material)) {
        object.material.forEach(material => disposeMaterial(material))
      } else {
        disposeMaterial(object.material)
      }
    }
  })
  
  console.log('🗑️ 模型内存已清理')
}

// 材质清理函数
function disposeMaterial(material) {
  if (!material) return
  
  // 清理纹理
  if (material.map) material.map.dispose()
  if (material.normalMap) material.normalMap.dispose()
  if (material.roughnessMap) material.roughnessMap.dispose()
  if (material.metalnessMap) material.metalnessMap.dispose()
  if (material.emissiveMap) material.emissiveMap.dispose()
  if (material.aoMap) material.aoMap.dispose()
  if (material.envMap) material.envMap.dispose()
  
  // 清理材质本身
  material.dispose()
}

// 模型简化函数 - 针对低配设备
function simplifyModelForLowEnd(gltfScene) {
  if (!isLowEndDevice && performanceLevel !== 'low') {
    console.log('🔧 跳过模型简化 - 设备性能足够')
    return
  }
  
  let removedObjects = 0
  const objectsToRemove = []
  
  gltfScene.traverse(child => {
    if (child.isMesh) {
      const geometry = child.geometry
      
      // 移除过于复杂的几何体（顶点数超过阈值）
      if (geometry && geometry.attributes.position) {
        const vertexCount = geometry.attributes.position.count
        console.log(`🔍 检查几何体: ${child.name || 'unnamed'}, 顶点数: ${vertexCount}`)
        
        // 提高阈值，避免移除重要的模型部分
        // 低配设备移除超过15000个顶点的复杂几何体
        if (performanceLevel === 'low' && vertexCount > 15000) {
          objectsToRemove.push(child)
          removedObjects++
          console.log(`❌ 移除复杂几何体: ${child.name || 'unnamed'} (${vertexCount} 顶点)`)
        }
        // 中等性能移除超过25000个顶点的几何体
        else if (performanceLevel === 'medium' && vertexCount > 25000) {
          objectsToRemove.push(child)
          removedObjects++
          console.log(`❌ 移除复杂几何体: ${child.name || 'unnamed'} (${vertexCount} 顶点)`)
        }
      }
      
      // 简化材质
      if (child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material]
        materials.forEach(mat => {
          // 禁用复杂的材质特性
          if (performanceLevel === 'low') {
            mat.roughness = 0.5  // 固定粗糙度
            mat.metalness = 0    // 禁用金属效果
            if (mat.normalMap) {
              mat.normalMap = null  // 移除法线贴图
            }
          }
        })
      }
    }
  })
  
  // 移除复杂对象
  objectsToRemove.forEach(obj => {
    if (obj.parent) {
      obj.parent.remove(obj)
    }
  })
  
  if (removedObjects > 0) {
    console.log(`🔧 性能优化: 移除了 ${removedObjects} 个复杂对象`)
  } else {
    console.log('🔧 性能优化: 没有需要移除的复杂对象')
  }
}

// 通知Vue组件性能状态变化
function notifyPerformanceChange(level, fps) {
  try {
    const container = document.querySelector('#threejs-container')
    if (container) {
      container.dispatchEvent(new CustomEvent('performanceChange', {
        detail: { level, fps }
      }))
    }
  } catch (error) {
    console.log('❌ 通知性能状态变化失败:', error)
  }
}

// 设置交互监听器
function setupInteractionListeners() {
  const domElement = renderer.domElement
  
  // 触摸和鼠标事件
  const startEvents = ['touchstart', 'mousedown']
  const endEvents = ['touchend', 'mouseup', 'touchcancel']
  
  startEvents.forEach(event => {
    domElement.addEventListener(event, () => {
      isUserInteracting = true
      clearTimeout(interactionTimeout)
      console.log('🖱️ 用户开始交互')
    }, { passive: true })
  })
  
  endEvents.forEach(event => {
    domElement.addEventListener(event, () => {
      // 延迟500ms后认为交互结束
      clearTimeout(interactionTimeout)
      interactionTimeout = setTimeout(() => {
        isUserInteracting = false
        console.log('🖱️ 用户交互结束')
      }, 500)
    }, { passive: true })
  })
  
  console.log('✅ 交互监听器已设置')
}

// 几何体优化函数
function optimizeGeometry(geometry) {
  if (!geometry || !geometry.attributes) return geometry
  
  // 移除不必要的属性以节省内存
  if (geometry.attributes.uv2) {
    geometry.deleteAttribute('uv2')
  }
  if (geometry.attributes.tangent) {
    geometry.deleteAttribute('tangent')
  }
  
  // 合并顶点（如果顶点数量过多）
  if (geometry.attributes.position && geometry.attributes.position.count > 10000) {
    geometry.mergeVertices()
    console.log('🔧 几何体顶点合并完成')
  }
  
  return geometry
}

// 纹理优化函数
function optimizeTexture(texture) {
  if (!texture) return texture
  
  // 根据设备性能调整纹理质量
  if (isLowEndDevice || performanceLevel === 'low') {
    // 降低纹理分辨率
    texture.minFilter = THREE.LinearFilter
    texture.magFilter = THREE.LinearFilter
    texture.generateMipmaps = false
    texture.anisotropy = 1
  } else {
    texture.anisotropy = Math.min(4, renderer.capabilities.getMaxAnisotropy())
  }
  
  return texture
}

// 深度几何体优化
function optimizeModelGeometry(gltfScene) {
  let optimizedCount = 0
  
  gltfScene.traverse(child => {
    if (child.isMesh && child.geometry) {
      // 优化几何体
      child.geometry = optimizeGeometry(child.geometry)
      
      // 优化材质中的纹理
      if (child.material) {
        const materials = Array.isArray(child.material) ? child.material : [child.material]
        materials.forEach(mat => {
          if (mat.map) mat.map = optimizeTexture(mat.map)
          if (mat.normalMap) mat.normalMap = optimizeTexture(mat.normalMap)
          if (mat.roughnessMap) mat.roughnessMap = optimizeTexture(mat.roughnessMap)
          if (mat.metalnessMap) mat.metalnessMap = optimizeTexture(mat.metalnessMap)
        })
      }
      
      optimizedCount++
    }
  })
  
  if (optimizedCount > 0) {
    console.log(`🔧 深度几何体优化完成: ${optimizedCount} 个对象`)
  }
}

// 设置可见性监听器
function setupVisibilityObserver() {
  const container = document.querySelector('#threejs-container')
  if (!container) return
  
  // 初始化时假设组件是可见的
  isVisible = true
  
  // 使用Intersection Observer监听组件可见性
  visibilityObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      const wasVisible = isVisible
      isVisible = entry.isIntersecting && entry.intersectionRatio > 0.1
      
      if (wasVisible !== isVisible) {
        console.log('👁️ 3D组件可见性变化:', isVisible ? '可见' : '不可见')
        
        if (!isVisible) {
          // 不可见时降低渲染频率
          console.log('⏸️ 组件不可见，暂停高频渲染')
        } else {
          // 重新可见时恢复渲染
          console.log('▶️ 组件重新可见，恢复渲染')
        }
      }
    })
  }, {
    threshold: [0, 0.1, 0.5, 1.0]  // 多个阈值，更精确的可见性检测
  })
  
  visibilityObserver.observe(container)
  console.log('✅ 可见性监听器已设置')
}

// 页面可见性监听
function setupPageVisibilityListener() {
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      isVisible = false
      console.log('📱 页面切换到后台，暂停3D渲染')
      // 后台时强制垃圾回收
      forceGarbageCollection()
    } else {
      isVisible = true
      console.log('📱 页面切换到前台，恢复3D渲染')
    }
  })
}

// 内存监控
function startMemoryMonitoring() {
  // 每10秒检查一次内存使用情况
  memoryCheckInterval = setInterval(() => {
    checkMemoryUsage()
  }, 10000)
  
  console.log('🧠 内存监控已启动')
}

// 检查内存使用情况
function checkMemoryUsage() {
  if (!performance.memory) return
  
  const memInfo = performance.memory
  const currentUsage = memInfo.usedJSHeapSize / 1024 / 1024  // MB
  const totalHeap = memInfo.totalJSHeapSize / 1024 / 1024   // MB
  const limit = memInfo.jsHeapSizeLimit / 1024 / 1024       // MB
  
  const usagePercent = (currentUsage / limit) * 100
  
  console.log(`🧠 内存使用: ${currentUsage.toFixed(1)}MB / ${limit.toFixed(1)}MB (${usagePercent.toFixed(1)}%)`)
  
  // 内存使用超过70%时触发优化
  if (usagePercent > 70) {
    console.log('⚠️ 内存使用过高，触发优化')
    handleHighMemoryUsage()
  }
  
  // 内存使用超过85%时强制垃圾回收
  if (usagePercent > 85) {
    console.log('🚨 内存使用严重过高，强制垃圾回收')
    forceGarbageCollection()
  }
  
  lastMemoryUsage = currentUsage
}

// 处理高内存使用
function handleHighMemoryUsage() {
  // 降低性能等级
  if (performanceLevel !== 'low') {
    performanceLevel = 'low'
    adaptRenderQuality()
    console.log('📉 因内存压力降低性能等级到 low')
  }
  
  // 清理不必要的缓存
  if (renderer) {
    renderer.info.memory.geometries = 0
    renderer.info.memory.textures = 0
  }
  
  // 强制垃圾回收
  forceGarbageCollection()
}

// 强制垃圾回收
function forceGarbageCollection() {
  // 清理Three.js缓存
  if (THREE.Cache) {
    THREE.Cache.clear()
  }
  
  // 清理渲染器信息
  if (renderer) {
    renderer.renderLists.dispose()
    if (renderer.info) {
      renderer.info.memory.geometries = 0
      renderer.info.memory.textures = 0
    }
  }
  
  // 尝试触发浏览器垃圾回收（仅在支持的浏览器中）
  if (window.gc) {
    window.gc()
    console.log('🗑️ 强制垃圾回收完成')
  } else {
    console.log('🗑️ 浏览器不支持手动垃圾回收')
  }
}

// 清理资源
function cleanup() {
  // 停止内存监控
  if (memoryCheckInterval) {
    clearInterval(memoryCheckInterval)
    memoryCheckInterval = null
  }
  
  // 停止动画循环
  if (frameId) {
    cancelAnimationFrame(frameId)
    frameId = null
  }
  
  // 清理可见性观察器
  if (visibilityObserver) {
    visibilityObserver.disconnect()
    visibilityObserver = null
  }
  
  // 清理3D资源
  if (model) {
    disposeModel(model)
    model = null
  }
  
  if (renderer) {
    renderer.dispose()
    renderer = null
  }
  
  console.log('🧹 资源清理完成')
}

// DOM属性监听器
function watchModelTypeAttribute() {
  const container = document.querySelector('#threejs-container')
  if (!container) return

  // 使用MutationObserver监听属性变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'data-model-type') {
        const newType = container.getAttribute('data-model-type')
        console.log('🔄 检测到data-model-type变化:', renderModelType, '->', newType)
        
        if (newType && newType !== renderModelType) {
          console.log('✅ 开始切换模型:', newType)
          loadModel(newType)
        }
      }
    })
  })

  observer.observe(container, {
    attributes: true,
    attributeFilter: ['data-model-type']
  })

  console.log('✅ DOM属性监听器已设置')
}

export default {
  mounted() {
    if (isInit) return
    isInit = true

    const container = document.querySelector('#threejs-container')
    const initialModelType = container.getAttribute('data-model-type') || 'default'
    console.log('🚀 renderjs初始化，初始模型类型:', initialModelType)

    // 初始化Three.js场景
    scene = new THREE.Scene()
    camera = new THREE.PerspectiveCamera(40, container.clientWidth / container.clientHeight, 0.1, 2000)

    // 设置相机初始位置
    camera.position.set(3, 4, 10)

    // 优化渲染器配置，提高清晰度
    isLowEndDevice = checkDevicePerformance()
    
    renderer = new THREE.WebGLRenderer({
      antialias: true,                      // 强制启用抗锯齿消除毛刺
      alpha: true,
      powerPreference: 'high-performance',  // 强制高性能模式
      precision: 'highp',                   // 强制高精度
      preserveDrawingBuffer: false,
      stencil: false,
      depth: true
    })
    renderer.setSize(container.clientWidth, container.clientHeight)
    // 平衡像素比，兼顾清晰度和流畅度
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2.0))
    
    // 启用高质量阴影
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
    
    // 优化渲染设置 - 提高清晰度
    renderer.outputColorSpace = THREE.SRGBColorSpace
    renderer.toneMapping = THREE.ACESFilmicToneMapping  // 使用电影级色调映射
    renderer.toneMappingExposure = 1.0  // 标准曝光
    
    container.appendChild(renderer.domElement)

    // 清晰均匀光照设置，消除毛刺
    const amb = new THREE.AmbientLight(0xffffff, 0.7)  // 适中环境光
    scene.add(amb)
    
    // 主光源 - 平衡清晰度和性能
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0)
    directionalLight.position.set(5, 8, 5)
    directionalLight.castShadow = true
    directionalLight.shadow.mapSize.width = 1024  // 降低阴影分辨率提升性能
    directionalLight.shadow.mapSize.height = 1024
    directionalLight.shadow.camera.near = 0.1
    directionalLight.shadow.camera.far = 50
    directionalLight.shadow.camera.left = -8
    directionalLight.shadow.camera.right = 8
    directionalLight.shadow.camera.top = 8
    directionalLight.shadow.camera.bottom = -8
    scene.add(directionalLight)

    // 补光 - 适中强度
    const fillLight = new THREE.DirectionalLight(0xffffff, 0.6)
    fillLight.position.set(-3, 3, 3)
    scene.add(fillLight)
    
    console.log('💡 清晰光照设置完成 - 消除毛刺')

    // 设置控制器 - 优化清晰度和响应性
    controls = new OrbitControls(camera, renderer.domElement)
    controls.enableDamping = true
    controls.dampingFactor = 0.05  // 更低阻尼，更清晰的运动
    controls.rotateSpeed = 0.7     // 适中旋转速度
    controls.enableZoom = false
    controls.enablePan = false
    controls.autoRotate = false
    controls.autoRotateSpeed = 0.4
    
    // 限制垂直旋转角度
    controls.minPolarAngle = Math.PI / 3.5
    controls.maxPolarAngle = Math.PI / 1.7
    
    // 设置初始旋转位置
    controls.target.set(0, 0.5, 0)
    camera.lookAt(controls.target)
    controls.update()
    
    // 添加交互事件监听
    setupInteractionListeners()

    // 设置DOM属性监听器
    watchModelTypeAttribute()
    
    // 暂时禁用可见性监听器和内存监控，确保模型正常显示
    // setupVisibilityObserver()
    // setupPageVisibilityListener()
    // startMemoryMonitoring()

    // 加载初始模型
    loadModel(initialModelType, true)

    // 开始动画循环
    animate()

    console.log('✅ renderjs初始化完成')
  }
}
</script>

<style scoped>
.charging-pile-3d-container {
  width: 100%;
  height: 100%;
  min-height: 1000rpx;
  overflow: hidden;
  position: relative;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* Loading动画样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff0000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 性能提示样式 */
.performance-tip {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  padding: 8px 16px;
  animation: slideDown 0.3s ease-out;
}

.tip-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tip-icon {
  font-size: 16px;
}

.tip-text {
  color: #ffffff;
  font-size: 12px;
  white-space: nowrap;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 性能指示器样式（开发用） */
.performance-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1002;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 4px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
}

.performance-indicator.high {
  border-left: 3px solid #00ff00;
}

.performance-indicator.medium {
  border-left: 3px solid #ffaa00;
}

.performance-indicator.low {
  border-left: 3px solid #ff0000;
}

.fps-text {
  color: #ffffff;
  font-size: 10px;
  font-weight: bold;
}

.level-text {
  color: #cccccc;
  font-size: 8px;
}
</style>
