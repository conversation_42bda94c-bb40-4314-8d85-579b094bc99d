import Request from '../../utils/request'

// 声明uni-app全局对象
declare const uni: {
  getStorageSync: (key: string) => any;
  setStorageSync: (key: string, value: any) => void;
  [key: string]: any;
};

export interface VersionResponse {
  code: number
  msg: string
  data: {
    versionId: string
    versionName: string
    versionNum: string
    versionType: string
    versionDescription: string
    programPackage: string
    programPackageUrl: string
    environmentType: string
    isNewest: boolean
    remark: string | null
    releaseDate: string | null
    isMandatoryUpdate: boolean
    status: string
  }
}

export interface VersionCheckResult {
  hasNewVersion: boolean
  shouldShowDialog: boolean
  versionInfo: VersionResponse['data'] | null
}

export const checkVersion = (): Promise<VersionResponse> => {
  // Version参数已在request拦截器中自动添加
  return Request.get('/system/version/detectionVersion')
}

/**
 * 获取本地存储的版本号
 */
export const getLocalVersion = (): string => {
  try {
    return uni.getStorageSync('app_version') || '1.0'
  } catch (error) {
    console.error('获取本地版本号失败:', error)
    return '1.0'
  }
}

/**
 * 保存版本号到本地存储
 */
export const saveLocalVersion = (version: string): void => {
  try {
    uni.setStorageSync('app_version', version)
    console.log('版本号已保存到本地:', version)
  } catch (error) {
    console.error('保存版本号失败:', error)
  }
}

/**
 * 检查版本更新并处理弹窗逻辑
 */
export const checkVersionUpdate = async (): Promise<VersionCheckResult> => {
  try {
    // 获取本地版本号
    const localVersion = getLocalVersion()
    console.log('本地版本号:', localVersion)

    // 调用版本检查接口（Version参数已在request拦截器中自动添加）
    const response = await checkVersion()

    if (response.code === 200 && response.data) {
      const { versionNum, status } = response.data
      console.log('服务器版本号:', versionNum, '状态:', status)

      // status为"0"表示有新版本，"1"表示没有新版本
      const hasNewVersion = status === '0'

      // 注意：不在这里保存版本号，应该在用户下载完成后保存
      console.log('版本检查结果:', hasNewVersion ? '有新版本' : '已是最新版本')

      return {
        hasNewVersion,
        shouldShowDialog: hasNewVersion,
        versionInfo: response.data
      }
    } else {
      console.error('版本检查接口返回错误:', response.msg)
      return {
        hasNewVersion: false,
        shouldShowDialog: false,
        versionInfo: null
      }
    }
  } catch (error) {
    console.error('版本检查失败:', error)
    return {
      hasNewVersion: false,
      shouldShowDialog: false,
      versionInfo: null
    }
  }
}