<template>
    <view class="login-password-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">Login Password</text>
                </view>
            </view>
        </view>
        
        <!-- 页面描述 -->
        <view class="page-description">
            <text class="description-text">Set a secure password to protect your account</text>
        </view>
        
        <!-- 密码修改区域 -->
        <view class="password-card">
            <!-- 旧密码 -->
            <view class="password-section">
                <text class="section-title">Enter current password</text>
                <view class="password-input-wrapper">
                    <input 
                        class="password-input" 
                        :type="showOldPassword ? 'text' : 'password'" 
                        v-model="oldPassword" 
                        placeholder="Enter current password"
                        maxlength="4"
                        @input="validateOldPassword"
                    />
                    <view class="eye-icon" @click="toggleOldPassword">
                        <text class="iconfont" :class="showOldPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"></text>
                    </view>
                </view>
                <text v-if="oldPasswordError" class="error-text">{{ oldPasswordError }}</text>
            </view>
            
            <!-- 新密码 -->
            <view class="password-section">
                <text class="section-title">Enter a new password</text>
                <view class="password-input-wrapper">
                    <input 
                        class="password-input" 
                        :type="showNewPassword ? 'text' : 'password'" 
                        v-model="newPassword" 
                        placeholder="Enter 4-digit password"
                        maxlength="4"
                        @input="validateNewPassword"
                    />
                    <view class="eye-icon" @click="toggleNewPassword">
                        <text class="iconfont" :class="showNewPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"></text>
                    </view>
                </view>
                <text v-if="newPasswordError" class="error-text">{{ newPasswordError }}</text>
            </view>
            
            <!-- 确认新密码 -->
            <view class="password-section">
                <text class="section-title">Re-enter the new password</text>
                <view class="password-input-wrapper">
                    <input 
                        class="password-input" 
                        :type="showConfirmPassword ? 'text' : 'password'" 
                        v-model="confirmPassword" 
                        placeholder="Enter 4-digit password"
                        maxlength="4"
                        @input="validateConfirmPassword"
                    />
                    <view class="eye-icon" @click="toggleConfirmPassword">
                        <text class="iconfont" :class="showConfirmPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"></text>
                    </view>
                </view>
                <text v-if="confirmPasswordError" class="error-text">{{ confirmPasswordError }}</text>
            </view>
        </view>
        
        <!-- 安全提示 -->
        <view class="security-tips">
            <text class="tips-title">Security Tips</text>
            <text class="tip-item">• Use 4 digits for your password</text>
            <text class="tip-item">• Avoid using simple sequences like 1234</text>
            <text class="tip-item">• Don't reuse passwords from other accounts</text>
        </view>
        
        <!-- 保存按钮 -->
        <view class="save-btn" :class="{ disabled: !isFormValid }" @click="savePassword">
            <text>Save</text>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { resetPassword } from '@/api'
import { useUserStore } from '@/store/user'

const statusBarHeight = ref(0)
const oldPassword = ref('')
const newPassword = ref('')
const confirmPassword = ref('')

// 获取用户store
const userStore = useUserStore()
const showOldPassword = ref(false)
const showNewPassword = ref(false)
const showConfirmPassword = ref(false)
const oldPasswordError = ref('')
const newPasswordError = ref('')
const confirmPasswordError = ref('')
const userPhoneNumber = ref('') // 用户手机号

const isFormValid = computed(() => {
    return /^\d{4}$/.test(oldPassword.value) &&
           /^\d{4}$/.test(newPassword.value) && 
           confirmPassword.value === newPassword.value &&
           !oldPasswordError.value &&
           !newPasswordError.value &&
           !confirmPasswordError.value
})

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
    
    // 从Pinia store获取用户信息
    const userInfo = userStore.getUserInfo
    if (userInfo) {
        // 优先从 user_info 中获取，然后是根级别
        const userInfoData = userInfo.user_info || userInfo
        userPhoneNumber.value = userInfoData.accountPhone || userInfoData.phonenumber || userInfoData.phone || ''
        console.log('从Pinia获取到用户手机号:', userPhoneNumber.value)
    } else {
        // 如果Pinia中没有，尝试从本地存储获取
        try {
            const userInfoStr = uni.getStorageSync('userInfo')
            if (userInfoStr) {
                const userInfo = JSON.parse(userInfoStr)
                const userInfoData = userInfo.user_info || userInfo
                userPhoneNumber.value = userInfoData.accountPhone || userInfoData.phonenumber || userInfoData.phone || ''
                console.log('从本地存储获取到用户手机号:', userPhoneNumber.value)
            }
        } catch (error) {
            console.error('获取用户信息失败:', error)
        }
    }
})

const goBack = () => {
    uni.navigateBack()
}

const toggleOldPassword = () => {
    showOldPassword.value = !showOldPassword.value
}

const toggleNewPassword = () => {
    showNewPassword.value = !showNewPassword.value
}

const toggleConfirmPassword = () => {
    showConfirmPassword.value = !showConfirmPassword.value
}

const validateOldPassword = () => {
    const password = oldPassword.value
    if (!password) {
        oldPasswordError.value = ''
        return
    }
    
    if (!/^\d{4}$/.test(password)) {
        oldPasswordError.value = 'Password must be exactly 4 digits'
    } else {
        oldPasswordError.value = ''
    }
    
    // 重新验证确认密码
    if (confirmPassword.value) {
        validateConfirmPassword()
    }
}

const validateNewPassword = () => {
    const password = newPassword.value
    if (!password) {
        newPasswordError.value = ''
        return
    }
    
    if (!/^\d{4}$/.test(password)) {
        newPasswordError.value = 'Password must be exactly 4 digits'
    } else {
        newPasswordError.value = ''
    }
    
    // 重新验证确认密码
    if (confirmPassword.value) {
        validateConfirmPassword()
    }
}

const validateConfirmPassword = () => {
    const confirm = confirmPassword.value
    if (!confirm) {
        confirmPasswordError.value = ''
        return
    }
    
    if (confirm !== newPassword.value) {
        confirmPasswordError.value = 'Passwords do not match'
    } else {
        confirmPasswordError.value = ''
    }
}

const savePassword = async () => {
    if (!isFormValid.value) {
        return
    }
    
    try {
        uni.showLoading({
            title: 'Updating password...',
            mask: true
        })

        // 调用修改密码API
        const response = await resetPassword({
            accountPassword: newPassword.value,
            oldAccountPassword: oldPassword.value, // 个人中心修改密码时需要旧密码
            accountPhone: userPhoneNumber.value, // 个人中心修改密码时也需要手机号
            operateType:'2'
        })

        uni.hideLoading()

        if (response && (response.code === 200 || response.code === "200")) {
            uni.showToast({
                title: 'Password updated successfully',
                icon: 'success',
                duration: 2000
            })
            
            setTimeout(() => {
                uni.navigateBack()
            }, 2000)
        } else {
            throw new Error(response?.message || 'Failed to update password')
        }

    } catch (error) {
        uni.hideLoading()
        console.error('修改密码失败:', error)
        
        let errorMessage = 'Failed to update password'
        if (error.response) {
            errorMessage = error.response.data?.message || errorMessage
        } else if (error.message) {
            errorMessage = error.message
        }

        uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
        })
    }
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.login-password-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.page-description {
    padding: 32rpx;
    text-align: center;
    
    .description-text {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
    }
}

.password-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .password-section {
        margin-bottom: 40rpx;
        
        &:last-child {
            margin-bottom: 0;
        }
        
        .section-title {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
            margin-bottom: 24rpx;
            display: block;
        }
        
        .password-input-wrapper {
            display: flex;
            align-items: center;
            border: 1rpx solid #e0e0e0;
            border-radius: 12rpx;
            padding: 0 24rpx;
            height: 88rpx;
            background: #fafafa;
            transition: all 0.2s ease;
            
            &:focus-within {
                border-color: #007AFF;
                background: #ffffff;
                box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
            }
            
            .password-input {
                flex: 1;
                height: 100%;
                font-size: 30rpx;
                color: #333;
                background: transparent;
                
                &::placeholder {
                    color: #999;
                    font-size: 28rpx;
                }
            }
            
            .eye-icon {
                width: 60rpx;
                height: 60rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8rpx;
                margin-left: 16rpx;
                transition: all 0.2s ease;
                
                &:active {
                    background: rgba(0, 0, 0, 0.05);
                    transform: scale(0.95);
                }
                
                .iconfont {
                    font-size: 32rpx;
                    color: #999;
                }
            }
        }
        
        .error-text {
            font-size: 24rpx;
            color: #ff3b30;
            margin-top: 12rpx;
            display: block;
        }
    }
}

.security-tips {
    margin: 0 32rpx 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .tips-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 20rpx;
        display: block;
    }
    
    .tip-item {
        display: block;
        font-size: 26rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 8rpx;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}

.save-btn {
    position: fixed;
    bottom: 48rpx;
    left: 32rpx;
    right: 32rpx;
    background: #007AFF;
    height: 88rpx;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
    transition: all 0.2s ease;
    
    &:active:not(.disabled) {
        transform: translateY(1rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
    }
    
    text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 600;
    }
    
    &.disabled {
        background: #cccccc;
        box-shadow: none;
        
        text {
            color: #ffffff;
        }
    }
}
</style> 