<template>
  <view class="reset-password-container">
    <view class="header">
      <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
      <text class="title">{{ t('user.resetPassword') }}</text>
    </view>

    <view class="description">
      {{ t('user.setNewPassword') }}
    </view>

    <view class="form-container">
      <view class="form-item">
        <text class="form-label">{{ t('user.newPassword') }}</text>
        <view class="input-wrapper">
          <input :type="showPassword ? 'text' : 'password'" class="form-input" :placeholder="t('user.enterNewPassword')" v-model="password" />
          <text class="iconfont eye-icon" :class="showPassword ? 'icon-eye' : 'icon-eye-close'" @click="togglePassword"></text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">{{ t('user.confirmPassword') }}</text>
        <view class="input-wrapper">
          <input :type="showConfirmPassword ? 'text' : 'password'" class="form-input" :placeholder="t('user.confirmNewPassword')" v-model="confirmPassword" />
          <text class="iconfont eye-icon" :class="showConfirmPassword ? 'icon-eye' : 'icon-eye-close'" @click="toggleConfirmPassword"></text>
        </view>
      </view>

      <button class="submit-btn" @click="resetPassword">{{ t('user.resetPassword') }}</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from '@/composables/useI18n.js'

// 国际化
const { t } = useI18n()

// 密码和确认密码
const password = ref('');
const confirmPassword = ref('');

// 是否显示密码
const showPassword = ref(false);
const showConfirmPassword = ref(false);

// 切换密码可见性
const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

// 切换确认密码可见性
const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value;
};

// 返回上一页
const navigateBack = () => {
  uni.navigateBack();
};

// 重置密码
const resetPassword = () => {
  // 简单的跳转逻辑
  uni.showToast({
    title: 'Password reset successful', // 这个页面需要添加 i18n 支持
    icon: 'success'
  });
  
  // 延迟跳转到登录页面
  setTimeout(() => {
    uni.reLaunch({
      url: '/pages/auth/login/index'
    });
  }, 1500);
};
</script>

<style lang="less">
.reset-password-container {
  min-height: 100vh;
  background-color: #fff;
  padding: 0 40rpx;

  .header {
    padding-top: 60rpx;
    margin-bottom: 24rpx;
    position: relative;

    .back-icon {
      position: absolute;
      left: 0rpx;
      top: 52rpx;
      font-size: 32rpx;
      color: #000;
      padding: 10rpx 0rpx;
      font-weight: bold;
      z-index: 1;
    }

    .title {
      font-size: 36rpx;
      color: #333;
      font-weight: 600;
      display: block;
      text-align: center;
    }
  }

  .description {
    font-size: 28rpx;
    color: #999;
    line-height: 1.5;
    text-align: left;
    margin: 40rpx 0 48rpx 0;
  }

  .form-container {
    width: 100%;

    .form-item {
      margin-bottom: 32rpx;

      .form-label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 16rpx;
        display: block;
      }

      .input-wrapper {
        position: relative;

        .form-input {
          width: 100%;
          height: 96rpx;
          background: #fff;
          border: 2rpx solid #eee;
          border-radius: 8rpx;
          padding: 0 80rpx 0 32rpx;
          font-size: 28rpx;
          color: #333;
          box-sizing: border-box;

          &::placeholder {
            color: #999;
            font-size: 28rpx;
          }
        }

        .eye-icon {
          position: absolute;
          right: 32rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 32rpx;
          color: #999;
          z-index: 1;
        }
      }
    }

    .submit-btn {
      width: 100%;
      height: 96rpx;
      background: #f23030;
      color: #fff;
      border-radius: 18rpx;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-top: 48rpx;
      border: none;
    }
  }
}
</style> 