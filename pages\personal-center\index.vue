<template>
    <view class="personal-center-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ $t('nav.account') }}</text>
                </view>
            </view>
        </view>
        
        <!-- 用户信息卡片 -->
        <view class="user-card" @click="navigateTo('/pages/personal-center/edit-profile')">
            <view class="avatar-container">
                <image :src="userInfo.accountPhotoUrl || '/static/images/avatar-red.png'" class="avatar" mode="aspectFill"></image>
            </view>
            <view class="user-info">
                <text class="username">{{ userInfo.accountName || '<PERSON>' }}</text>
                <text class="phone">{{ userInfo.accountPhone || '+225 **********' }}</text>
            </view>
            <view class="arrow-icon">
                <text class="iconfont icon-jinrujiantou"></text>
            </view>
        </view>

        <!-- 设置选项列表 -->
        <view class="settings-list">
            <!-- 账户安全 -->
            <view class="setting-item" @click="navigateTo('/pages/personal-center/account-security')">
                <text class="setting-name">{{ $t('settings.security') }}</text>
                <view class="arrow-icon">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 支付设置 -->
            <view class="setting-item" @click="navigateTo('/pages/personal-center/payment-settings')">
                <text class="setting-name">{{ $t('payment.title') }}</text>
                <view class="arrow-icon">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
            
            <!-- 分隔线 -->
            <view class="divider"></view>
            
            <!-- 语言设置 -->
            <view class="setting-item language-setting">
                <text class="setting-name">{{ $t('settings.language') }}</text>
                <view class="language-switcher-container">
                    <LanguageSwitcher type="select" @change="onLanguageChange" />
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 消息设置 -->
            <view class="setting-item" @click="navigateTo('/pages/personal-center/message-settings')">
                <text class="setting-name">{{ $t('settings.notifications') }}</text>
                <view class="arrow-icon">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>
            
            <!-- 问题反馈 -->
            <view class="setting-item" @click="navigateTo('/pages/personal-center/problem-feedback')">
                <text class="setting-name">{{ $t('nav.help') }}</text>
                <view class="arrow-icon">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 关于我们 -->
            <view class="setting-item" @click="navigateTo('/pages/personal-center/about-us')">
                <text class="setting-name">{{ $t('nav.about') }}</text>
                <view class="arrow-icon">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 隐私 -->
            <view class="setting-item" @click="navigateTo('/pages/personal-center/privacy')">
                <text class="setting-name">{{ $t('settings.privacy') }}</text>
                <view class="arrow-icon">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
        </view>
        
        <!-- 退出登录按钮 -->
        <view class="sign-out-btn" @click="signOut">
            <text>{{ $t('user.logout') }}</text>
        </view>

        <!-- Toast容器 -->
        <ToastContainer />
    </view>
</template>

<script setup>
import { ref, inject, onMounted, reactive } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useI18n } from 'vue-i18n'
import ToastContainer from '@/components/common/ToastContainer.vue'
import LanguageSwitcher from '@/components/common/LanguageSwitcher.vue'
import { useUserStore } from '@/store/user'
import { getUserInfo as fetchUserInfo } from '@/api/modules/user'

// 使用国际化
const { t } = useI18n()

// 注入Toast实例
const toast = inject('toast')

// 使用用户状态store
const userStore = useUserStore()

const statusBarHeight = ref(0)

// 用户信息对象
const userInfo = reactive({
    accountName: '',
    accountPhone: '',
    accountPhotoUrl: ''
})

// 防止重复请求的标志
let isLoadingUserInfo = false

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
    // 获取用户信息
    getUserInfo()
})

// 页面显示时重新获取用户信息（从编辑页面返回时会触发）
onShow(() => {
    getUserInfo()
})

// 获取用户信息
const getUserInfo = async () => {
    // 防止重复请求
    if (isLoadingUserInfo) {
        console.log('用户信息正在加载中，跳过重复请求')
        return
    }

    try {
        isLoadingUserInfo = true

        // 首先从store获取用户信息作为默认值
        const storeUserInfo = userStore.getUserInfo || {}
        userInfo.accountName = storeUserInfo.nickName || storeUserInfo.accountName || ''
        userInfo.accountPhone = storeUserInfo.accountPhone || ''
        userInfo.accountPhotoUrl = storeUserInfo.accountPhotoUrl || ''

        // 调用接口获取最新的用户信息（不显示成功提示）
        const openId = storeUserInfo.openId || uni.getStorageSync('openId') || ''
        if (openId) {
            const response = await fetchUserInfo({ openId }, { showLoading: false })
            if (response.code === 200 && response.data) {
                // 更新用户信息显示，优先使用nickName字段
                userInfo.accountName = response.data.nickName || response.data.nickname || response.data.username || ''
                userInfo.accountPhone = response.data.accountPhone || response.data.phonenumber || ''
                userInfo.accountPhotoUrl = response.data.accountPhotoUrl || response.data.avatar || ''

                // 同时更新store中的用户信息
                userStore.updateUserInfo({
                    accountName: userInfo.accountName,
                    accountPhone: userInfo.accountPhone,
                    accountPhotoUrl: userInfo.accountPhotoUrl,
                    nickName: response.data.nickName || response.data.nickname || response.data.username || '',
                    ...response.data
                })
            }
        }
    } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果接口调用失败，继续使用store中的信息
    } finally {
        isLoadingUserInfo = false
    }
}

const navigateTo = (url) => {
    uni.navigateTo({ url })
}

const goBack = () => {
    uni.navigateBack()
}

const signOut = () => {
    // 使用自定义Toast确认对话框
    toast.confirm({
        title: t('user.logout'),
        message: t('user.logoutConfirm'),
        confirmText: t('common.confirm'),
        cancelText: t('common.cancel'),
        onConfirm: () => {
            // 清除用户信息
            userStore.clearUserInfo()

            // 清理Toast状态
            toast.clearToastState()

            // 跳转到登录页
            uni.reLaunch({
                url: '/pages/auth/login/index'
            })
        }
    })
}

// 语言切换处理
const onLanguageChange = (langCode) => {
    console.log('Language changed to:', langCode)
    // 移除语言切换成功提示
    // toast.success(t('settings.languageChanged'))
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.personal-center-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.user-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    position: relative;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .avatar-container {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        overflow: hidden;
        margin-right: 24rpx;
        border: 2rpx solid #f0f0f0;
        background-color: #f8f8f8;
        
        .avatar {
            width: 100%;
            height: 100%;
        }
    }
    
    .user-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .username {
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
            margin-bottom: 8rpx;
        }
        
        .phone {
            font-size: 28rpx;
            color: #888;
        }
    }
    
    .arrow-icon {
        color: #ccc;
        font-size: 40rpx;
    }

    &:active {
        background-color: #f5f5f5;
    }
}

.settings-list {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;

        &:active {
            background-color: #f5f5f5;
        }

        .setting-name {
            font-size: 32rpx;
            color: #333;
        }

        .arrow-icon {
            color: #ccc;
            font-size: 40rpx;
        }

        &.language-setting {
            .language-switcher-container {
                flex-shrink: 0;
            }
        }
    }
    
    .divider {
        height: 1rpx;
        background-color: #f0f0f0;
        margin-left: 32rpx;
        margin-right: 32rpx;
    }
}

.sign-out-btn {
    margin: 64rpx 32rpx;
    background: #ffffff;
    height: 88rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    text {
        font-size: 32rpx;
        color: #666;
        font-weight: 500;
    }
    
    &:active {
        background-color: #f5f5f5;
    }
}
</style> 