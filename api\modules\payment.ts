import Request from '../../utils/request'
import type { ApiResponse } from '../types'

export interface PaymentRequestBody {
  orderNum: string
  paymentMethod: string // 固定传 "2"
  prepaidFees: string | number
  type: string // 固定传 '0'
}

export interface PaymentResponseData {
  orderId: string
  [key: string]: any
}

/**
 * 发起支付
 * POST /order/pay/payment
 */
export function payOrder(body: PaymentRequestBody): Promise<ApiResponse<PaymentResponseData>> {
  return Request.post('/order/pay/payment', body)
}

