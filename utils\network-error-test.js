/**
 * 网络异常提示功能测试工具
 * 用于测试网络异常检测和弹窗显示功能
 */

// 模拟网络异常的测试函数
export const testNetworkError = () => {
  console.log('🧪 开始测试网络异常提示功能')
  
  try {
    // 获取简化版网络异常处理模块
    const networkErrorModule = (globalThis).__simpleNetworkErrorModule
    
    if (!networkErrorModule) {
      console.error('❌ 简化版网络异常处理模块未找到')
      return false
    }
    
    // 模拟不同类型的网络异常
    const networkErrors = [
      {
        name: '网络连接失败',
        error: {
          request: {},
          message: 'Network Error',
          code: 'NETWORK_ERROR'
        }
      },
      {
        name: '请求超时',
        error: {
          request: {},
          message: 'timeout of 10000ms exceeded',
          code: 'ECONNABORTED'
        }
      },
      {
        name: '连接被拒绝',
        error: {
          request: {},
          message: 'connect ECONNREFUSED',
          code: 'ECONNREFUSED'
        }
      },
      {
        name: 'Fetch失败',
        error: {
          request: {},
          message: 'Failed to fetch',
          code: 'FETCH_ERROR'
        }
      }
    ]
    
    console.log('🔍 测试网络异常检测函数')
    
    networkErrors.forEach((testCase, index) => {
      const isDetected = networkErrorModule.isNetworkError(testCase.error)
      console.log(`${index + 1}. ${testCase.name}: ${isDetected ? '✅ 检测成功' : '❌ 检测失败'}`)
    })
    
    // 测试非网络异常（不应该被检测为网络异常）
    const nonNetworkErrors = [
      {
        name: '业务逻辑错误',
        error: {
          response: { status: 400, data: { message: 'Bad Request' } },
          message: 'Request failed with status code 400'
        }
      },
      {
        name: '服务器内部错误',
        error: {
          response: { status: 500, data: { message: 'Internal Server Error' } },
          message: 'Request failed with status code 500'
        }
      }
    ]
    
    console.log('🔍 测试非网络异常检测')
    
    nonNetworkErrors.forEach((testCase, index) => {
      const isDetected = networkErrorModule.isNetworkError(testCase.error)
      console.log(`${index + 1}. ${testCase.name}: ${isDetected ? '❌ 误检测' : '✅ 正确排除'}`)
    })
    
    return true
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
    return false
  }
}

// 手动触发网络异常弹窗的测试函数
export const testNetworkErrorModal = () => {
  console.log('🧪 测试网络异常弹窗显示')
  
  try {
    const networkErrorModule = (globalThis).__networkErrorModule
    
    if (!networkErrorModule) {
      console.error('❌ 网络异常处理模块未找到')
      return false
    }
    
    const { useGlobalNetworkError } = networkErrorModule
    const networkError = useGlobalNetworkError()
    
    if (networkError && networkError.showNetworkError) {
      console.log('✅ 手动触发网络异常弹窗')
      networkError.showNetworkError()
      
      // 5秒后自动隐藏（用于测试）
      setTimeout(() => {
        console.log('🕐 5秒后自动隐藏弹窗')
        networkError.hideNetworkError()
      }, 5000)
      
      return true
    } else {
      console.error('❌ 网络异常处理函数未找到')
      return false
    }
    
  } catch (error) {
    console.error('❌ 测试网络异常弹窗时发生错误:', error)
    return false
  }
}

// 测试系统网络状态检测
export const testSystemNetworkStatus = () => {
  console.log('🧪 测试系统网络状态检测')
  
  try {
    const networkErrorModule = (globalThis).__networkErrorModule
    
    if (!networkErrorModule) {
      console.error('❌ 网络异常处理模块未找到')
      return false
    }
    
    const { useGlobalNetworkError } = networkErrorModule
    const networkError = useGlobalNetworkError()
    
    if (networkError && networkError.checkCurrentNetworkStatus) {
      console.log('✅ 检查当前系统网络状态')
      networkError.checkCurrentNetworkStatus()
      
      // 显示当前网络状态
      setTimeout(() => {
        console.log('🌐 当前网络状态:', {
          isConnected: networkError.networkState.isConnected,
          networkType: networkError.networkState.networkType,
          lastCheckTime: new Date(networkError.networkState.lastCheckTime).toLocaleString()
        })
      }, 1000)
      
      return true
    } else {
      console.error('❌ 网络状态检测函数未找到')
      return false
    }
    
  } catch (error) {
    console.error('❌ 测试系统网络状态检测时发生错误:', error)
    return false
  }
}

// 测试国际化功能
export const testNetworkErrorI18n = () => {
  console.log('🌐 测试网络异常弹窗国际化功能')
  
  try {
    const networkErrorModule = (globalThis).__simpleNetworkErrorModule
    if (networkErrorModule) {
      console.log('🌐 当前语言:', networkErrorModule.getCurrentLocale())
      console.log('🌐 当前弹窗文本:', networkErrorModule.getNetworkErrorTexts())
      return true
    } else {
      console.error('❌ 简化版网络异常处理模块未找到')
      return false
    }
  } catch (error) {
    console.error('❌ 测试国际化功能失败:', error)
    return false
  }
}

// 测试语言切换
export const testLanguageSwitch = (language = 'fr') => {
  console.log(`🌐 测试切换语言到: ${language}`)
  
  try {
    if (language !== 'fr' && language !== 'en') {
      console.error('❌ 不支持的语言:', language, '支持的语言: fr, en')
      return false
    }
    
    // 切换语言
    uni.setStorageSync('locale', language)
    console.log(`✅ 语言已切换为: ${language}`)
    
    // 测试新的文本
    const networkErrorModule = (globalThis).__simpleNetworkErrorModule
    if (networkErrorModule) {
      console.log('🌐 新的弹窗文本:', networkErrorModule.getNetworkErrorTexts())
      // 显示弹窗查看效果
      networkErrorModule.showNetworkError()
      return true
    }
    
    return false
  } catch (error) {
    console.error('❌ 测试语言切换失败:', error)
    return false
  }
}

// 在控制台中可用的测试命令
if (typeof window !== 'undefined') {
  window.testNetworkError = testNetworkError
  window.testNetworkErrorModal = testNetworkErrorModal
  window.testSystemNetworkStatus = testSystemNetworkStatus
  window.testNetworkErrorI18n = testNetworkErrorI18n
  window.testLanguageSwitch = testLanguageSwitch
  console.log('🎯 网络异常测试工具已加载')
  console.log('💡 使用 testNetworkError() 测试检测功能')
  console.log('💡 使用 testNetworkErrorModal() 测试弹窗显示')
  console.log('💡 使用 testSystemNetworkStatus() 测试系统网络状态')
  console.log('💡 使用 testNetworkErrorI18n() 测试国际化功能')
  console.log('💡 使用 testLanguageSwitch("fr") 或 testLanguageSwitch("en") 测试语言切换')
}
