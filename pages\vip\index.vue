<template>
    <view class="vip-container">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="'height:' + statusBarHeight + 'px'"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <view class="header-title">
                        {{ $t('account.membership') || 'Membership' }}
                    </view>
                    <view class="placeholder"></view>
                </view>
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-wrapper">
            <!-- 激活会员卡片 -->
            <view class="activate-card">
                <view class="activate-content">
                    <text class="activate-title">{{ $t('account.activateMembership') || 'Activate Membership' }}</text>
                    <text class="activate-desc">{{ $t('account.membershipBenefits') || 'There are more membershipbenefits to claim' }}</text>
                </view>
            </view>

            <!-- 会员套餐价格 -->
            <view class="membership-section">
                <text class="section-title">{{ $t('account.membershipPackage') || 'Membership package price' }}</text>

                <view class="package-cards">
                    <!-- 会员卡片 -->
                    <view 
                        class="package-card" 
                        v-for="item in vipData" 
                        :key="item.id"
                        :class="{ 'active': selectedCard === item.id }"
                        @click="selectCard(item.id)"
                    >
                        <view class="recommended-tag" v-if="item.isRecommend">{{ $t('account.recommended') || 'Recommended' }}</view>
                        <text class="card-type">{{ item.typeName }}</text>
                        <view class="price">
                            <text class="current-price">{{ item.price }}</text>
                            <text class="currency">F</text>
                        </view>
                        <text class="original-price">{{ item.oldPrice }} F</text>
                    </view>
                </view>
            </view>

            <!-- 会员权益 -->
            <view class="benefits-section">
                <text class="section-title">Membership benefits</text>

                <!-- 无限次数 -->
                <view class="benefit-card">
                    <view class="benefit-info">
                        <text class="benefit-title">Unlimited times</text>
                        <text class="benefit-desc">Member Price</text>
                    </view>
                    <view class="benefit-badge">
                        <text>Member</text>
                        <text>Exclusive</text>
                    </view>
                </view>

                <!-- 会员六大权益 -->
                <view class="rights-section">
                    <view class="rights-header">
                        <text class="rights-title">Members enjoy</text>
                        <text class="rights-highlight">6</text>
                        <text class="rights-title">major rights and interests</text>
                    </view>

                    <view class="rights-grid">
                        <!-- 月度福利 -->
                        <view class="right-item">
                            <view class="right-icon">
                                <image src="/static/images/vip-benefit-1.png" mode="aspectFit"></image>
                            </view>
                            <text class="right-name">Monthly Benefits</text>
                        </view>

                        <!-- 奖励特权 -->
                        <view class="right-item">
                            <view class="right-icon">
                                <image src="/static/images/vip-benefit-2.png" mode="aspectFit"></image>
                            </view>
                            <text class="right-name">Reward Privileges</text>
                        </view>

                        <!-- 专属勋章 -->
                        <view class="right-item">
                            <view class="right-icon">
                                <image src="/static/images/vip-benefit-3.png" mode="aspectFit"></image>
                            </view>
                            <text class="right-name">Exclusive Medal</text>
                        </view>

                        <!-- 红包福利 -->
                        <view class="right-item">
                            <view class="right-icon">
                                <image src="/static/images/vip-benefit-4.png" mode="aspectFit"></image>
                            </view>
                            <text class="right-name">Red Packet Benefits</text>
                        </view>

                        <!-- 专属权益1 -->
                        <view class="right-item">
                            <view class="right-icon">
                                <image src="/static/images/vip-benefit-5.png" mode="aspectFit"></image>
                            </view>
                            <text class="right-name">Exclusive Rights</text>
                        </view>

                        <!-- 专属权益2 -->
                        <view class="right-item">
                            <view class="right-icon">
                                <image src="/static/images/vip-benefit-6.png" mode="aspectFit"></image>
                            </view>
                            <text class="right-name">Exclusive Rights</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 底部激活按钮 -->
            <view class="activate-btn">
                <text>Activate membership</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ref } from 'vue'
import { getVipInfoList } from '@/api'
// 使用国际化
const { t } = useI18n()
const vipData=ref([])
// 状态栏高度
const statusBarHeight = ref(20)
// 选中的会员卡ID
const selectedCard = ref(null)

onMounted(() => {
    // 获取状态栏高度
    try {
        const systemInfo = uni.getSystemInfoSync()
        if (systemInfo && systemInfo.statusBarHeight) {
            statusBarHeight.value = systemInfo.statusBarHeight
        }
    } catch (e) {
        console.error('获取系统信息失败', e)
    }
    getVipInfo()
})
const getVipInfo = async () => {
    const result = await getVipInfoList()
    console.log(result)
    if(result.code==200){
        vipData.value=result.rows
        // 默认选中推荐的会员卡
        const recommendedCard = result.rows.find(item => item.isRecommend)
        if (recommendedCard) {
            selectedCard.value = recommendedCard.id
        } else if (result.rows.length > 0) {
            // 如果没有推荐的卡，默认选中第一个
            selectedCard.value = result.rows[0].id
        }
    }
}

// 选择会员卡
const selectCard = (id) => {
    selectedCard.value = id
}

const goBack = () => {
    uni.navigateBack()
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

page {
    --status-bar-height: 0px;
}

.vip-container {
    min-height: 100vh;
    background-color: #FFFCF3;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #FFFCF3;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.02);
}

.status-bar {
    background-color: #FFFCF3;
    width: 100%;
    height: 44rpx;
}

.header {
    background: #FFFCF3;
    width: 100%;

    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 32rpx;

        .back-btn {
            width: 60rpx;
            display: flex;
            align-items: center;

            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }

        .header-title {
            font-size: 34rpx;
            font-weight: 500;
            color: #333;
        }

        .placeholder {
            width: 60rpx;
        }
    }
}

.content-wrapper {
    flex: 1;
    background: #ffffff;
    padding-top: 48rpx;
    padding-bottom: 32rpx;
    margin-top: 90rpx;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.03);
}

.activate-card {
    margin: 24rpx 32rpx;
    background: linear-gradient(to right, #FF7B3B, #FF5722);
    border-radius: 24rpx;
    padding: 40rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8rpx 20rpx rgba(255, 87, 34, 0.2);
    position: relative;
    overflow: hidden;

    &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        width: 40%;
        height: 100%;
        background-image: url('/static/images/vip-bg.png');
        background-size: cover;
        background-position: right center;
        opacity: 0.5;
    }

    .activate-content {
        display: flex;
        flex-direction: column;
        position: relative;
        z-index: 1;

        .activate-title {
            font-size: 48rpx;
            color: #fff;
            font-weight: 600;
            margin-bottom: 12rpx;
        }

        .activate-desc {
            font-size: 28rpx;
            color: rgba(255, 255, 255, 0.9);
        }
    }

    .membership-icon {
        width: 120rpx;
        height: 120rpx;
        position: relative;
        z-index: 1;
        margin-right: 20rpx;
    }
}

.membership-section {
    margin: 40rpx 32rpx 0;

    .section-title {
        font-size: 36rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 24rpx;
        display: block;
    }

    .package-cards {
        display: flex;
        justify-content: space-between;
        gap: 20rpx;

        .package-card {
            flex: 1;
            border-radius: 24rpx;
            border: 2rpx solid #eee;
            padding: 30rpx 20rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            background-color: #fff;

            &.active {
                border-color: #FF5722;
                background-color: #FFF9F7;
            }

            .recommended-tag {
                position: absolute;
                top: -1rpx;
                left: -1rpx;
                background-color: #FF5722;
                color: #fff;
                font-size: 20rpx;
                padding: 6rpx 16rpx;
                border-top-left-radius: 24rpx;
                border-bottom-right-radius: 16rpx;
            }

            .card-type {
                font-size: 32rpx;
                color: #333;
                font-weight: 500;
                margin-bottom: 16rpx;
                text-align: center;
            }

            .price {
                display: flex;
                align-items: flex-end;
                margin-bottom: 8rpx;

                .current-price {
                    font-size: 52rpx;
                    color: #FF5722;
                    font-weight: 700;
                    line-height: 1;
                }

                .currency {
                    font-size: 24rpx;
                    color: #FF5722;
                    margin-left: 4rpx;
                    margin-bottom: 8rpx;
                }
            }

            .original-price {
                font-size: 24rpx;
                color: #999;
                text-decoration: line-through;
            }
        }
    }
}

.benefits-section {
    margin: 50rpx 32rpx 0;

    .section-title {
        font-size: 36rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 24rpx;
        display: block;
    }

    .benefit-card {
        background-image: url('/static/images/vip-recharge-bg.png');
        background-size: 100% 100%;
        background-position: center;
        border-radius: 24rpx;
        padding: 40rpx 40rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 50rpx;
        overflow: hidden;
        box-sizing: border-box;
        width: 100%;

        .benefit-info {
            display: flex;
            flex-direction: column;

            .benefit-title {
                font-size: 42rpx;
                color: #663C00;
                font-weight: 600;
                margin-bottom: 12rpx;
            }

            .benefit-desc {
                font-size: 30rpx;
                color: #8B5300;
            }
        }

        .benefit-badge {
            // background: #FFD700;
            border-radius: 50%;
            width: 160rpx;
            height: 160rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            text {
                color: #663C00;
                font-weight: 600;
                font-size: 28rpx;
                line-height: 1.4;
            }
        }
    }

    .rights-section {
        .rights-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 50rpx;

            .rights-title {
                font-size: 32rpx;
                color: #333;
            }

            .rights-highlight {
                font-size: 48rpx;
                color: #FF5722;
                font-weight: bold;
                margin: 0 12rpx;
            }
        }

        .rights-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;

            .right-item {
                width: 33.33%;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 48rpx;

                .right-icon {
                    width: 120rpx;
                    height: 120rpx;
                    background-color: #FFF5E8;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 16rpx;

                    image {
                        width: 80rpx;
                        height: 80rpx;
                    }
                }

                .right-name {
                    font-size: 26rpx;
                    color: #666;
                    text-align: center;
                    margin-top: 8rpx;
                }
            }
        }
    }
}

.activate-btn {
    margin: 60rpx 32rpx 0;
    height: 90rpx;
    background: linear-gradient(to right, #FF7B3B, #FF5722);
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 20rpx rgba(255, 87, 34, 0.2);

    text {
        color: #fff;
        font-size: 32rpx;
        font-weight: 500;
    }
}
</style>