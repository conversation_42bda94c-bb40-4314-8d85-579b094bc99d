<template>
  <view class="register-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
      <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
      <text class="title">{{ t('user.register') }}</text>
      </view>
    </view>

    <!-- 注册表单 -->
    <view class="form-container">
      <!-- First Name -->
      <view class="form-item">
        <input type="text" class="form-input" v-model="formData.firstName"
          :placeholder="t('user.firstName') || 'First Name'" maxlength="30" @input="validateFirstName" />
        <text v-if="errors.firstName" class="error-text">{{ errors.firstName }}</text>
      </view>

      <!-- Last Name -->
      <view class="form-item">
        <input type="text" class="form-input" v-model="formData.lastName"
          :placeholder="t('user.lastName') || 'Last Name'" maxlength="30" @input="validateLastName" />
        <text v-if="errors.lastName" class="error-text">{{ errors.lastName }}</text>
      </view>

      <!-- Phone Number -->
      <view class="form-item">
        <input type="tel" class="form-input" v-model="formData.phoneNumber"
          :placeholder="t('user.phoneNumber') || 'Phone Number'" @input="validatePhoneNumber" />
        <text v-if="errors.phoneNumber" class="error-text">{{ errors.phoneNumber }}</text>
      </view>

      <!-- Verification Code -->
      <view class="form-item">
        <view class="input-wrapper">
          <input type="number" class="form-input" v-model="formData.verificationCode"
            :placeholder="t('user.enterVerificationCode') || 'Verification Code'" maxlength="6"
            @input="validateVerificationCode" />
          <text class="code-btn" :class="{ disabled: isSendingCode || countdown > 0 }" @click="sendVerificationCode">
            {{ countdown > 0 ? `${countdown}s` : (t('user.send') || 'Send') }}
          </text>
        </view>
        <text v-if="errors.verificationCode" class="error-text">{{ errors.verificationCode }}</text>
      </view>

      <!-- Password -->
      <view class="form-item">
        <view class="input-wrapper">
          <input :type="showPassword ? 'text' : 'password'" class="form-input" v-model="formData.password"
            :placeholder="t('user.enterPassword') || 'Password'" maxlength="4" @input="validatePassword" />
          <text class="iconfont eye-icon" :class="showPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
            @click="togglePassword"></text>
        </view>
        <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
      </view>

      <!-- Confirm Password -->
      <view class="form-item">
        <view class="input-wrapper">
          <input :type="showConfirmPassword ? 'text' : 'password'" class="form-input" v-model="formData.confirmPassword"
            :placeholder="t('user.confirmPassword') || 'Confirm Password'" maxlength="4"
            @input="validateConfirmPassword" />
          <text class="iconfont eye-icon" :class="showConfirmPassword ? 'icon-mimayanjing' : 'icon-yanjingmima'"
            @click="toggleConfirmPassword"></text>
        </view>
        <text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
      </view>

      <!-- 用户协议 -->
      <view class="agreement">
        <checkbox :checked="agreeTerms" @click="toggleAgreement" color="#f23030" />
        <view class="agreement-text" @click="toggleAgreement">
          <text>{{ t('user.agreementText1') || 'I understand and agree to the' }}</text>
          <text class="link">{{ t('settings.privacy') || 'Privacy Policy' }}</text>
          <text>{{ t('user.agreementText2') || 'and' }}</text>
          <text class="link">{{ t('user.termsOfService') || 'Terms of Service' }}</text>
        </view>
      </view>

      <!-- 注册按钮 -->
      <button class="submit-btn" @click="register">
        {{ t('user.register') || 'Register' }}
      </button>

      <!-- 登录链接 -->
      <view class="login-link">
        <text>{{ t('user.haveAccount') || 'Already have an account?' }}</text>
        <text class="link" @click="goToLogin">{{ t('user.login') || 'Login' }}</text>
      </view>
    </view>
    
    <!-- 全局HUD组件 -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { registerUser, sendSmsCode, verifySmsCode, passwordLogin } from '@/api'
import { useUserStore } from '@/store/user'
import { useGlobalHud } from '@/composables/useHud'
import GlobalHUD from '@/components/common/GlobalHUD.vue'

// 国际化
const { t } = useI18n()

// 获取用户store
const userStore = useUserStore()

// 全局HUD
const hud = useGlobalHud()

// 状态栏高度
const statusBarHeight = ref(0)

// 表单数据
const formData = ref({
  firstName: '',
  lastName: '',
  phoneNumber: '',
  verificationCode: '',
  password: '',
  confirmPassword: ''
})

// 错误信息
const errors = ref({
  firstName: '',
  lastName: '',
  phoneNumber: '',
  verificationCode: '',
  password: '',
  confirmPassword: ''
})

// 是否显示密码
const showPassword = ref(false)
// 是否显示确认密码
const showConfirmPassword = ref(false)
// 是否同意协议
const agreeTerms = ref(false)

// 初始化调试
console.log('agreeTerms初始值:', agreeTerms.value)
// 验证码发送状态
const isSendingCode = ref(false)
const countdown = ref(0)
let countdownTimer = null

// 表单验证
const validateFirstName = () => {
  const value = formData.value.firstName
  if (!value) {
    errors.value.firstName = 'First name is required'
  } else if (!/^[a-zA-Z\s]+$/.test(value)) {
    errors.value.firstName = 'First name can only contain letters'
  } else if (value.length < 2) {
    errors.value.firstName = 'First name must be at least 2 characters'
  } else {
    errors.value.firstName = ''
  }
}

const validateLastName = () => {
  const value = formData.value.lastName
  if (!value) {
    errors.value.lastName = 'Last name is required'
  } else if (!/^[a-zA-Z\s]+$/.test(value)) {
    errors.value.lastName = 'Last name can only contain letters'
  } else if (value.length < 2) {
    errors.value.lastName = 'Last name must be at least 2 characters'
  } else {
    errors.value.lastName = ''
  }
}

const validatePhoneNumber = () => {
  const value = formData.value.phoneNumber
  if (!value) {
    errors.value.phoneNumber = 'Phone number is required'
  } else if (!/^225\d{8,10}$/.test(value)) {
    errors.value.phoneNumber = 'Please enter a valid phone number (225 + 8-10 digits)'
  } else {
    errors.value.phoneNumber = ''
  }
}

const validateVerificationCode = () => {
  const value = formData.value.verificationCode
  if (!value) {
    errors.value.verificationCode = 'Verification code is required'
  } else if (!/^\d{6}$/.test(value)) {
    errors.value.verificationCode = 'Verification code must be 6 digits'
  } else {
    errors.value.verificationCode = ''
  }
}

const validatePassword = () => {
  const value = formData.value.password
  if (!value) {
    errors.value.password = 'Password is required'
  } else if (!/^\d{4}$/.test(value)) {
    errors.value.password = 'Password must be exactly 4 digits'
  } else {
    errors.value.password = ''
  }
  // 同时验证确认密码
  if (formData.value.confirmPassword) {
    validateConfirmPassword()
  }
}

const validateConfirmPassword = () => {
  const value = formData.value.confirmPassword
  if (!value) {
    errors.value.confirmPassword = 'Please confirm your password'
  } else if (value !== formData.value.password) {
    errors.value.confirmPassword = 'Passwords do not match'
  } else {
    errors.value.confirmPassword = ''
  }
}

// 发送验证码
const sendVerificationCode = async () => {
  // 检查手机号是否为空
  if (!formData.value.phoneNumber) {
    hud.error('Please enter phone number first')
    return
  }

  // 验证手机号格式
  const phoneRegex = /^225\d{8,10}$/
  if (!phoneRegex.test(formData.value.phoneNumber)) {
    hud.error('Please enter a valid phone number')
    return
  }

  // 如果正在发送或倒计时中，不允许再次发送
  if (isSendingCode.value || countdown.value > 0) {
    return
  }

  try {
    isSendingCode.value = true

    // 显示加载提示
    hud.loading('Sending...')

    // 调用发送短信验证码API
    const response = await sendSmsCode(formData.value.phoneNumber)

    // 发送成功，显示成功提示并开始倒计时
    hud.success('Verification code sent', 2000)

    startCountdown()

  } catch (error) {
    console.error('发送验证码失败:', error)

    // 显示错误提示
    let errorMessage = 'Failed to send verification code'
    if (error.response) {
      errorMessage = error.response.data?.message || errorMessage
    } else if (error.message) {
      errorMessage = error.message
    }

    hud.error(errorMessage)
  } finally {
    isSendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60

  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }

  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 表单是否有效
const isFormValid = computed(() => {
  const valid = formData.value.firstName &&
    formData.value.lastName &&
    formData.value.phoneNumber &&
    formData.value.verificationCode &&
    formData.value.password &&
    formData.value.confirmPassword &&
    agreeTerms.value &&
    !errors.value.firstName &&
    !errors.value.lastName &&
    !errors.value.phoneNumber &&
    !errors.value.verificationCode &&
    !errors.value.password &&
    !errors.value.confirmPassword

  console.log('表单验证状态:', {
    firstName: formData.value.firstName,
    lastName: formData.value.lastName,
    phoneNumber: formData.value.phoneNumber,
    verificationCode: formData.value.verificationCode,
    password: formData.value.password,
    confirmPassword: formData.value.confirmPassword,
    agreeTerms: agreeTerms.value,
    errors: errors.value,
    isValid: valid
  })

  return valid
})

// 切换密码可见性
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 切换确认密码可见性
const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

// 切换用户协议
const toggleAgreement = () => {
  agreeTerms.value = !agreeTerms.value
  console.log('协议状态切换:', agreeTerms.value)
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 注册方法
const register = async () => {
  console.log('注册按钮被点击了！')
  console.log('当前表单数据:', formData.value)
  console.log('当前错误状态:', errors.value)
  console.log('协议同意状态:', agreeTerms.value)

  // 验证所有字段
  validateFirstName()
  validateLastName()
  validatePhoneNumber()
  validateVerificationCode()
  validatePassword()
  validateConfirmPassword()

  console.log('验证后的错误状态:', errors.value)

  // 详细检查协议状态
  console.log('=== 协议状态详细检查 ===')
  console.log('agreeTerms.value:', agreeTerms.value)
  console.log('agreeTerms类型:', typeof agreeTerms.value)
  console.log('agreeTerms是否为true:', agreeTerms.value === true)
  console.log('agreeTerms是否为false:', agreeTerms.value === false)

  if (!agreeTerms.value) {
    console.log('用户协议未勾选，阻止注册')
    hud.error('Please agree to the terms and conditions')
    return
  }

  console.log('协议已勾选，继续注册流程')

  // 检查是否有错误
  const hasErrors = Object.values(errors.value).some(error => error !== '')
  if (hasErrors) {
    console.log('表单有错误，无法提交')
    hud.error('Please fix the errors in the form')
    return
  }

  // 检查必填字段
  if (!formData.value.firstName || !formData.value.lastName || !formData.value.phoneNumber ||
    !formData.value.verificationCode || !formData.value.password || !formData.value.confirmPassword) {
    console.log('必填字段未填写完整')
    hud.error('Please fill in all required fields')
    return
  }

  console.log('开始注册流程...')

  try {
    hud.loading('Verifying...')

    // 第一步：验证验证码
    console.log('开始验证验证码...')
    const verifyResponse = await verifySmsCode({
      phonenumber: formData.value.phoneNumber,
      smsCode: formData.value.verificationCode,
      clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
      grantType: "sms",
    })

    console.log('验证码验证响应:', verifyResponse)

    if (!verifyResponse || (verifyResponse.code !== 200 && verifyResponse.code !== "200")) {
      throw new Error(verifyResponse?.message || 'Verification code is invalid')
    }

    hud.success('Verification successful', 1500)

    // 第二步：验证成功后，调用注册接口
    setTimeout(async () => {
      try {
        hud.loading('Registering...')

        console.log('开始注册...')
        const response = await registerUser({
          
          grantType: "accountPassword",
          tenantId: "000000",
          password: formData.value.password,
          userType: "account_app_user",
          phone: formData.value.phoneNumber,
          nickName: `${formData.value.firstName} ${formData.value.lastName}`
        })

        console.log('注册响应:', response)

        if (response && (response.code === 200 || response.code === "200")) {
          hud.success('Registration successful!', 1500)

          // 注册成功后自动登录
          setTimeout(async () => {
            try {
              hud.loading('Auto logging in...')

              console.log('开始自动登录...')
              const loginResponse = await passwordLogin({
                clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
                grantType: "accountPassword",
                password: formData.value.password,
                rememberMe: false,
                tenantId: "000000",
                username: formData.value.phoneNumber
              })

              console.log('自动登录响应:', loginResponse)

              if (loginResponse && (loginResponse.code === 200 || loginResponse.code === "200") && loginResponse.data) {
                // 使用Pinia存储用户信息
                userStore.loginSuccess(loginResponse.data)
                
                // 注册成功后自动保存手机号用于快速登录
                if (formData.value.phoneNumber) {
                  userStore.savePhoneNumber(formData.value.phoneNumber)
                  console.log('手机号已保存用于快速登录:', formData.value.phoneNumber)
                }
                
                // 同时也保存到本地存储，以便下次启动时恢复
                uni.setStorageSync('userInfo', JSON.stringify(loginResponse.data))
                if (loginResponse.data.access_token) {
                  uni.setStorageSync('token', loginResponse.data.access_token)
                } else if (loginResponse.data.token) {
                  uni.setStorageSync('token', loginResponse.data.token)
                }

                hud.success('Login successful!', 1500)

                // 跳转到主页面
                setTimeout(() => {
                  uni.reLaunch({
                    url: '/pages/home/<USER>'
                  })
                }, 1500)
              } else {
                // 自动登录失败，跳转到登录页面
                console.log('自动登录失败，跳转到登录页面')
                hud.success('Registration successful! Please login.', 1500)
                setTimeout(() => {
                  uni.navigateTo({
                    url: '/pages/auth/login/index'
                  })
                }, 1500)
              }

            } catch (loginError) {
              console.error('自动登录失败:', loginError)
              
              // 自动登录失败，跳转到登录页面
              hud.success('Registration successful! Please login.', 1500)
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/auth/login/index'
                })
              }, 1500)
            }
          }, 1500)
        } else {
          throw new Error(response?.message || 'Registration failed')
        }

      } catch (registerError) {
        console.error('注册失败:', registerError)

        let errorMessage = 'Registration failed'
        if (registerError.response) {
          errorMessage = registerError.response.data?.message || registerError.response.data?.msg || errorMessage
        } else if (registerError.message) {
          errorMessage = registerError.message
        }

        hud.error(errorMessage)
      }
    }, 1500)

  } catch (error) {
    console.error('验证码验证失败:', error)

    let errorMessage = 'Verification failed'
    if (error.response) {
      errorMessage = error.response.data?.message || error.response.data?.msg || errorMessage
    } else if (error.message) {
      errorMessage = error.message
    }

    hud.error(errorMessage)
  }
}

// 跳转到登录页
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/auth/login/index'
  })
}

// 组件挂载时获取状态栏高度
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
})
</script>

<style lang="less">
/* 全局复选框样式 */
:deep(.uni-checkbox-input) {
  border-radius: 50% !important;
  width: 36rpx !important;
  height: 36rpx !important;
  border: 2rpx solid #ddd !important;
  background-color: #fff !important;
}

:deep(.uni-checkbox-input.uni-checkbox-input-checked) {
  background-color: #f23030 !important;
  border-color: #f23030 !important;
}

:deep(.uni-checkbox-input.uni-checkbox-input-checked::before) {
  font-size: 28rpx;
  color: #fff;
}

.register-container {
  min-height: 100vh;
  height: 100vh;
  background-color: #fff;
  padding: 0 40rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 32rpx 0;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
  }

    .back-icon {
      position: absolute;
    left: 0;
      font-size: 32rpx;
      color: #000;
      font-weight: bold;
    }

    .title {
      font-size: 36rpx;
      color: #333;
      font-weight: 600;
  }

  .form-container {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    margin-top: 40rpx;

    .form-item {
      margin-bottom: 20rpx;

      .form-input {
        width: 100%;
        height: 88rpx;
        background: #fff;
        border: 1rpx solid #E5E5E5;
        border-radius: 8rpx;
        padding: 0 32rpx;
        font-size: 28rpx;
        color: #333;
        box-sizing: border-box;

        &::placeholder {
          color: #999;
          font-size: 26rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:focus {
          border-color: #f23030;
        }
      }

      .error-text {
        color: #f23030;
        font-size: 24rpx;
        margin-top: 8rpx;
        display: block;
      }

      .input-wrapper {
        position: relative;

        .form-input {
          padding-right: 120rpx;
        }

        .eye-icon {
          position: absolute;
          right: 32rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 32rpx;
          color: #999;
          z-index: 1;
          padding: 10rpx;
          
          &.icon-mimayanjing {
            color: #f23030;
          }
          
          &.icon-yanjingmima {
            color: #999;
          }
        }

        .code-btn {
          position: absolute;
          right: 32rpx;
          top: 50%;
          transform: translateY(-50%);
          color: #f23030;
          font-size: 28rpx;
          z-index: 1;
          padding: 10rpx;

          &.disabled {
            color: #ccc;
          }
        }
      }
    }

    .agreement {
      display: flex;
      align-items: flex-start;
      margin: 30rpx 0 40rpx;

      checkbox {
        margin: 6rpx 4rpx 0 0;
      }

      .agreement-text {
        font-size: 26rpx;
        color: #333;
        margin-left: 8rpx;
        line-height: 1.4;

        .link {
          color: #333;
          text-decoration: underline;
        }
      }
    }

    .submit-btn {
      width: 100%;
      height: 88rpx;
      background: #f23030;
      color: #fff;
      border-radius: 56rpx;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      margin-top: 20rpx;
      border: none;

      &:disabled {
        background: #ccc;
        color: #999;
      }
    }

    .login-link {
      text-align: center;
      margin-top: 32rpx;

      text {
        font-size: 28rpx;
        color: #666;

        &.link {
          color: #f23030;
          margin-left: 8rpx;
        }
      }
    }
  }
}
</style> 