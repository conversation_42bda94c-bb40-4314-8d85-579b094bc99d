/**
 * 性能优化器
 * 提供全局的性能优化策略和自适应配置
 */

import { getDevicePerformanceLevel, getPerformanceConfig, isLowEndDevice, DEVICE_PERFORMANCE_LEVEL } from './device-performance.js';

/**
 * 全局性能优化器类
 */
class PerformanceOptimizer {
  constructor() {
    this.isInitialized = false;
    this.performanceLevel = null;
    this.config = null;
    this.isLowEnd = false;
    this.optimizationStrategies = new Map();
    this.activeOptimizations = new Set();
  }

  /**
   * 初始化性能优化器
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('初始化性能优化器...');
      
      // 检测设备性能
      this.performanceLevel = await getDevicePerformanceLevel();
      this.config = await getPerformanceConfig();
      this.isLowEnd = await isLowEndDevice();
      
      // 注册优化策略
      this.registerOptimizationStrategies();
      
      // 应用全局优化
      this.applyGlobalOptimizations();
      
      this.isInitialized = true;
      
      console.log('性能优化器初始化完成:', {
        level: this.performanceLevel,
        isLowEnd: this.isLowEnd,
        strategies: Array.from(this.optimizationStrategies.keys())
      });
    } catch (error) {
      console.error('性能优化器初始化失败:', error);
      // 使用保守的默认配置
      this.performanceLevel = DEVICE_PERFORMANCE_LEVEL.LOW;
      this.isLowEnd = true;
      this.config = {
        scanner: {
          enableAnimations: false,
          scanQuality: 'low',
          previewFrameRate: 15,
          enableHoverEffects: false,
          enable3DRendering: false
        },
        ui: {
          enableComplexAnimations: false,
          enableParticleEffects: false,
          maxConcurrentAnimations: 1
        }
      };
    }
  }

  /**
   * 注册优化策略
   */
  registerOptimizationStrategies() {
    // CSS动画优化策略
    this.optimizationStrategies.set('css-animations', {
      condition: () => this.isLowEnd || !this.config.ui.enableComplexAnimations,
      apply: () => this.disableCSSAnimations(),
      remove: () => this.enableCSSAnimations()
    });

    // 3D渲染优化策略
    this.optimizationStrategies.set('3d-rendering', {
      condition: () => this.isLowEnd || !this.config.scanner.enable3DRendering,
      apply: () => this.optimize3DRendering(),
      remove: () => this.restore3DRendering()
    });

    // 扫码优化策略
    this.optimizationStrategies.set('scanner-optimization', {
      condition: () => this.isLowEnd,
      apply: () => this.optimizeScannerPerformance(),
      remove: () => this.restoreScannerPerformance()
    });

    // 内存管理优化策略
    this.optimizationStrategies.set('memory-management', {
      condition: () => this.isLowEnd,
      apply: () => this.enableAggressiveMemoryManagement(),
      remove: () => this.disableAggressiveMemoryManagement()
    });

    // UI简化策略
    this.optimizationStrategies.set('ui-simplification', {
      condition: () => this.isLowEnd,
      apply: () => this.simplifyUI(),
      remove: () => this.restoreUI()
    });
  }

  /**
   * 应用全局优化
   */
  applyGlobalOptimizations() {
    for (const [name, strategy] of this.optimizationStrategies) {
      if (strategy.condition()) {
        try {
          strategy.apply();
          this.activeOptimizations.add(name);
          console.log(`应用优化策略: ${name}`);
        } catch (error) {
          console.warn(`应用优化策略失败: ${name}`, error);
        }
      }
    }
  }

  /**
   * 禁用CSS动画
   */
  disableCSSAnimations() {
    const style = document.createElement('style');
    style.id = 'performance-optimizer-css-animations';
    style.textContent = `
      .low-end-device *,
      .no-animations * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
        filter: none !important;
        box-shadow: none !important;
        text-shadow: none !important;
      }
      
      .low-end-device .scan-icon::before,
      .no-animations .scan-icon::before {
        display: none !important;
      }
    `;
    
    // #ifdef H5
    if (typeof document !== 'undefined') {
      document.head.appendChild(style);
    }
    // #endif
  }

  /**
   * 启用CSS动画
   */
  enableCSSAnimations() {
    // #ifdef H5
    if (typeof document !== 'undefined') {
      const style = document.getElementById('performance-optimizer-css-animations');
      if (style) {
        style.remove();
      }
    }
    // #endif
  }

  /**
   * 优化3D渲染
   */
  optimize3DRendering() {
    // 设置全局3D渲染标志
    uni.setStorageSync('disable3DRendering', true);
    
    // 通知所有3D组件暂停渲染
    uni.$emit('optimize3DRendering', { disable: true });
  }

  /**
   * 恢复3D渲染
   */
  restore3DRendering() {
    uni.removeStorageSync('disable3DRendering');
    uni.$emit('optimize3DRendering', { disable: false });
  }

  /**
   * 优化扫码性能
   */
  optimizeScannerPerformance() {
    // 设置扫码优化标志
    uni.setStorageSync('scannerOptimization', {
      lowQuality: true,
      reducedFrameRate: true,
      simplifiedUI: true
    });
  }

  /**
   * 恢复扫码性能
   */
  restoreScannerPerformance() {
    uni.removeStorageSync('scannerOptimization');
  }

  /**
   * 启用激进的内存管理
   */
  enableAggressiveMemoryManagement() {
    // 设置更短的垃圾回收间隔
    if (typeof setInterval !== 'undefined') {
      this.memoryCleanupInterval = setInterval(() => {
        this.performMemoryCleanup();
      }, 30000); // 30秒清理一次
    }
  }

  /**
   * 禁用激进的内存管理
   */
  disableAggressiveMemoryManagement() {
    if (this.memoryCleanupInterval) {
      clearInterval(this.memoryCleanupInterval);
      this.memoryCleanupInterval = null;
    }
  }

  /**
   * 执行内存清理
   */
  performMemoryCleanup() {
    try {
      // 清理可能的内存泄漏
      uni.$emit('performMemoryCleanup');
      
      // 强制垃圾回收（如果支持）
      if (typeof gc !== 'undefined') {
        gc();
      }
      
      console.log('执行内存清理');
    } catch (error) {
      console.warn('内存清理失败:', error);
    }
  }

  /**
   * 简化UI
   */
  simplifyUI() {
    // 设置UI简化标志
    uni.setStorageSync('simplifyUI', true);
    
    // 通知组件简化UI
    uni.$emit('simplifyUI', true);
  }

  /**
   * 恢复UI
   */
  restoreUI() {
    uni.removeStorageSync('simplifyUI');
    uni.$emit('simplifyUI', false);
  }

  /**
   * 获取扫码优化配置
   * @returns {Object} 扫码配置
   */
  getScannerConfig() {
    if (!this.isInitialized) {
      return null;
    }

    return {
      quality: this.config.scanner.scanQuality,
      frameRate: this.config.scanner.previewFrameRate,
      enableAnimations: this.config.scanner.enableAnimations,
      enableHoverEffects: this.config.scanner.enableHoverEffects,
      compressed: this.isLowEnd,
      scanArea: this.isLowEnd ? {
        x: 0.2,
        y: 0.3,
        width: 0.6,
        height: 0.4
      } : {
        x: 0,
        y: 0,
        width: 1,
        height: 1
      }
    };
  }

  /**
   * 获取UI配置
   * @returns {Object} UI配置
   */
  getUIConfig() {
    if (!this.isInitialized) {
      return null;
    }

    return {
      enableComplexAnimations: this.config.ui.enableComplexAnimations,
      enableParticleEffects: this.config.ui.enableParticleEffects,
      maxConcurrentAnimations: this.config.ui.maxConcurrentAnimations,
      simplifiedMode: this.isLowEnd
    };
  }

  /**
   * 检查是否应用了特定优化
   * @param {string} optimizationName 优化名称
   * @returns {boolean} 是否已应用
   */
  isOptimizationActive(optimizationName) {
    return this.activeOptimizations.has(optimizationName);
  }

  /**
   * 获取性能摘要
   * @returns {Object} 性能摘要
   */
  getPerformanceSummary() {
    return {
      level: this.performanceLevel,
      isLowEnd: this.isLowEnd,
      activeOptimizations: Array.from(this.activeOptimizations),
      scannerConfig: this.getScannerConfig(),
      uiConfig: this.getUIConfig()
    };
  }

  /**
   * 销毁优化器
   */
  destroy() {
    // 移除所有优化
    for (const [name, strategy] of this.optimizationStrategies) {
      if (this.activeOptimizations.has(name)) {
        try {
          strategy.remove();
        } catch (error) {
          console.warn(`移除优化策略失败: ${name}`, error);
        }
      }
    }

    // 清理资源
    this.disableAggressiveMemoryManagement();
    this.activeOptimizations.clear();
    this.isInitialized = false;
    
    console.log('性能优化器已销毁');
  }
}

// 创建全局实例
const performanceOptimizer = new PerformanceOptimizer();

/**
 * 初始化性能优化器
 * @returns {Promise<void>}
 */
export async function initializePerformanceOptimizer() {
  await performanceOptimizer.initialize();
}

/**
 * 获取扫码配置
 * @returns {Object|null} 扫码配置
 */
export function getScannerConfig() {
  return performanceOptimizer.getScannerConfig();
}

/**
 * 获取UI配置
 * @returns {Object|null} UI配置
 */
export function getUIConfig() {
  return performanceOptimizer.getUIConfig();
}

/**
 * 获取性能摘要
 * @returns {Object} 性能摘要
 */
export function getPerformanceSummary() {
  return performanceOptimizer.getPerformanceSummary();
}

/**
 * 检查优化是否激活
 * @param {string} optimizationName 优化名称
 * @returns {boolean} 是否激活
 */
export function isOptimizationActive(optimizationName) {
  return performanceOptimizer.isOptimizationActive(optimizationName);
}

export default performanceOptimizer;
