<template>
  <view class="forgot-password-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <view class="header">
      <view class="header-content">
        <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
        <text class="title">{{ t('user.forgotPassword') || 'Forgot Password' }}</text>
      </view>
    </view>

    <view class="description">
      {{ t('user.forgotPasswordDesc') || 'Enter your mobile phone number and we\'ll send you a code to reset your password.' }}
    </view>

    <view class="form-container">
      <text class="form-label">{{ t('user.phoneNumber') || 'Mobile Number' }}</text>
      <input 
        type="tel" 
        class="form-input" 
        v-model="phoneNumber"
        :placeholder="t('user.enterPhoneNumber') || 'Please enter your mobile number'"
        @input="validatePhoneNumber"
      />
      <text v-if="errors.phoneNumber" class="error-text">{{ errors.phoneNumber }}</text>

      <view class="button-group">
        <button class="reset-btn" @click="resetByCode" :disabled="!isFormValid">
          {{ t('user.resetByCode') || 'Reset by a code' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { sendSmsCode } from '@/api'

// 国际化
const { t } = useI18n()

// 状态栏高度
const statusBarHeight = ref(0)

// 表单数据
const phoneNumber = ref('')
const errors = ref({
  phoneNumber: ''
})

// 表单验证
const validatePhoneNumber = () => {
  const value = phoneNumber.value
  if (!value) {
    errors.value.phoneNumber = 'Phone number is required'
  } else if (!/^225\d{8,10}$/.test(value)) {
    errors.value.phoneNumber = 'Please enter a valid phone number (225 + 8-10 digits)'
  } else {
    errors.value.phoneNumber = ''
  }
}

// 表单是否有效
const isFormValid = computed(() => {
  return phoneNumber.value && !errors.value.phoneNumber
})

// 组件挂载时获取状态栏高度
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })
})

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 通过验证码重置密码
const resetByCode = async () => {
  // 验证手机号
  validatePhoneNumber()
  
  if (!isFormValid.value) {
    uni.showToast({
      title: 'Please enter a valid phone number',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: 'Sending...',
      mask: true
    })

    // 调用发送验证码API
    const response = await sendSmsCode(phoneNumber.value)
    
    uni.hideLoading()
    
    // 发送成功，跳转到验证码页面
    if (response && (response.code === 200 || response.code === "200")) {
      uni.showToast({
        title: 'Verification code sent',
        icon: 'success',
        duration: 2000
      })
      
      // 跳转到验证码页面，传递手机号参数
      setTimeout(() => {
        uni.navigateTo({
          url: `/pages/auth/verify-code/index?phoneNumber=${phoneNumber.value}&type=reset`
        })
      }, 1500)
    } else {
      throw new Error(response?.message || 'Failed to send verification code')
    }

  } catch (error) {
    uni.hideLoading()
    console.error('发送验证码失败:', error)
    
    let errorMessage = 'Failed to send verification code'
    if (error.response) {
      errorMessage = error.response.data?.message || errorMessage
    } else if (error.message) {
      errorMessage = error.message
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000
    })
  }
}
</script>

<style lang="less">
.forgot-password-container {
  min-height: 100vh;
  background-color: #fff;
  padding: 0 40rpx;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 32rpx 0;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;
  }

  .back-icon {
    position: absolute;
    left: 0;
    font-size: 32rpx;
    color: #000;
    font-weight: bold;
  }

  .title {
    font-size: 36rpx;
    color: #333;
    font-weight: 600;
  }

  .description {
    font-size: 28rpx;
    color: #999;
    line-height: 1.5;
    text-align: left;
    margin: 40rpx 0 48rpx 0;
  }

  .form-container {
    width: 100%;

    .form-label {
      font-size: 32rpx;
      color: #333;
      font-weight: 600;
      margin-bottom: 24rpx;
      display: block;
    }

    .form-input {
      width: 100%;
      height: 96rpx;
      background: #fff;
      border: 2rpx solid #eee;
      border-radius: 8rpx;
      padding: 0 32rpx;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 16rpx;
      box-sizing: border-box;

      &::placeholder {
        color: #999;
        font-size: 28rpx;
      }

      &:focus {
        border-color: #f23030;
      }
    }

    .error-text {
      color: #f23030;
      font-size: 24rpx;
      margin-bottom: 16rpx;
      display: block;
    }

    .button-group {
      display: flex;
      gap: 24rpx;
      
      .reset-btn {
        margin-top: 20rpx;
        flex: 1;
        height: 96rpx;
        background: #f23030;
        color: #fff;
        border-radius: 18rpx;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        border: none;

        &:disabled {
          background: #ccc;
          color: #999;
        }
      }
    }
  }
}
</style>