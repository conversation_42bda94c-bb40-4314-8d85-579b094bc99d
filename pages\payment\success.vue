<template>
  <view class="payment-success">
    <!-- 固定头部区域 -->
    <view class="fixed-header">
      <!-- 状态栏占位 -->
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <!-- 导航栏 -->
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back"></text>
          </view>
          <text class="title">{{ $t('payment.expenseSettlement') }}</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-scroll" :style="{ marginTop: (statusBarHeight + 88) + 'rpx' }">
      <view class="success-container">
        <!-- 成功图标 -->
        <view class="success-icon-container">
          <view class="success-icon">
            <view class="checkmark"></view>
          </view>
          <view class="decoration-circle circle1"></view>
          <view class="decoration-circle circle2"></view>
          <view class="decoration-x"></view>
        </view>
        
        <!-- 成功信息 -->
        <view class="success-info">
          <text class="success-text">{{ $t('payment.paymentSuccessful') }}</text>
          <view class="amount-container">
            <text class="amount">{{ amount }}</text>
            <text class="currency">F</text>
          </view>
        </view>

        <!-- 按钮区域 -->
        <view class="buttons-container">
          <button class="view-orders-btn" @click="viewOrders">{{ $t('payment.viewOrders') }}</button>
          <text class="return-home-btn" @click="returnHome">{{ $t('payment.returnToHomepage') }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getRouteParams } from '@/utils/route'
import { getOrderDetailPay } from '@/api/modules/charging'

// 响应式数据
const statusBarHeight = ref(0)
const amount = ref('0')

// 生命周期
onMounted(async () => {
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight

  try {
    // 从路由参数拿到 orderId
    let orderId = ''
    let orderNumber = ''

    const routeParams = getRouteParams(['orderId','orderNumber'])
    orderId = routeParams.orderId || ''
    orderNumber = routeParams.orderNumber || ''

    // 兜底：H5 进入参数
    if ((!orderId || !orderNumber) && uni.getEnterOptionsSync) {
      const q = uni.getEnterOptionsSync().query || {}
      orderId = orderId || q.orderId || ''
      orderNumber = orderNumber || q.orderNumber || ''
    }

    // 调用结算接口获取金额
    if (orderId) {
      try {
        const res = await getOrderDetailPay(orderId)
        if (res && res.code === 200 && res.data) {
          amount.value = res.data.actualPaymentPrice || res.data.orderPrice || '0'
          return
        }
      } catch (err) {
        console.error('getOrderDetailPay failed:', err)
      }
    }

    // 兜底：如果没有接口值，显示0或路由传参amount
    const query = uni.getEnterOptionsSync ? uni.getEnterOptionsSync().query : {}
    amount.value = query?.amount || amount.value || '0'
  } catch (e) {
    console.error('Error getting payment data:', e)
    amount.value = amount.value || '0'
  }
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const viewOrders = () => {
  // 跳转到订单详情页面
  uni.navigateTo({
    url: '/pages/payment/order-detail'
  })
}

const returnHome = () => {
  // 返回主页
  uni.reLaunch({
    url: '/pages/home/<USER>'
  })
}
</script>

<style lang="scss">
@import '@/static/iconfont/iconfont.css';

.payment-success {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0,0,0,0.04);
}

.status-bar {
  background-color: #fff;
  width: 100%;
}

.header {
  background: #fff;
  width: 100%;
  
  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .back-btn {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;
      
      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }
  }
}

.content-scroll {
  flex: 1;
  padding: 20rpx;
  background-color: #fff;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx;
}

.success-icon-container {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 60rpx;
  
  .success-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 180rpx;
    height: 180rpx;
    background-color: #ff4d4f;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .checkmark {
      width: 80rpx;
      height: 40rpx;
      border-bottom: 12rpx solid white;
      border-left: 12rpx solid white;
      transform: rotate(-45deg) translate(0, -10rpx);
    }
  }
  
  .decoration-circle {
    position: absolute;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background-color: #ff9500;
    
    &.circle1 {
      top: 0;
      right: 20rpx;
    }
    
    &.circle2 {
      bottom: 40rpx;
      left: 0;
    }
  }
  
  .decoration-x {
    position: absolute;
    top: 20rpx;
    left: 10rpx;
    width: 20rpx;
    height: 20rpx;
    
    &:before, &:after {
      content: '';
      position: absolute;
      width: 20rpx;
      height: 4rpx;
      background-color: #ff4d4f;
      top: 8rpx;
    }
    
    &:before {
      transform: rotate(45deg);
    }
    
    &:after {
      transform: rotate(-45deg);
    }
  }
}

.success-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 100rpx;
  
  .success-text {
    font-size: 48rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 40rpx;
  }
  
  .amount-container {
    display: flex;
    align-items: baseline;
    
    .amount {
      font-size: 72rpx;
      font-weight: bold;
      color: #333;
    }
    
    .currency {
      font-size: 36rpx;
      color: #666;
      margin-left: 16rpx;
    }
  }
}

.buttons-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .view-orders-btn {
    width: 100%;
    height: 96rpx;
    background-color: #ff4d4f;
    color: white;
    font-size: 36rpx;
    font-weight: 500;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40rpx;
    border: none;
  }
  
  .return-home-btn {
    font-size: 32rpx;
    color: #333;
    padding: 20rpx;
  }
}
</style> 