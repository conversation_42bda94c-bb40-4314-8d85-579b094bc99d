<template>
    <view class="scanner-container" :class="{ 'optimized': isLowEndDeviceFlag }">
        <!-- 扫描图标 - 充电时隐藏 -->
        <view
            v-if="!disabled"
            class="scan-icon"
            :class="getIconClasses()"
            @click="startChargingPileScan"
            @touchstart="handleTouchStart"
            @touchend="handleTouchEnd"
        >
            <image
                src="/static/images/export.png"
                mode="aspectFit"
                class="icon"
                :class="{ 'optimized-icon': isLowEndDeviceFlag }"
                loading="eager"
            />
        </view>
    </view>
</template>

<script setup>
import { ref, inject, watch, onUnmounted, getCurrentInstance, nextTick, onMounted } from 'vue';
import { scanChargingPile } from '@/utils/vehicle-scanner.js';
import { getPerformanceConfig, isLowEndDevice } from '@/utils/device-performance.js';
import { recordScannerClick, recordScannerResponse } from '@/utils/performance-monitor.js';
import {
    queryPlotVoByChargingId,
    extractSpearIdFromUrl,
    createChargingOrder,
    startCharging,
    stopCharging
} from '@/api';
import { useI18n } from 'vue-i18n';
import { useUserStore } from '@/store/user';

// 获取当前组件实例
const instance = getCurrentInstance();

// 使用国际化
const { t } = useI18n();

// 注入Toast实例
const toast = inject('toast');

// 使用用户状态store
const userStore = useUserStore();

// 组件是否已销毁的标志
const isDestroyed = ref(false);

// 定义props
const props = defineProps({
    // 是否禁用扫描
    disabled: {
        type: Boolean,
        default: false
    }
});

// 定义事件
const emit = defineEmits([
    'scan-success',    // 扫描成功
    'scan-fail',       // 扫描失败
    'charging-start',  // 开始充电
    'charging-test',   // 充电测试
    'charging-error',  // 充电错误
    'loading-change',  // loading状态改变
    'step-change',     // 充电步骤改变
    'model-type-change' // 模型类型改变
]);

// 状态管理
const scanSuccess = ref(false);
const showChargingLoading = ref(false);
const chargingStep = ref(0); // 0: 未开始, 1: 查询站点信息, 2: 创建订单, 3: 开始充电, 4: 充电完成
const orderInfo = ref(null);

// 性能优化相关状态
const isLowEndDeviceFlag = ref(false);
const performanceConfig = ref(null);
const enableAnimations = ref(true);
const enableHoverEffects = ref(true);

// 触摸反馈状态
const isTouching = ref(false);

/**
 * 初始化性能配置
 */
const initPerformanceConfig = async () => {
    try {
        isLowEndDeviceFlag.value = await isLowEndDevice();
        performanceConfig.value = await getPerformanceConfig();

        // 根据设备性能调整UI配置
        if (isLowEndDeviceFlag.value) {
            enableAnimations.value = false;
            enableHoverEffects.value = false;
            console.log('检测到低端设备，禁用动画和悬停效果');
        } else {
            enableAnimations.value = performanceConfig.value?.scanner?.enableAnimations ?? true;
            enableHoverEffects.value = performanceConfig.value?.scanner?.enableHoverEffects ?? true;
        }

        console.log('性能配置初始化完成:', {
            isLowEnd: isLowEndDeviceFlag.value,
            enableAnimations: enableAnimations.value,
            enableHoverEffects: enableHoverEffects.value
        });
    } catch (error) {
        console.warn('性能配置初始化失败，使用默认配置:', error);
        // 使用保守的默认配置
        enableAnimations.value = false;
        enableHoverEffects.value = false;
    }
};

/**
 * 安全的emit函数，检查组件是否已销毁
 */
const safeEmit = (eventName, ...args) => {
    try {
        console.log(`尝试触发事件: ${eventName}`, 'isDestroyed:', isDestroyed.value);
        if (!isDestroyed.value) {
            console.log(`成功触发事件: ${eventName}`);
            emit(eventName, ...args);
        } else {
            console.warn(`组件已销毁，无法触发事件: ${eventName}`);
        }
    } catch (error) {
        console.warn('Emit error:', error);
    }
};

/**
 * 安全的状态更新函数
 */
const safeUpdateState = (updateFn) => {
    try {
        if (!isDestroyed.value && instance) {
            updateFn();
        }
    } catch (error) {
        console.warn('State update error:', error);
    }
};

/**
 * 根据power值确定充电桩模型类型
 */
const getModelTypeByPower = (power) => {
    console.log('根据power值确定模型类型:', power);

    // 将power转换为数字进行比较
    const powerNum = parseInt(power) || 0;

    if (powerNum === 40) {
        console.log('power=40，使用default模型（GEN3APPFBX.fbx）');
        return 'default';
    } else if (powerNum === 22) {
        console.log('power=22，使用arnio模型（arnio.fbx）');
        return 'arnio';
    } else {
        console.log('power值不匹配，使用默认default模型（40对应的GEN3APPFBX.fbx）');
        return 'default'; // 默认显示40对应的充电桩
    }
};

/**
 * 检查充电桩状态是否可用
 */
const checkChargingPileStatus = (stationData) => {
    if (!stationData) return { available: false, reason: '站点信息为空' };

    // 检查充电桩状态
    if (stationData.status) {
        const status = stationData.status.toLowerCase();

        switch (status) {
            case 'charging':
            case 'occupied':
                return {
                    available: false,
                    reason: t('charging.unavailable') || 'Cette borne est en cours d\'utilisation, veuillez choisir une autre borne',
                    type: 'already_charging'
                };
            case 'offline':
            case 'fault':
            case 'error':
                return {
                    available: false,
                    reason: t('charging.unavailable') || 'Cette borne est hors ligne ou en panne, veuillez choisir une autre borne',
                    type: 'unavailable'
                };
            case 'maintenance':
                return {
                    available: false,
                    reason: t('charging.unavailable') || 'Cette borne est en maintenance, veuillez choisir une autre borne',
                    type: 'unavailable'
                };
            case 'available':
            case 'idle':
            case 'free':
                return { available: true };
            default:
                // 未知状态，允许尝试
                return { available: true };
        }
    }

    // 如果没有状态信息，默认允许尝试
    return { available: true };
};

/**
 * 查询充电桩站点信息
 */
const fetchStationInfo = async (spearId) => {
    try {
        // 移除loading提示
        safeUpdateState(() => {
            chargingStep.value = 1;
        });
        safeEmit('step-change', 1);

        console.log('查询站点信息，spearId:', spearId);

        const response = await queryPlotVoByChargingId(spearId);

        // 检查组件是否已销毁
        if (isDestroyed.value) {
            return null;
        }
        console.log('站点信息响应:', response);

        if (response && response.code === 200 && response.data) {
            // 检查必需的参数是否存在
            if (!response.data.chargingId || !response.data.spearId) {
                throw new Error('站点信息缺少必需参数：chargingId或spearId');
            }

            // 检查充电桩状态
            const statusCheck = checkChargingPileStatus(response.data);
            if (!statusCheck.available) {
                const error = new Error(statusCheck.reason);
                error.type = statusCheck.type;
                throw error;
            }

            // 根据power值确定模型类型并发出事件
            const power = response.data.power;
            const modelType = getModelTypeByPower(power);
            console.log('发出模型类型变更事件:', { power, modelType });
            safeEmit('model-type-change', { power, modelType });

            return response;
        } else {
            const errorMsg = response?.msg || '获取站点信息失败';

            if (errorMsg.includes('not.found') || errorMsg.includes('不存在')) {
                throw new Error(t('errors.notFound') || 'Borne de recharge introuvable, veuillez vérifier le code QR');
            } else if (errorMsg.includes('offline') || errorMsg.includes('离线')) {
                throw new Error(t('charging.unavailable') || 'Cette borne est hors ligne, veuillez choisir une autre borne');
            } else {
                throw new Error(errorMsg);
            }
        }
    } catch (error) {
        // 发送错误事件，保持弹窗打开
        safeEmit('charging-error', {
            error,
            keepPopupOpen: true,
            errorMessage: error.message || t('errors.loadFailed')
        });
        throw error;
    }
};

/**
 * 处理站点信息并开始充电流程
 */
const handleStationInfoAndStartCharging = async (spearId, plotInfo, scanData) => {
    try {
        scanSuccess.value = true;
        showChargingLoading.value = true;

        // 触发扫描成功事件
        safeEmit('scan-success', { scanData, spearId });

        // 创建订单并开始充电
        await createOrder(plotInfo);

    } catch (error) {
        showChargingLoading.value = false;
        chargingStep.value = 0;
        safeEmit('step-change', 0);
        safeEmit('charging-error', { error });
        throw error;
    }
};

/**
 * 启动充电桩扫描功能（优化版本）
 */
const startChargingPileScan = () => {
    // 检查是否禁用
    if (props.disabled) {
        console.log('Scan function is disabled');
        return;
    }

    console.log('Click scan icon, start charging pile scan');

    // 使用 requestAnimationFrame 确保在下一帧执行，避免阻塞UI
    if (typeof requestAnimationFrame !== 'undefined') {
        requestAnimationFrame(() => {
            startScanProcess();
        });
    } else {
        // 降级到 setTimeout，但使用最小延迟
        setTimeout(() => {
            startScanProcess();
        }, 0);
    }
};

/**
 * 实际启动扫码流程
 */
const startScanProcess = () => {
    try {
        // 直接启动扫码，不显示额外提示
        scanChargingPile({
            showTips: false,
            success: (result) => {
                console.log('Scan successful, processing result');
                handleScanSuccess(result);
            },
            fail: (error) => {
                console.log('Scan failed:', error);
                handleScanFail(error);
            }
        });
    } catch (error) {
        console.error('启动扫码失败:', error);
    }
};

/**
 * 处理扫描成功（极速优化版本）
 */
const handleScanSuccess = (result) => {
    // 清理可能存在的定时器
    clearProcessingTimers();

    try {
        console.log('Scan successful, processing result:', result);

        // 解析扫描结果
        let scanData = result.result || result.text || result.data || '';

        if (!scanData) {
            return;
        }

        console.log('Scan content:', scanData);

        // 根据设备性能选择处理策略
        if (isLowEndDeviceFlag.value) {
            // 低端设备：极速模式，直接处理，无延迟
            processChargingPileQRCode(scanData);
        } else {
            // 高端设备：快速模式，最小延迟
            // 使用 requestIdleCallback 或最小延迟
            if (typeof requestIdleCallback !== 'undefined') {
                requestIdleCallback(() => {
                    processChargingPileQRCode(scanData);
                }, { timeout: 100 });
            } else {
                // 最小延迟处理
                setTimeout(() => {
                    processChargingPileQRCode(scanData);
                }, 50);
            }
        }
    } catch (error) {
        showChargingLoading.value = false;
        scanSuccess.value = false;
        safeEmit('scan-fail', { error });
    }
};

// 定时器管理
let processingTimers = [];

/**
 * 清理处理过程中的定时器
 */
const clearProcessingTimers = () => {
    processingTimers.forEach(timer => {
        if (timer) {
            clearTimeout(timer);
        }
    });
    processingTimers = [];
};

/**
 * 处理扫描失败
 */
const handleScanFail = (error) => {
    // 用户取消扫描不显示错误提示
    if (error && error.message && error.message.includes('cancel')) {
        return;
    }

    safeEmit('scan-fail', { error });
};

/**
 * 获取图标样式类
 */
const getIconClasses = () => {
    const classes = [];

    if (scanSuccess.value) classes.push('scan-success');
    if (isLowEndDeviceFlag.value) classes.push('low-end-device');
    if (!enableAnimations.value) classes.push('no-animations');
    if (!enableHoverEffects.value) classes.push('no-hover');
    if (isTouching.value) classes.push('touching');

    return classes.join(' ');
};

/**
 * 处理触摸开始
 */
const handleTouchStart = () => {
    if (!isLowEndDeviceFlag.value) {
        isTouching.value = true;
    }
};

/**
 * 处理触摸结束
 */
const handleTouchEnd = () => {
    if (!isLowEndDeviceFlag.value) {
        // 延迟重置，提供视觉反馈
        setTimeout(() => {
            isTouching.value = false;
        }, 150);
    }
};

/**
 * 处理充电桩二维码
 */
const processChargingPileQRCode = async (scanData) => {
    try {
        let spearId = null;

        // 处理URL格式的二维码（统一使用extractSpearIdFromUrl函数）
        if (scanData.includes('code=') && (scanData.startsWith('http://') || scanData.startsWith('https://'))) {
            // 处理所有URL格式的二维码，包括：
            // 1. https://zxsc.94lihai.com?code=1933714558320254977-1935150900858314753
            // 2. http://185.166.39.76:8080?code=1950033726313025538-1950040901626318849
            console.log('检测到URL格式二维码，开始解析:', scanData);
            spearId = extractSpearIdFromUrl(scanData);
            console.log('解析结果 spearId:', spearId);

            if (!spearId) {
                throw new Error(t('toast.invalidQRCode'));
            }
        } else {
            // 处理JSON格式的二维码
            try {
                const qrData = JSON.parse(scanData);
                spearId = qrData.spearId;

                if (!spearId) {
                    throw new Error(t('toast.missingChargingPileId'));
                }
            } catch (e) {
                throw new Error(t('toast.unrecognizedQRFormat'));
            }
        }

        // 统一处理流程：查询站点信息 -> 创建订单 -> 开始充电
        if (spearId) {
            try {
                // 设置扫描成功状态并通知父组件显示弹窗
                scanSuccess.value = true;
                showChargingLoading.value = true;
                safeEmit('scan-success', { scanData, spearId });

                // 更新步骤到1（查询站点信息）
                chargingStep.value = 1;
                safeEmit('step-change', 1);

                // 查询站点信息
                const stationInfo = await fetchStationInfo(spearId);

                if (stationInfo && stationInfo.data) {
                    try {
                        console.log('=== 准备创建订单并开始充电 ===');
                        // 创建订单并开始充电
                        const createOrderResult = await createOrder(stationInfo.data);
                        
                        // 检查是否是待支付订单跳转的情况
                        if (createOrderResult && createOrderResult.navigated) {
                            console.log('=== 检测到待支付订单，已跳转到订单详情页 ===');
                            return; // 直接返回，不继续执行
                        }
                        
                        console.log('=== 创建订单并开始充电完成 ===');
                        console.log('2222222222222222222221111111111111111111111111111111111111111111111111111111111111');

                    } catch (orderError) {
                        console.log('1111111111111111111111111111111111111111111111111111111111111');
                        console.error('=== 创建订单或开始充电失败 ===');
                        console.error('错误详情:', orderError);
                        console.error('错误消息:', orderError.message);
                        console.error('错误堆栈:', orderError.stack);
                        
                        // 检查是否是待支付订单错误
                        if (orderError.type === 'pending_payment' && orderError.orderNumber) {
                            console.log('=== 检测到待支付订单错误，添加支付按钮 ===');
                            // 保持弹窗显示，但是添加支付选项
                            safeEmit('charging-error', {
                                error: orderError,
                                keepPopupOpen: true,
                                errorMessage: orderError.message || t('toast.orderCreationFailed') || 'Échec de la création de commande',
                                // 添加待支付订单的特殊信息
                                isPendingPayment: true,
                                orderNumber: orderError.orderNumber,
                                showPaymentButton: true
                            });
                        } else {
                            // 普通错误处理
                            safeEmit('charging-error', {
                                error: orderError,
                                keepPopupOpen: true,
                                errorMessage: orderError.message || t('toast.orderCreationFailed') || 'Échec de la création de commande'
                            });
                        }

                        // 不重置chargingStep，让弹窗继续显示错误信息
                        return;
                    }
                } else {
                    throw new Error(t('errors.notFound') || 'Informations de borne de recharge introuvables');
                }
            } catch (apiError) {
                console.error('API调用失败:', apiError);

                // 保持弹窗显示，但是更新状态
                safeEmit('charging-error', {
                    error: apiError,
                    keepPopupOpen: true,
                    errorMessage: apiError.message || t('toast.getChargingPileInfoFailed') || 'Échec de l\'obtention des informations de borne de recharge'
                });

                // 不重置chargingStep，让弹窗继续显示错误信息
                return;
            }
        }
    } catch (error) {
        console.error('Failed to process QR code:', error);

        // 设置扫描成功状态并通知父组件显示弹窗
        scanSuccess.value = true;
        showChargingLoading.value = true;

        // 更新步骤到1（查询站点信息）
        chargingStep.value = 1;
        safeEmit('step-change', 1);

        // 发送错误事件，但指示保持弹窗打开
        safeEmit('charging-error', {
            error,
            scanData,
            keepPopupOpen: true,
            errorMessage: error.message || t('errors.processingFailed')
        });
    }
};

// 监听状态变化
watch(showChargingLoading, (newVal) => {
    safeEmit('loading-change', newVal);
});
watch(chargingStep, (newVal) => {
    safeEmit('step-change', newVal);
});

// 组件挂载时初始化性能配置
onMounted(() => {
    initPerformanceConfig();
});

// 组件销毁时的清理
onUnmounted(() => {
    isDestroyed.value = true;

    // 清理所有定时器
    clearProcessingTimers();

    // 清理loading状态
    try {
        uni.hideLoading();
        uni.hideToast();
    } catch (e) {
        // 忽略错误
    }

    // 清理性能配置缓存
    performanceConfig.value = null;

    // 重置状态
    scanSuccess.value = false;
    showChargingLoading.value = false;
    chargingStep.value = 0;
    orderInfo.value = null;

    // 清理内存引用
    isLowEndDeviceFlag.value = false;
    enableAnimations.value = true;
    enableHoverEffects.value = true;

    console.log('ChargingPileScanner组件已清理完成');
});

/**
 * 创建充电订单
 */
const createOrder = async (stationInfo) => {
    try {
        // 移除loading提示
        chargingStep.value = 2;
        safeEmit('step-change', 2);

        // 严格按照order.md中的参数格式，只使用必需的参数
        const params = {
            plotId: stationInfo.plotId,
            chargingId: stationInfo.chargingId,
            isAutoPay: false,
            isRefund: false,
            spearId: stationInfo.spearId,
            orderType: "0",
            isStopWhenFull: false,
        };

        console.log('创建订单参数:', params);
        // 创建订单
        let response;
        try {
            response = await createChargingOrder(params);
        } catch (apiError) {
            console.log('=== createChargingOrder API调用异常 ===');
            console.log('异常详情:', apiError);
            console.log('异常message:', apiError.message);
            console.log('异常responseData:', apiError.responseData);
            
            // 检查是否是待支付订单的情况
            // 使用新的responseData属性来获取原始响应数据
            const responseData = apiError.responseData;
            if (responseData && responseData.code === 501 && responseData.data && responseData.data.orderState === '2') {
                console.log('222222222222222222222222222222222');
                console.log('=== 从异常中检测到待支付订单 ===');
                console.log('待支付订单信息:', responseData.data);
                console.log('订单ID:', responseData.data.orderId);
                console.log('订单号:', responseData.data.orderNumber);
                
                // 获取订单ID（用于跳转）
                const orderId = responseData.data.orderId;
                
                // 创建带有支付选项的错误对象
                const pendingPaymentError = new Error(responseData?.msg || 'You have a pending payment order, please complete payment before starting again');
                pendingPaymentError.type = 'pending_payment';
                pendingPaymentError.orderNumber = orderId; // 将订单ID附加到错误对象上（用于跳转）
                pendingPaymentError.responseData = responseData; // 保留完整响应数据
                
                throw pendingPaymentError;
            }
            
            // 如果不是待支付订单的情况，重新抛出异常
            throw apiError;
        }
        console.log('创建订单响应:', response);
        console.log('响应状态码:', response?.code);
        console.log('响应消息:', response?.msg);
        console.log('响应数据:', response?.data);
        // 移除loading隐藏

        if (response && response.code === 200 && response.data) {
            console.log('=== 订单创建成功，准备开始充电 ===');
            orderInfo.value = response.data;
            // 检查是否有orderNum
            if (response.data.orderNumber) {
                console.log('订单号:', response.data.orderNumber);
                console.log('=== 调用 startChargingProcess ===');
                return await startChargingProcess(response.data.orderNumber);
            } else {
                console.error('订单创建成功但未返回订单号，响应数据:', response.data);
                throw new Error(t('toast.orderNumberMissing') || 'Commande créée avec succès mais numéro de commande manquant');
            }
        } else {
            // 处理特定的错误情况
            const errorMsg = response?.msg || t('toast.orderCreationFailed') || 'Échec de la création de commande';
            console.log('处理错误消息:', errorMsg);

            // 创建错误对象，包含类型信息
            let error;

            if (errorMsg.includes('order.is.charging') ||
                errorMsg.includes('charging') ||
                errorMsg.includes('正在充电') ||
                errorMsg.includes('已在充电')) {
                error = new Error(t('charging.unavailable') || 'Cette borne est en cours de charge, veuillez choisir une autre borne ou réessayer plus tard');
                error.type = 'already_charging';
            } else if (errorMsg.includes('spear.not.available') ||
                errorMsg.includes('not.available') ||
                errorMsg.includes('不可用') ||
                errorMsg.includes('离线') ||
                errorMsg.includes('故障')) {
                error = new Error(t('charging.unavailable') || 'Cette borne n\'est pas disponible, veuillez choisir une autre borne');
                error.type = 'unavailable';
            } else if (errorMsg.includes('insufficient.balance') ||
                errorMsg.includes('balance') ||
                errorMsg.includes('余额不足') ||
                errorMsg.includes('余额') ||
                errorMsg.includes('account.below.amount')) {
                error = new Error(t('account.insufficientBalance') || 'Solde insuffisant, veuillez recharger d\'abord');
                error.type = 'insufficient_balance';
            } else {
                error = new Error(errorMsg);
                error.type = 'unknown';
            }

            throw error;
        }
    } catch (error) {
        // 不重置chargingStep，保持弹窗打开显示错误
        safeUpdateState(() => {
            showChargingLoading.value = false;
        });

        const errorType = error.type || 'unknown';
        console.log('错误类型:', errorType, '错误消息:', error.message);

        // 发送错误事件，包含错误类型，保持弹窗打开
        safeEmit('charging-error', {
            error,
            type: errorType,
            message: error.message,
            keepPopupOpen: true,
            errorMessage: error.message || t('toast.orderCreationFailed') || 'Échec de la création de commande'
        });

        throw error;
    }
};

/**
 * 开始充电流程
 */
const startChargingProcess = async (orderNum) => {
    try {
        if (!orderNum) {
            throw new Error(t('toast.orderNumberMissing') || 'Numéro de commande vide, impossible de commencer la charge');
        }
        // 移除loading提示
        chargingStep.value = 3;
        safeEmit('step-change', 3);

        console.log('=== 开始调用startCharging API ===');
        console.log('开始充电，订单号:', orderNum);
        // 调用开始充电接口
        const response = await startCharging(orderNum);
        // 移除loading隐藏

        console.log('=== startCharging API 响应 ===');
        console.log('开始充电响应:', response);
        console.log('响应代码:', response?.code);
        console.log('响应消息:', response?.msg);

        if (response && response.code === 200) {
            console.log('=== API 调用成功，准备触发事件 ===');

            // 使用nextTick确保事件在下一个事件循环中触发
            console.log('准备触发charging-start事件，订单号:', orderNum);
            const eventData = {
                orderInfo: orderInfo.value,
                orderNumber: orderNum,
                pileInfo: {
                    id: orderInfo.value?.spearId || 'unknown',
                    name: 'Charging Pile',
                    location: 'Unknown Location',
                    plotId: orderInfo.value?.plotId || null
                },
                response: response.data
            };
            console.log('charging-start事件数据:', eventData);

            nextTick(() => {
                console.log('在nextTick中触发charging-start事件');
                emit('charging-start', eventData);
            });
            emit('charging-test','111111111111')
            // 然后更新状态和触发step-change
            chargingStep.value = 4;
            showChargingLoading.value = false;
            emit('step-change', 4);

            // 移除toast提示，直接返回响应
            return response;
        } else {
            console.log('=== API 调用失败 ===');
            console.log('失败响应:', response);
            throw new Error(response?.msg || t('charging.startFailed'));
        }
    } catch (error) {
        // 不重置chargingStep，保持弹窗打开显示错误
        showChargingLoading.value = false;

        // 发送错误事件，保持弹窗打开
        safeEmit('charging-error', {
            error,
            keepPopupOpen: true,
            errorMessage: error.message || t('charging.startFailed')
        });

        throw error;
    }
};

// 暴露组件方法
defineExpose({
    startChargingPileScan,
    scanSuccess,
    showChargingLoading
});
</script>

<style lang="less" scoped>
.scanner-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    // 优化版本的容器样式
    &.optimized {
        // 强制使用GPU加速
        transform: translateZ(0);
        backface-visibility: hidden;
        // 减少重绘
        will-change: auto;
    }
}

.scan-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;

    // 基础样式，避免复杂的嵌套
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;

    // 默认过渡效果（仅在非低端设备上）
    transition: transform 0.2s ease, opacity 0.2s ease;

    // 伪元素边框效果
    &::before {
        content: '';
        position: absolute;
        top: -8rpx;
        left: -8rpx;
        right: -8rpx;
        bottom: -8rpx;
        border: 2rpx solid rgba(0, 122, 255, 0.3);
        border-radius: 50%;
        opacity: 0;
        transform: scale(0.8);
        transition: opacity 0.2s ease, transform 0.2s ease;
        pointer-events: none;
    }

    // 悬停效果（仅在支持的设备上启用）
    &:not(.no-hover):hover::before {
        opacity: 1;
        transform: scale(1);
    }

    // 触摸反馈（替代复杂的active效果）
    &.touching {
        transform: scale(0.95);
        opacity: 0.8;
    }

    // 点击效果（简化版本）
    &:active {
        transform: scale(0.9);
    }

    // 扫描成功状态
    &.scan-success {
        &::before {
            border-color: rgba(0, 200, 81, 0.6);
            opacity: 1;
            transform: scale(1);
            background-color: rgba(0, 200, 81, 0.1);
        }

        .icon {
            // 简化滤镜效果，减少GPU负担
            opacity: 0.9;
        }

        // 成功动画（仅在支持动画的设备上启用）
        &:not(.no-animations) {
            animation: successPulse 1.5s ease-in-out 3; // 减少动画时长和次数
        }
    }

    // 低端设备优化样式
    &.low-end-device {
        // 完全禁用变换和滤镜
        transform: none !important;
        transition: none !important;

        &:active, &.touching {
            transform: none !important;
            opacity: 0.7 !important;
        }

        &.scan-success .icon {
            filter: none !important;
            opacity: 0.8 !important;
        }

        &::before {
            display: none !important; // 完全移除伪元素以提升性能
        }
    }

    // 禁用动画的样式
    &.no-animations {
        transition: none !important;
        animation: none !important;

        &::before {
            transition: none !important;
        }

        .icon {
            transition: none !important;
        }
    }

    // 禁用悬停效果的样式
    &.no-hover {
        &:hover::before {
            opacity: 0 !important;
            transform: scale(0.8) !important;
        }
    }
}

.icon {
    width: 40rpx;
    height: 40rpx;
    // 强制硬件加速
    transform: translateZ(0);
    backface-visibility: hidden;
    // 优化图片渲染
    image-rendering: -webkit-optimize-contrast;

    // 低端设备优化版本
    &.optimized-icon {
        // 禁用所有可能的性能消耗特性
        transform: none !important;
        filter: none !important;
        transition: none !important;
        // 使用更快的图片渲染
        image-rendering: pixelated;
    }
}

// 成功脉冲动画（仅在非低端设备上使用）
@keyframes successPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 200, 81, 0.4);
    }

    50% {
        box-shadow: 0 0 0 8rpx rgba(0, 200, 81, 0.1);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(0, 200, 81, 0);
    }
}

// 低端设备专用的简化样式
.low-end-device {
    // 强制禁用所有可能消耗性能的CSS特性
    * {
        will-change: auto !important;
        transform: none !important;
        filter: none !important;
        box-shadow: none !important;
        text-shadow: none !important;
        animation: none !important;
        transition: none !important;
    }
}
</style>