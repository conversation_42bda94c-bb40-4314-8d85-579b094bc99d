{
	"pages": [
		{
			"path": "pages/welcome/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white",
				"backgroundColor": "#000000"
			}
		},
		{
			"path": "pages/personal-center/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/edit-profile",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/account-security",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/login-password",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/payment-settings",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/payment-password",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},

		{
			"path": "pages/personal-center/payment-password-reset",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/message-settings",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/privacy",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/privacy-policy",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/terms-of-use",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/trading-rules",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/about-us",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/personal-center/problem-feedback",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/coupon/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/vip/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/charging-status/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTextStyle": "black",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/map/index",
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-right",
					"animationDuration": 300
				}
			}
		},
		{
			"path": "pages/search/index",
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-right",
					"animationDuration": 300
				}
			}
		},
		{
			"path": "pages/station-list/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Station List"
			}
		},
		{
			"path": "pages/station-detail/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Station Detail"
			}
		},
		{
			"path": "pages/filter/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Filter"
			}
		},
		{
			"path": "pages/account/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Account",
				"app-plus": {
					"titleNView": false,
					"animationType": "fade-in",
					"animationDuration": 300
				}
			}
		},
		{
			"path": "pages/more/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "More",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-left",
					"animationDuration": 300
				}
			}
		},
		{
			"path": "pages/account/recharge/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Add money"
			}
		},
		{
			"path": "pages/account/recharge/success",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Payment Successful"
			}
		},
		{
			"path": "pages/my-car/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Ma voiture",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-right",
					"animationDuration": 300
				}
			}
		},
		{
			"path": "pages/add-car/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "Add a Car",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-right",
					"animationDuration": 300
				}
			}
		}
	],
	"subPackages": [
		{
			"root": "pages/auth",
			"pages": [
				{
					"path": "login/index",
					"style": {
						"navigationBarTitleText": "Login",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "register/index",
					"style": {
						"navigationBarTitleText": "Register",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "forgot-password/index",
					"style": {
						"navigationBarTitleText": "Forgot Password",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "verify-code/index",
					"style": {
						"navigationBarTitleText": "Verification",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "reset-password/index",
					"style": {
						"navigationBarTitleText": "Reset Password",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "pin-login/index",
					"style": {
						"navigationBarTitleText": "Quick Login",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "register/phone",
					"style": {
						"navigationBarTitleText": "Phone Number",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "register/verification",
					"style": {
						"navigationBarTitleText": "Verification",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "register/name",
					"style": {
						"navigationBarTitleText": "Name",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "register/password",
					"style": {
						"navigationBarTitleText": "Password",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "register/confirm",
					"style": {
						"navigationBarTitleText": "Confirm",
						"navigationStyle": "custom"
					}
				}
			]
		},
		{
			"root": "pages/payment",
			"pages": [
				{
					"path": "expense-settlement/index",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "Expense Settlement"
					}
				},
				{
					"path": "password",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "Payment Password"
					}
				},
				{
					"path": "success",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "Payment Successful"
					}
				},
				{
					"path": "order-detail",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "Order Details"
					}
				},
				{
					"path": "order-pending",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": "Order Details"
					}
				}
			]
		},
		{
			"root": "pages/account",
			"pages": [
				
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "ARNIO",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF"
	},
	"uniIdRouter": {},
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "", //模式名称
				"path": "", //启动页面，必选
				"query": "" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}
}
