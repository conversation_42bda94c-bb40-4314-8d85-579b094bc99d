<template>
  <view class="password-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
        <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题文本 -->
      <view class="title-section">
        <text class="main-title">{{ $t('auth.setNewPassword') }}</text>
      </view>

      <!-- 密码输入 -->
      <view class="password-input-section">
        <view class="password-dots">
          <view 
            v-for="(dot, index) in passwordDots" 
            :key="index"
            class="password-dot"
            :class="{ 
              filled: dot.filled, 
              active: activeIndex === index,
              success: password.length === 4 && !hasError
            }"
          >
            <view v-if="dot.filled" class="dot-inner"></view>
          </view>
        </view>

        <text v-if="hasError" class="error-text">{{ errorMessage }}</text>
        <text v-else class="hint-text">{{ $t('auth.enter4DigitPassword') }}</text>
      </view>

      <!-- 数字键盘 -->
      <view class="keypad-section">
        <view class="keypad">
          <view 
            v-for="key in keypadNumbers" 
            :key="key"
            class="keypad-btn"
            :class="{ number: key !== 'delete' && key !== 'biometric' }"
            @click="onKeypadPress(key)"
          >
            <text v-if="key === 'delete'" class="iconfont icon-delete keypad-icon"></text>
            <text v-else-if="key === 'biometric'" class="iconfont icon-fingerprint keypad-icon"></text>
            <text v-else class="keypad-text">{{ key }}</text>
          </view>
        </view>
      </view>

      <!-- 密码提示 -->
      <view class="tips-section">
        <text class="tips-title">{{ $t('auth.passwordTips') }}</text>
        <view class="tips-list">
          <text class="tip-item">• {{ $t('auth.passwordTip1') }}</text>
          <text class="tip-item">• {{ $t('auth.passwordTip2') }}</text>
          <text class="tip-item">• {{ $t('auth.passwordTip3') }}</text>
        </view>
      </view>
    </view>

    <!-- 全局HUD -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { useGlobalHud } from '@/composables/useHud'
import GlobalHUD from '@/components/common/GlobalHUD.vue'

// 国际化
const { t: $t } = useI18n()

// 全局HUD
const hud = useGlobalHud()

// 状态栏高度
const statusBarHeight = ref(0)

// 注册数据
const registerData = ref({})

// 密码相关
const password = ref('')
const activeIndex = ref(-1)
const hasError = ref(false)
const errorMessage = ref('')

// 数字键盘布局
const keypadNumbers = [
  '1', '2', '3',
  '4', '5', '6', 
  '7', '8', '9',
  'biometric', '0', 'delete'
]

// 密码点状态
const passwordDots = computed(() => {
  return Array.from({ length: 4 }, (_, index) => ({
    filled: index < password.value.length
  }))
})

// 监听密码变化
watch(password, (newPassword) => {
  if (newPassword.length === 4) {
    // 密码输入完整，延迟跳转
    setTimeout(() => {
      continueToConfirm()
    }, 500)
  }
  
  // 清除错误状态
  if (hasError.value) {
    hasError.value = false
    errorMessage.value = ''
  }
})

// 键盘按键处理
const onKeypadPress = (key) => {
  if (key === 'delete') {
    // 删除最后一位
    if (password.value.length > 0) {
      password.value = password.value.slice(0, -1)
      activeIndex.value = password.value.length
    }
  } else if (key === 'biometric') {
    // 生物识别（暂不实现）
    hud.info($t('auth.biometricNotAvailable'))
  } else if (typeof key === 'string' && /^\d$/.test(key)) {
    // 数字输入
    if (password.value.length < 4) {
      password.value += key
      activeIndex.value = password.value.length - 1
      
      // 短暂显示活动状态
      setTimeout(() => {
        activeIndex.value = -1
      }, 200)
    }
  }
}

// 验证密码
const validatePassword = () => {
  const pwd = password.value
  
  if (pwd.length !== 4) {
    errorMessage.value = $t('auth.passwordMust4Digits')
    hasError.value = true
    return false
  }
  
  // 检查是否为简单序列
  if (pwd === '1234' || pwd === '0000' || pwd === '1111' || 
      pwd === '2222' || pwd === '3333' || pwd === '4444' || 
      pwd === '5555' || pwd === '6666' || pwd === '7777' || 
      pwd === '8888' || pwd === '9999' || pwd === '4321') {
    errorMessage.value = $t('auth.passwordTooSimple')
    hasError.value = true
    return false
  }
  
  return true
}

// 继续到确认密码页面
const continueToConfirm = () => {
  if (!validatePassword()) {
    // 清空密码重新输入
    setTimeout(() => {
      password.value = ''
    }, 1500)
    return
  }

  // 更新注册数据
  const updatedData = {
    ...registerData.value,
    password: password.value,
    step: 4
  }
  
  uni.setStorageSync('registerData', JSON.stringify(updatedData))
  
  // 跳转到确认密码页面
  uni.navigateTo({
    url: '/pages/auth/register/confirm'
  })
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 组件挂载
onMounted(() => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })

  // 获取注册数据
  try {
    const data = uni.getStorageSync('registerData')
    if (data) {
      registerData.value = JSON.parse(data)
    }
  } catch (error) {
    console.error('获取注册数据失败:', error)
    // 如果没有注册数据，返回上一页
    uni.navigateBack()
  }
})
</script>

<style lang="less">
.password-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 40rpx;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    height: 88rpx;

    .back-icon {
      font-size: 32rpx;
      color: #000;
      font-weight: bold;
      padding: 20rpx;
      margin-left: -20rpx;
    }
  }

  .main-content {
    flex: 1;
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;

    .title-section {
      text-align: center;
      margin: 60rpx 0 80rpx;

      .main-title {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.3;
      }
    }

    .password-input-section {
      text-align: center;
      margin-bottom: 80rpx;

      .password-dots {
        display: flex;
        justify-content: center;
        gap: 32rpx;
        margin-bottom: 40rpx;

        .password-dot {
          width: 80rpx;
          height: 80rpx;
          border: 3rpx solid #E5E5E5;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &.active {
            border-color: #f23030;
            transform: scale(1.1);
          }

          &.filled {
            border-color: #f23030;
            background: rgba(242, 48, 48, 0.1);

            .dot-inner {
              width: 32rpx;
              height: 32rpx;
              background: #f23030;
              border-radius: 50%;
            }
          }

          &.success {
            border-color: #f23030;
            background: rgba(242, 48, 48, 0.1);

            .dot-inner {
              background: #f23030;
            }
          }
        }
      }

      .error-text {
        color: #f23030;
        font-size: 28rpx;
      }

      .hint-text {
        color: #666;
        font-size: 28rpx;
      }
    }

    .keypad-section {
      margin-bottom: 60rpx;

      .keypad {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32rpx;
        max-width: 600rpx;
        margin: 0 auto;

        .keypad-btn {
          height: 120rpx;
          background: #F8F9FA;
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          border: 2rpx solid transparent;

          &:active {
            background: #E9ECEF;
            transform: scale(0.95);
          }

          &.number {
            &:active {
              background: rgba(242, 48, 48, 0.1);
              border-color: #f23030;
            }
          }

          .keypad-text {
            font-size: 48rpx;
            font-weight: 500;
            color: #333;
          }

          .keypad-icon {
            font-size: 32rpx;
            color: #666;
          }
        }
      }
    }

    .tips-section {
      background: #F8F9FA;
      padding: 32rpx;
      border-radius: 16rpx;

      .tips-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
      }

      .tips-list {
        .tip-item {
          display: block;
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
          margin-bottom: 12rpx;
        }
      }
    }
  }
}
</style>
