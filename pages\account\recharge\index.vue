<template>
  <view class="recharge-page">
    <!-- 顶部状态栏和返回 -->
    <view class="fixed-header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="goBack">
            <text class="iconfont icon-back"></text>
          </view>
          <text class="title">{{ t('account.addMoney') }}</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 88) + 'px', marginTop: '-40rpx' }">
      <!-- 支付方式选择 -->
      <view class="section-title">
        <text>{{ t('payment.choosePaymentMethod') }}</text>
      </view>

      <view class="payment-methods">
        <view class="payment-method" :class="{ active: selectedPaymentMethod === 'orange' }"
          @click="selectPaymentMethod('orange')">
          <image src="/static/images/orange.png" class="payment-icon"></image>
          <text class="payment-name">Orange</text>
        </view>
        <view class="payment-method" :class="{ active: selectedPaymentMethod === 'wave' }"
          @click="selectPaymentMethod('wave')">
          <image src="/static/images/wave.png" class="payment-icon"></image>
          <text class="payment-name">Wave</text>
        </view>
        <view class="payment-method" :class="{ active: selectedPaymentMethod === 'moov' }"
          @click="selectPaymentMethod('moov')">
          <image src="/static/images/coupon-icon.png" class="payment-icon"></image>
          <text class="payment-name">Moov</text>
        </view>
        <view class="payment-method" :class="{ active: selectedPaymentMethod === 'mtn' }"
          @click="selectPaymentMethod('mtn')">
          <image src="/static/images/points-coin-small.png" class="payment-icon"></image>
          <text class="payment-name">MTN</text>
        </view>
      </view>

      <!-- 金额选择 -->
      <view class="section-title">
        <text>Choisissez un pack:</text>
      </view>

      <view class="amount-packs">
        <view v-for="(amount, index) in amountOptions" :key="index" class="amount-pack" :class="{
          active: selectedAmount === amount.value

        }" @click="selectAmount(amount.value)">
          <text class="amount-value" :class="{ blue: selectedAmount === amount.value }">{{ amount.label }}</text>
        </view>
      </view>

      <!-- 电话号码输入 -->
      <view class="section-title">
        <text>Votre numero de telephone:</text>
      </view>

      <view class="phone-input-container">
        <input type="tel" pattern="[0-9]*" inputmode="numeric" v-model="phoneNumber" class="phone-input"
          placeholder="+225 0554954287" ref="phoneInputRef" @focus="handleInputFocus" @blur="handleInputBlur"
          :adjust-position="false" :cursor-spacing="0" :hold-keyboard="true" :confirm-hold="true" />
      </view>

      <!-- 底部留空，确保内容不被按钮遮挡 -->
      <view style="height: 180rpx;"></view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button class="submit-btn" @click="handleSubmit" :disabled="!isFormValid">Valider</button>
    </view>

    <!-- 加载遮罩层 -->
    <view class="loading-overlay" v-if="isLoading">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">{{ t('payment.processing') }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from '@/composables/useI18n.js'

// 国际化
const { t } = useI18n()

// 状态变量
const statusBarHeight = ref(20) // 默认状态栏高度
const selectedPaymentMethod = ref('orange')
const selectedAmount = ref(100) // 默认选中100 F
const phoneNumber = ref('+225 0554954287')
const isLoading = ref(false) // 加载状态
const phoneInputRef = ref(null) // 电话输入框引用

// 金额选项数据
const amountOptions = [
  { value: 100, label: '100 F', isDefault: false, hasRedBorder: false },
  { value: 1000, label: '1000 F', isDefault: false, hasRedBorder: false },
  { value: 2000, label: '2000 F', isDefault: false, hasRedBorder: false },
  { value: 5000, label: '5000 F', isDefault: false, hasRedBorder: false },
  { value: 10000, label: '10000 F', isDefault: false, hasRedBorder: true }
]

// 计算属性
const isFormValid = computed(() => {
  return selectedPaymentMethod.value &&
    selectedAmount.value &&
    phoneNumber.value &&
    phoneNumber.value.length > 8
})

// 页面加载时获取状态栏高度
uni.getSystemInfo({
  success: (res) => {
    statusBarHeight.value = res.statusBarHeight
  }
})

// 选择支付方式
const selectPaymentMethod = (method) => {
  selectedPaymentMethod.value = method
}

// 选择金额
const selectAmount = (amount) => {
  selectedAmount.value = amount
}

// 处理输入框获得焦点
const handleInputFocus = (event) => {
  console.log('Input focused');
  // 可以在这里添加额外的焦点处理逻辑
}

// 处理输入框失去焦点
const handleInputBlur = (event) => {
  console.log('Input blurred');
  // 可以在这里添加额外的失焦处理逻辑
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 提交充值
const handleSubmit = () => {
  if (!isFormValid.value) {
    uni.showToast({
      title: 'Please fill all required fields', // 这个页面需要添加 i18n 支持
      icon: 'none'
    })
    return
  }

  // 显示加载遮罩层
  isLoading.value = true

  // 模拟支付过程
  setTimeout(() => {
    isLoading.value = false
    // 跳转到充值成功页面，不携带参数
    uni.navigateTo({
      url: '/pages/account/recharge/success'
    })
  }, 3000)
}
</script>

<style lang="scss" scoped>
@import '@/static/iconfont/iconfont.css';

.recharge-page {
  min-height: 100vh;
  background: #F5F7FA;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
}

.status-bar {
  width: 100%;
  background-color: #fff;
}

.header {
  background: #fff;
  width: 100%;

  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;

    .back-btn {
      width: 88rpx;
      height: 44px;
      display: flex;
      align-items: center;
      z-index: 10;

      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
      pointer-events: none;
    }
  }
}

.content {
  flex: 1;
  padding: 0 32rpx;
  box-sizing: border-box;
  background: #fff;
  width: 100%;
  overflow-x: hidden;
  margin-top: -40rpx;
  /* 整体布局往上提 */
}

.section-title {
  margin-top: 24rpx;
  /* 减少顶部间距 */
  margin-bottom: 20rpx;

  text {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }
}

.payment-methods {
  display: flex;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;
  padding: 0 4rpx;
  
  .payment-method {
    width: 164rpx;
    height: 164rpx;
    border: 1rpx solid #E0E0E0;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16rpx;
    box-sizing: border-box;
    position: relative;
    margin: 0 8rpx;
    background-color: #FFFFFF;
    transition: all 0.2s ease;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    
    &:last-child {
      border-right: 1rpx solid #E0E0E0;
    }
    
    &.active {
      border: 2rpx solid #007AFF;
      background-color: #F0F8FF;
      box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);
    }
    
    .payment-icon {
      width: 64rpx;
      height: 64rpx;
      object-fit: contain;
      margin-bottom: 12rpx;
      will-change: transform;
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
    }
    
    .payment-name {
      font-size: 26rpx;
      color: #333;
      font-weight: 500;
      margin-top: 8rpx;
    }
  }
}

// 添加Wave图标样式
.payment-method:nth-child(2) .payment-icon {
  background-color: #00ADEF;
  border-radius: 8rpx;
}

// 添加Moov图标样式
.payment-method:nth-child(3) .payment-icon {
  background-color: #F5F5F5;
  border-radius: 50%;
  padding: 4rpx;
}

// 添加MTN图标样式
.payment-method:nth-child(4) .payment-icon {
  background-color: #FFCC00;
  border-radius: 50%;
  padding: 4rpx;
}

.amount-packs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: 100rpx;
  gap: 20rpx;
  width: 100%;
  margin-bottom: 16rpx;

  .amount-pack {
    height: 100rpx;
    background-color: #F5F5F5;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background-color: #E5F3FF;
    }

    &.red-border {
      border: 2rpx solid #FF4D4F;
      background-color: #FFF;
    }

    .amount-value {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;

      &.blue {
        color: #007AFF;
      }
    }
  }
}

.phone-input-container {
  margin-top: 16rpx;
  width: 100%;
  position: relative;

  .phone-input {
    width: 100%;
    height: 100rpx;
    border: 1rpx solid #EBEBEB;
    border-radius: 16rpx;
    padding: 0 32rpx;
    font-size: 32rpx;
    color: #333;
    box-sizing: border-box;
    text-align: center;
    background-color: #fff;
  }
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 32rpx;
  background-color: #FFFFFF;
  box-shadow: 0 -1rpx 10rpx rgba(0, 0, 0, 0.03);

  .submit-btn {
    height: 88rpx;
    background-color: #FF4D4F;
    color: white;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    letter-spacing: 2rpx;

    &:disabled {
      opacity: 0.7;
    }
  }
}

/* 加载遮罩层样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      width: 80rpx;
      height: 80rpx;
      border: 4rpx solid #E1E9FF;
      border-top: 4rpx solid #4080FF;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 30rpx;
    }

    .loading-text {
      font-size: 32rpx;
      color: #333;
      font-weight: 500;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>