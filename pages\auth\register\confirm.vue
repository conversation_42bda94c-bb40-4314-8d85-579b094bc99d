<template>
  <view class="confirm-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
        <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题文本 -->
      <view class="title-section">
        <text class="main-title">{{ $t('auth.confirmPassword') }}</text>
      </view>

      <!-- 密码输入 -->
      <view class="password-input-section">
        <view class="password-dots">
          <view 
            v-for="(dot, index) in passwordDots" 
            :key="index"
            class="password-dot"
            :class="{ 
              filled: dot.filled, 
              active: activeIndex === index,
              success: confirmPassword.length === 4 && passwordsMatch,
              error: confirmPassword.length === 4 && !passwordsMatch
            }"
          >
            <view v-if="dot.filled" class="dot-inner"></view>
          </view>
        </view>

        <text v-if="hasError" class="error-text">{{ errorMessage }}</text>
        <text v-else class="hint-text">{{ $t('auth.reenterPassword') }}</text>
      </view>

      <!-- 数字键盘 -->
      <view class="keypad-section">
        <view class="keypad">
          <view 
            v-for="key in keypadNumbers" 
            :key="key"
            class="keypad-btn"
            :class="{ number: key !== 'delete' && key !== 'biometric' }"
            @click="onKeypadPress(key)"
          >
            <text v-if="key === 'delete'" class="iconfont icon-delete keypad-icon"></text>
            <text v-else-if="key === 'biometric'" class="iconfont icon-fingerprint keypad-icon"></text>
            <text v-else class="keypad-text">{{ key }}</text>
          </view>
        </view>
      </view>

      <!-- 用户协议 -->
      <view class="agreement-section">
        <view class="agreement-item" @click="toggleAgreement">
          <checkbox :checked="agreeTerms" color="#1E90FF" />
          <view class="agreement-text">
            <text>{{ $t('auth.agreementPrefix') }}</text>
            <text class="link" @click.stop="showPrivacyPolicy">{{ $t('settings.privacy') }}</text>
            <text>{{ $t('auth.and') }}</text>
            <text class="link" @click.stop="showTermsOfService">{{ $t('user.termsOfService') }}</text>
          </view>
        </view>
      </view>

      <!-- 注册按钮 -->
      <view class="register-section">
        <button 
          class="register-btn" 
          :class="{ active: canRegister, disabled: !canRegister, loading: isRegistering }"
          :disabled="!canRegister || isRegistering"
          @click="completeRegistration"
        >
          <text v-if="isRegistering">{{ $t('auth.registering') }}</text>
          <text v-else>{{ $t('user.register') }}</text>
        </button>
      </view>
    </view>

    <!-- 全局HUD -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { useGlobalHud } from '@/composables/useHud'
import { useUserStore } from '@/store/user'
import GlobalHUD from '@/components/common/GlobalHUD.vue'
import { registerUser, passwordLogin } from '@/api'

// 国际化
const { t: $t } = useI18n()

// 全局HUD
const hud = useGlobalHud()

// 用户状态管理
const userStore = useUserStore()

// 状态栏高度
const statusBarHeight = ref(0)

// 注册数据
const registerData = ref({})

// 密码相关
const confirmPassword = ref('')
const activeIndex = ref(-1)
const hasError = ref(false)
const errorMessage = ref('')

// 用户协议
const agreeTerms = ref(false)

// 注册状态
const isRegistering = ref(false)

// 数字键盘布局
const keypadNumbers = [
  '1', '2', '3',
  '4', '5', '6', 
  '7', '8', '9',
  'biometric', '0', 'delete'
]

// 密码点状态
const passwordDots = computed(() => {
  return Array.from({ length: 4 }, (_, index) => ({
    filled: index < confirmPassword.value.length
  }))
})

// 密码是否匹配
const passwordsMatch = computed(() => {
  return confirmPassword.value === registerData.value.password
})

// 是否可以注册
const canRegister = computed(() => {
  return confirmPassword.value.length === 4 && 
         passwordsMatch.value && 
         agreeTerms.value && 
         !isRegistering.value
})

// 监听确认密码变化
watch(confirmPassword, (newPassword) => {
  if (newPassword.length === 4) {
    if (passwordsMatch.value) {
      // 密码匹配，可以注册
      hasError.value = false
      errorMessage.value = ''
    } else {
      // 密码不匹配
      hasError.value = true
      errorMessage.value = $t('auth.passwordNotMatch')
      
      // 延迟清空密码
      setTimeout(() => {
        confirmPassword.value = ''
        hasError.value = false
        errorMessage.value = ''
      }, 1500)
    }
  } else {
    // 清除错误状态
    if (hasError.value) {
      hasError.value = false
      errorMessage.value = ''
    }
  }
})

// 键盘按键处理
const onKeypadPress = (key) => {
  if (key === 'delete') {
    // 删除最后一位
    if (confirmPassword.value.length > 0) {
      confirmPassword.value = confirmPassword.value.slice(0, -1)
      activeIndex.value = confirmPassword.value.length
    }
  } else if (key === 'biometric') {
    // 生物识别（暂不实现）
    hud.info($t('auth.biometricNotAvailable'))
  } else if (typeof key === 'string' && /^\d$/.test(key)) {
    // 数字输入
    if (confirmPassword.value.length < 4) {
      confirmPassword.value += key
      activeIndex.value = confirmPassword.value.length - 1
      
      // 短暂显示活动状态
      setTimeout(() => {
        activeIndex.value = -1
      }, 200)
    }
  }
}

// 切换用户协议
const toggleAgreement = () => {
  agreeTerms.value = !agreeTerms.value
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/personal-center/privacy-policy'
  })
}

// 显示用户协议
const showTermsOfService = () => {
  uni.navigateTo({
    url: '/pages/personal-center/terms-of-use'
  })
}

// 完成注册
const completeRegistration = async () => {
  if (!canRegister.value || isRegistering.value) {
    return
  }

  console.log('开始注册流程...')
  console.log('注册数据:', registerData.value)

  try {
    isRegistering.value = true
    hud.loading($t('auth.registering'))

    // 调用注册接口
    const response = await registerUser({
      grantType: "accountPassword",
      tenantId: "000000",
      password: registerData.value.password,
      userType: "account_app_user",
      phone: registerData.value.phoneNumber,
      nickName: registerData.value.nickName
    })

    console.log('注册响应:', response)

    if (response && (response.code === 200 || response.code === "200")) {
      hud.success($t('auth.registerSuccess'), 1500)

      // 注册成功后自动登录
      setTimeout(async () => {
        try {
          hud.loading($t('auth.autoLoggingIn'))

          console.log('开始自动登录...')
          const loginResponse = await passwordLogin({
            clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
            grantType: "accountPassword",
            password: registerData.value.password,
            rememberMe: false,
            tenantId: "000000",
            username: registerData.value.phoneNumber
          })

          console.log('自动登录响应:', loginResponse)

          if (loginResponse && (loginResponse.code === 200 || loginResponse.code === "200") && loginResponse.data) {
            // 使用Pinia存储用户信息
            userStore.loginSuccess(loginResponse.data)
            
            // 保存手机号用于快速登录
            if (registerData.value.phoneNumber) {
              userStore.savePhoneNumber(registerData.value.phoneNumber)
              console.log('手机号已保存用于快速登录:', registerData.value.phoneNumber)
            }
            
            // 同时也保存到本地存储
            uni.setStorageSync('userInfo', JSON.stringify(loginResponse.data))
            if (loginResponse.data.access_token) {
              uni.setStorageSync('token', loginResponse.data.access_token)
            } else if (loginResponse.data.token) {
              uni.setStorageSync('token', loginResponse.data.token)
            }

            hud.success($t('auth.loginSuccess'), 1500)

            // 清除注册数据
            uni.removeStorageSync('registerData')

            // 跳转到主页面
            setTimeout(() => {
              uni.reLaunch({
                url: '/pages/home/<USER>'
              })
            }, 1500)
          } else {
            // 自动登录失败，跳转到登录页面
            console.log('自动登录失败，跳转到登录页面')
            hud.success($t('auth.registerSuccessLogin'), 1500)
            
            // 清除注册数据
            uni.removeStorageSync('registerData')
            
            setTimeout(() => {
              uni.navigateTo({
                url: '/pages/auth/login/index'
              })
            }, 1500)
          }

        } catch (loginError) {
          console.error('自动登录失败:', loginError)
          
          // 自动登录失败，跳转到登录页面
          hud.success($t('auth.registerSuccessLogin'), 1500)
          
          // 清除注册数据
          uni.removeStorageSync('registerData')
          
          setTimeout(() => {
            uni.navigateTo({
              url: '/pages/auth/login/index'
            })
          }, 1500)
        }
      }, 1500)
    } else {
      throw new Error(response?.message || $t('auth.registerFailed'))
    }

  } catch (error) {
    console.error('注册失败:', error)

    let errorText = $t('auth.registerFailed')
    if (error.response?.data?.message) {
      errorText = error.response.data.message
    } else if (error.message) {
      errorText = error.message
    }

    hud.error(errorText)
  } finally {
    isRegistering.value = false
  }
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 组件挂载
onMounted(() => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })

  // 获取注册数据
  try {
    const data = uni.getStorageSync('registerData')
    if (data) {
      registerData.value = JSON.parse(data)
      console.log('获取到注册数据:', registerData.value)
    } else {
      // 没有注册数据，返回上一页
      console.error('没有找到注册数据')
      uni.navigateBack()
    }
  } catch (error) {
    console.error('获取注册数据失败:', error)
    uni.navigateBack()
  }
})
</script>

<style lang="less">
// 全局复选框样式
:deep(.uni-checkbox-input) {
  border-radius: 50% !important;
  width: 36rpx !important;
  height: 36rpx !important;
  border: 2rpx solid #ddd !important;
  background-color: #fff !important;
}

:deep(.uni-checkbox-input.uni-checkbox-input-checked) {
  background-color: #f23030 !important;
  border-color: #f23030 !important;
}

:deep(.uni-checkbox-input.uni-checkbox-input-checked::before) {
  font-size: 28rpx;
  color: #fff;
}

.confirm-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 40rpx;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    height: 88rpx;

    .back-icon {
      font-size: 32rpx;
      color: #000;
      font-weight: bold;
      padding: 20rpx;
      margin-left: -20rpx;
    }
  }

  .main-content {
    flex: 1;
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;

    .title-section {
      text-align: center;
      margin: 60rpx 0 80rpx;

      .main-title {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        line-height: 1.3;
      }
    }

    .password-input-section {
      text-align: center;
      margin-bottom: 80rpx;

      .password-dots {
        display: flex;
        justify-content: center;
        gap: 32rpx;
        margin-bottom: 40rpx;

        .password-dot {
          width: 80rpx;
          height: 80rpx;
          border: 3rpx solid #E5E5E5;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &.active {
            border-color: #f23030;
            transform: scale(1.1);
          }

          &.filled {
            border-color: #f23030;
            background: rgba(242, 48, 48, 0.1);

            .dot-inner {
              width: 32rpx;
              height: 32rpx;
              background: #f23030;
              border-radius: 50%;
            }
          }

          &.success {
            border-color: #f23030;
            background: rgba(242, 48, 48, 0.1);

            .dot-inner {
              background: #f23030;
            }
          }

          &.error {
            border-color: #f23030;
            background: rgba(242, 48, 48, 0.1);

            .dot-inner {
              background: #f23030;
            }
          }
        }
      }

      .error-text {
        color: #f23030;
        font-size: 28rpx;
      }

      .hint-text {
        color: #666;
        font-size: 28rpx;
      }
    }

    .keypad-section {
      margin-bottom: 40rpx;

      .keypad {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 32rpx;
        max-width: 600rpx;
        margin: 0 auto;

        .keypad-btn {
          height: 120rpx;
          background: #F8F9FA;
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          border: 2rpx solid transparent;

          &:active {
            background: #E9ECEF;
            transform: scale(0.95);
          }

          &.number {
            &:active {
              background: rgba(242, 48, 48, 0.1);
              border-color: #f23030;
            }
          }

          .keypad-text {
            font-size: 48rpx;
            font-weight: 500;
            color: #333;
          }

          .keypad-icon {
            font-size: 32rpx;
            color: #666;
          }
        }
      }
    }

    .agreement-section {
      margin-bottom: 40rpx;

      .agreement-item {
        display: flex;
        align-items: flex-start;
        padding: 16rpx;

        checkbox {
          margin: 6rpx 12rpx 0 0;
        }

        .agreement-text {
          font-size: 26rpx;
          color: #333;
          line-height: 1.4;

        .link {
          color: #f23030;
          text-decoration: underline;
        }
        }
      }
    }

    .register-section {
      .register-btn {
        width: 100%;
        height: 88rpx;
        background: #ccc;
        color: #999;
        border-radius: 56rpx;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;

        &.active {
          background: #f23030;
          color: #fff;
        }

        &.disabled {
          opacity: 0.6;
        }

        &.loading {
          background: #f23030;
          color: #fff;
        }
      }
    }
  }
}
</style>
