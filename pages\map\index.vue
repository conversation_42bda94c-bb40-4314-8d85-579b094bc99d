﻿<template>
  <view class="map-container">
    <!-- Status bar -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- Header -->
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <text class="iconfont icon-back"></text>
        </view>
        <text class="title">{{ t('nav.map') || 'Carte' }}</text>
      </view>
    </view>

    <!-- Search section -->
    <view class="search-section">
      <view class="search-bar" @click="goToSearch">
        <image src="/static/images/search.png" mode="aspectFit" class="search-icon" />
        <input type="text" :placeholder="t('station.searchStations') || 'Rechercher des stations de recharge'" class="search-input" />
      </view>
      <view class="menu-btn" @tap="goToStationList">
        <image src="/static/images/list.png" mode="aspectFit" class="menu-icon" />
      </view>
    </view>

    <!-- Map area -->
    <view class="map-area">
      <!-- H5 environment: Google Maps -->
      <!-- #ifdef H5 -->
      <view id="google-map" ref="googleMapRef" class="google-map"></view>
      <!-- #endif -->

      <!-- APP environment: Web-view map (only display map part) -->
      <!-- #ifdef APP-PLUS -->
      <web-view :src="mapUrl" @message="onMessage" class="native-map" />
      <!-- #endif -->

      <view class="map-controls">
        <view class="control-btn location-btn" @click="goToMyLocation">
          <image src="/static/images/my-location.png" mode="aspectFit" class="control-icon" />
        </view>
        <view class="control-btn watch-location-btn" :class="{ 'active': isWatchingLocation }"
          @click="toggleWatchLocation">
          <text class="watch-icon">{{ isWatchingLocation ? '📍' : '📌' }}</text>
        </view>
      </view>

      <!-- Loading indicator -->
      <view class="loading-overlay" v-if="loading">
        <view class="loading-spinner"></view>
      </view>
    </view>

    <!-- Charging station detail card -->
    <view class="station-card" :class="{ 'show': isShowDetail }" @click="goToStationDetail">
      <view class="card-header">
        <text class="station-name">{{ selectedStation.name || 'Arnio charging 01 PIL' }}</text>
        <text class="close-btn" @click.stop="hideStationDetail">×</text>
      </view>
      <view class="card-info">
        <view class="left-info">
          <text class="parking">P</text>
          <text class="free-text">{{ t('station.freeParking') || 'Stationnement gratuit 1 heure' }}</text>
          <text class="divider">{{ selectedStation.type || 'GBT DC 40KW' }}</text>
        </view>
        <view class="status">
          <text class="status-text">{{ t('station.available') || 'Libre' }} {{ selectedStation.available || 10 }}</text>
          <text class="total">/{{ selectedStation.total || 24 }}</text>
        </view>
      </view>
      <view class="price-section">
        <view class="normal-price">
          <text class="amount">{{ selectedStation.price || 250 }}</text>
          <text class="currency">F</text>
          <text class="unit">/kWh</text>
        </view>
        <view class="vip-section">
          <image src="/static/images/VIP.png" mode="aspectFit" class="vip-icon"></image>
          <text class="vip-price">{{ selectedStation.vipPrice || 248 }} fafc/kWh</text>
        </view>
        <view class="distance">
          <text>{{ selectedStation.distance || '4.26km' }}</text>
          <image src="/static/images/my-location.png" mode="aspectFit" class="location-icon"></image>
        </view>
      </view>
      <view class="action-buttons">
        <view class="action-btn" @click.stop="navigateToStation">
          <image src="/static/images/map-icon.png" mode="aspectFit" class="action-icon"></image>
        </view>
        <view class="action-btn" @click.stop="toggleFavorite">
          <image :src="isFavorite ? '/static/images/favorite-selected.png' : '/static/images/favorite-unselected.png'" mode="aspectFit" class="action-icon"></image>
        </view>
      </view>
    </view>
  </view>
  
</template>



<script setup>
import { ref, onMounted, onUnmounted, reactive, nextTick, watch } from 'vue'
import { useI18n } from '@/composables/useI18n.js'

import config from '../../config/env'
// #ifdef H5
import { Loader } from '@googlemaps/js-api-loader'
// #endif
// #ifdef APP-PLUS
import { onLoad } from '@dcloudio/uni-app'
// #endif
import {
  getNearbyChargingStations,
  getChargingStationDetail,
  getChargingStationList
} from '@/api'
// Import user state store
import { useUserStore } from '@/store/user'

// Use internationalization
const { t, locale } = useI18n()

// Get user state
const userStore = useUserStore()



// State data
const statusBarHeight = ref(0)
const isShowDetail = ref(false)

// #ifdef H5
const googleMapRef = ref(null)
const map = ref(null)
const markers = ref([])
const userMarker = ref(null)
const markerCluster = ref(null)
const userHeading = ref(0) // 用户方向角度
// #endif

// #ifdef APP-PLUS
const mapUrl = ref('')

// APP side page loading initialization
onLoad(() => {
  const currentLang = (locale && locale.value) ? locale.value : (uni.getStorageSync('locale') || 'fr')
  mapUrl.value = `/static/gmap/index.html?lang=${currentLang}`  // Use complete map with Google Maps and pass language

  // Add window.postMessage listener as backup communication solution
  if (typeof window !== 'undefined') {
    window.addEventListener('message', (event) => {
      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data
        console.log('Received window.postMessage:', data)
        // Call same message handling logic
        if (data.type) {
          onMessage({ detail: { data: [JSON.stringify(data)] } })
        }
      } catch (error) {
        console.error('Failed to handle window.postMessage:', error)
      }
    })
  }
})

// APP side message handling
const onMessage = (event) => {
  try {
    const data = JSON.parse(event.detail.data[0])
    console.log('Received Web-view message:', data)

    switch (data.type) {
      case 'test':
        // Test message
        console.log('✅ Received test message:', data.data)
        uni.showToast({
          title: t('toast.receivedTestMessage') || 'Message de test reçu',
          icon: 'success'
        })
        break
      case 'goBack':
        // Back button click
        console.log('Back button clicked')
        uni.navigateBack()
        break
      case 'goToSearch':
        // Search button click
        console.log('Search button clicked')
        uni.navigateTo({
          url: '/pages/search/index'
        })
        break
      case 'goToStationList':
        // List button click
        console.log('List button clicked')
        uni.navigateTo({
          url: '/pages/station-list/index'
        })
        break
      case 'goToMyLocation':
        // Location button click
        console.log('Location button clicked')
        goToMyLocation()
        break
      case 'toggleWatchLocation':
        // Real-time location toggle
        console.log('Real-time location toggle:', data.data.isWatching)
        if (data.data.isWatching) {
          startWatchingLocation()
        } else {
          stopWatchingLocation()
        }
        break
      case 'goToStationDetail':
        // Jump to charging station detail
        console.log('Jump to charging station detail:', data.data)
        if (data.data && data.data.id) {
          const plotNum = data.data.plotNum || ''
          uni.navigateTo({
            url: `/pages/station-detail/index?plotId=${data.data.id}&plotNum=${plotNum}`
          })
        }
        break
      case 'hideStationDetail':
        // Hide charging station detail
        console.log('Hide charging station detail')
        isShowDetail.value = false
        break
      case 'stationClick':
        // Charging pile click - keep consistent with H5 side handling
        const station = chargingStations.value.find(s => s.id === data.data.id)
        if (station) {
          selectChargingStation(station)
        }
        break
      case 'mapClick':
        // Map click
        console.log('Map clicked:', data.data)
        break
      case 'userLocationUpdate':
        // User location update
        userLocation.lat = data.data.lat
        userLocation.lng = data.data.lng
        console.log('Location updated:', userLocation)
        break
      case 'mapReady':
        // Map initialization complete, send charging station data
        console.log('Map initialization complete')
        if (chargingStations.value.length > 0) {
          sendChargingStationsData()
        }
        break
      case 'requestChargingStations':
        // HTML page actively requests charging pile data
        console.log('🔄 HTML page requests charging pile data')
        if (chargingStations.value.length > 0) {
          console.log('✅ Have charging pile data, send immediately')
          sendChargingStationsData()
        } else {
          console.log('⚠️ No charging pile data, try to re-fetch')
          fetchChargingStationList()
        }
        break
      case 'requestUserLocation':
      case 'getCurrentLocation':
        // Webview requests user location
        console.log('📍 Webview requests user location')
        sendUserLocationToWebView()
        break
      case 'domReady':
        console.log('✅ Web-view DOM loaded:', data.data)
        break
      case 'error':
        console.error('❌ Web-view error:', data.data)
        // 静默处理错误，不显示弹窗
        break
      case 'mapLoadError':
      case 'mapInitError':
        console.error('❌ Map error:', data.data)
        // 静默处理地图错误，不显示弹窗
        break
      case 'debug':
        console.log('🐛 Web-view debug:', data.data)
        break
      default:
        console.log('Unhandled message type:', data.type, data)
    }
  } catch (error) {
    console.error('Failed to handle Web-view message:', error)
  }
}
// #endif

// #ifdef APP-PLUS
// Send user location to Web-view
const sendUserLocationToWebView = () => {
  console.log('📍 Send user location to Web-view')
  console.log('📍 Current user location:', userLocation)

  // Check if user location is valid
  if (!userLocation.lat || !userLocation.lng) {
    console.log('⚠️ User location not available, try to get it first')
    getCurrentLocation().then(() => {
      // After getting location, send it to webview
      if (userLocation.lat && userLocation.lng) {
        console.log('✅ Got user location, sending to webview:', userLocation)
        sendLocationDataToWebView()
      }
    }).catch(err => {
      console.error('❌ Failed to get user location:', err)
    })
    return
  }

  sendLocationDataToWebView()
}

// Send language to webview
const sendLanguageToWebView = () => {
  try {
    console.log('🌐 发送语言信息到webview')
    
    const currentWebview = plus.webview.currentWebview()
    let webviewContext = null

    // Find webview
    if (currentWebview.children().length > 0) {
      webviewContext = currentWebview.children()[0]
    } else {
      webviewContext = currentWebview
    }

    if (webviewContext) {
      // 获取当前语言
      const currentLang = uni.getStorageSync('locale') || 'en'
      console.log('当前语言:', currentLang)
      
      const jsCode = `
        try {
          if (typeof receiveFromApp === 'function') {
            console.log('🌐 Receiving language from APP:', '${currentLang}');
            receiveFromApp({
              type: 'updateLanguage',
              language: '${currentLang}'
            });
          }
        } catch (error) {
          console.error('Error receiving language:', error);
        }
      `

      webviewContext.evalJS(jsCode)
      console.log('✅ 成功发送语言信息到webview')
    } else {
      console.error('❌ 未找到webview上下文')
    }
  } catch (error) {
    console.error('❌ 发送语言信息到webview失败:', error)
  }
}

// Send location data to webview
const sendLocationDataToWebView = () => {
  try {
    console.log('🔍 Looking for web-view to send location data')

    const currentWebview = plus.webview.currentWebview()
    let webviewContext = null

    // Find webview
    if (currentWebview.children().length > 0) {
      webviewContext = currentWebview.children()[0]
    } else {
      const webviews = plus.webview.all()
      for (let i = 0; i < webviews.length; i++) {
        if (webviews[i].getURL && webviews[i].getURL().includes('gmap/index.html')) {
          webviewContext = webviews[i]
          break
        }
      }
    }

    if (webviewContext) {
      console.log('✅ Found web-view, sending user location')

      const jsCode = `
        try {
          if (typeof receiveFromApp === 'function') {
            console.log('📍 Receiving user location from APP:', ${JSON.stringify(userLocation)});
            receiveFromApp({
              type: 'updateUserLocation',
              location: {
                lat: ${userLocation.lat},
                lng: ${userLocation.lng}
              }
            });
          } else {
            console.error('receiveFromApp function not found');
          }
        } catch (error) {
          console.error('Error receiving user location:', error);
        }
      `

      webviewContext.evalJS(jsCode)
      console.log('✅ Successfully sent user location to Web-view')
    } else {
      console.error('❌ Web-view context not found for sending location')
    }
  } catch (error) {
    console.error('❌ Error sending user location to Web-view:', error)
  }
}

// Send charging station data to Web-view
const sendChargingStationsToWebView = () => {
  console.log('🎯🎯🎯 Send charging station data to Web-view')
  console.log('📊 Charging station data count:', chargingStations.value.length)
  console.log('📊 Charging station data details:', chargingStations.value)

  // 检查是否在APP环境且plus API可用
  if (typeof plus === 'undefined') {
    console.warn('⚠️ plus API not available, skip sending data to web-view')
    return
  }

  try {
    console.log('🔍 Start looking for web-view context')

    // Method 1: Find web-view through current page
    const currentWebview = plus.webview.currentWebview()
    console.log('📱 Current webview:', currentWebview)
    console.log('📱 Child webview count:', currentWebview.children().length)

    let webviewContext = null

    // Try multiple ways to get web-view
    if (currentWebview.children().length > 0) {
      webviewContext = currentWebview.children()[0]
      console.log('✅ Method 1 success: Get web-view through children()[0]')
    } else {
      // Method 2: Find by ID
      const webviews = plus.webview.all()
      for (let i = 0; i < webviews.length; i++) {
        if (webviews[i].getURL && webviews[i].getURL().includes('gmap/index.html')) {
          webviewContext = webviews[i]
          console.log('✅ Method 2 success: Found web-view through URL matching')
          break
        }
      }
    }

    if (webviewContext) {
      console.log('✅ Found web-view context, preparing to send data')
      console.log('📱 web-view URL:', webviewContext.getURL ? webviewContext.getURL() : 'unknown')

      const jsCode = `
        try {
          if (typeof receiveFromApp === 'function') {
            receiveFromApp({
              type: 'updateChargingStations',
              stations: ${JSON.stringify(chargingStations.value)}
            });
            if (typeof postToApp === 'function') {
              postToApp({
                type: 'debug',
                data: { message: 'Successfully called receiveFromApp with ' + ${chargingStations.value.length} + ' stations' }
              });
            }
          } else {
            if (typeof postToApp === 'function') {
              postToApp({
                type: 'error',
                data: { message: 'receiveFromApp function not found' }
              });
            }
          }
        } catch (error) {
          if (typeof postToApp === 'function') {
            postToApp({
              type: 'error',
              data: { message: 'Error in receiveFromApp: ' + error.message }
            });
          }
        }
      `

      console.log('📤 JS code length to execute:', jsCode.length)
      webviewContext.evalJS(jsCode)
      console.log('✅ Successfully sent charging station data to Web-view')
    } else {
      console.error('❌ Web-view context not found')
      console.log('🔍 All webviews:', plus.webview.all().map(w => ({
        id: w.id,
        url: w.getURL ? w.getURL() : 'unknown'
      })))
    }
  } catch (error) {
    console.error('❌ Failed to send charging station data:', error)
    console.error('❌ Error details:', error.stack)
  }
}
// #endif

// 通用的发送充电站数据函数（兼容所有环境）
const sendChargingStationsData = () => {
  // #ifdef APP-PLUS
  if (typeof sendChargingStationsToWebView === 'function') {
    sendChargingStationsToWebView()
  } else {
    console.warn('⚠️ sendChargingStationsToWebView function not available')
  }
  // #endif

  // #ifdef H5
  console.log('H5 environment: Charging stations data already handled by Google Maps')
  // #endif
}
const userLocation = reactive({
  lat: null, // 等待获取用户真实位置
  lng: null
})
const loading = ref(false)
const searchRadius = ref(5000) // Default search radius 5000 meters

// Platform detection
const isApp = ref(false)

// Map initialization state
const mapInitialized = ref(false)

// Real-time location related
const watchId = ref(null)
const isWatchingLocation = ref(false)


// APP side complex message handling removed, use simplified onMessage

// Selected charging station
const selectedStation = reactive({
  id: '',
  name: 'Arnio charging 01 PIL',
  type: 'GBT DC 40KW',
  available: 10,
  total: 24,
  price: 250,
  vipPrice: 248,
  distance: '4.26km',
  plotNum: ''
})

const isFavorite = ref(false)

// Charging station list
const chargingStations = ref([])



// Timer ID for debouncing
let debounceTimer = null

// Listen to user location changes, re-fetch nearby charging stations (with debounce handling)
watch([() => userLocation.lat, () => userLocation.lng], () => {
  // Clear previous timer
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // Set new timer, delay execution of request
  debounceTimer = setTimeout(() => {
    fetchChargingStationList()
  }, 1000) // 1 second debounce delay
}, { deep: true })

// Initialize when page loads
onMounted(async () => {
  console.log('Check login status...')
  const userStore = useUserStore()
  const isLoggedIn = userStore.isLoggedIn
  console.log('User login status:', isLoggedIn)

  // Get system info and detect runtime environment
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
      console.log('Current runtime environment:', res.platform)

      // Detect if APP environment
      // #ifdef APP-PLUS
      isApp.value = true
      console.log('Detected APP environment, using native map')
      // #endif

      // #ifdef H5
      isApp.value = false
      console.log('Detected H5 environment, using Google Maps')
      // #endif
    }
  })

  try {
    // Get user location
    await getCurrentLocation()

    // Initialize map based on platform
    // #ifdef APP-PLUS
    console.log('APP environment: Using Web-view map')
    // Set web-view map URL with language parameter
    const currentLang = (locale && locale.value) ? locale.value : (uni.getStorageSync('locale') || 'fr')
    mapUrl.value = `/static/gmap/index.html?lang=${currentLang}`

    // Send user location and language to webview after a short delay to ensure webview is loaded
    setTimeout(() => {
      console.log('📍 APP环境：发送用户位置到webview')
      sendUserLocationToWebView()
    }, 1500)
    
    // Send language to webview
    setTimeout(() => {
      console.log('🌐 APP环境：发送语言信息到webview')
      sendLanguageToWebView()
    }, 2000)

    // Get charging station data
    setTimeout(() => {
      fetchChargingStationList()
    }, 2000)
    // #endif

    // #ifdef H5
    console.log('H5 environment: Initialize Google map')
    await initGoogleMap()

    // Get charging station data
    setTimeout(() => {
      fetchChargingStationList()
    }, 500)
    // #endif

    // 不自动启动实时定位，让用户手动控制
    // 避免实时定位覆盖正确的用户位置
    console.log('📍 地图初始化完成，不自动启动实时定位')

  } catch (error) {
    console.error('Failed to initialize map:', error)
    loading.value = false
  }
})

// Stop real-time location when page unloads
onUnmounted(() => {
  console.log('Map page unloaded, stop real-time location')
  stopWatchingLocation()
})

// Check login status
const checkLoginStatus = () => {
  console.log('Check login status...')
  const token = uni.getStorageSync('token')
  const isLoggedIn = userStore.loggedIn || Boolean(token)

  console.log('Login status:', isLoggedIn, 'User info:', userStore.getUserInfo)

  if (!isLoggedIn) {
    console.log('User not logged in, show login prompt')

    // Show login prompt
    uni.showModal({
      title: 'Login Required',
      content: 'Please login first to get full functionality',
      confirmText: 'Go Login',
      cancelText: 'Not Now',
      success: (res) => {
        if (res.confirm) {
          // User clicked confirm, jump to login page
          uni.navigateTo({
            url: '/pages/auth/login/index'
          })
        } else {
          // User clicked cancel, use mock data
          console.log('User chose not to login, use mock data')
        }
      }
    })

    return false
  }

  return true
}



// #ifdef H5
// Initialize Google Maps
const initGoogleMap = async () => {
  try {
    loading.value = true

    console.log('Initialize Google Maps...')
    console.log('Current environment:', process.env.NODE_ENV)
    console.log('API Key:', config.GOOGLE_MAPS_API_KEY ? 'Configured' : 'Not configured')

    // Get user location
    try {
      await getCurrentLocation()
      console.log('Get user location success:', userLocation)
    } catch (locationError) {
      console.warn('Failed to get user location, use default location:', locationError)
    }

    // Load Google Maps API
    console.log('Load Google Maps API')

    try {
      // Load Google Maps API
      const loader = new Loader({
        apiKey: config.GOOGLE_MAPS_API_KEY,
        version: 'weekly',
        libraries: ['places', 'marker']
      })

      const google = await loader.load()
      console.log('Google Maps API loaded successfully')

      // Create map instance
      nextTick(() => {
        try {
          const mapElement = document.getElementById('google-map')

          if (!mapElement) {
            console.error('Cannot find map container element')
            loading.value = false
            return
          }

          map.value = new google.maps.Map(mapElement, {
            center: { lat: userLocation.lat, lng: userLocation.lng },
            zoom: 14,
            minZoom: 2,  // 最小缩放级别，可以看到国家级别
            maxZoom: 20, // 最大缩放级别
            mapTypeControl: false,
            fullscreenControl: false,
            streetViewControl: false,
            zoomControl: false,
            rotateControl: false,
            scaleControl: false,
            panControl: false,
            keyboardShortcuts: false,
            clickableIcons: false,
            disableDefaultUI: true,
            gestureHandling: 'greedy'
          })

          console.log('Google Maps created successfully')

          // Load MarkerClusterer library
          try {
            // 动态加载 MarkerClusterer 库
            const script = document.createElement('script')
            script.src = 'https://unpkg.com/@google/markerclustererplus@4.0.1/dist/markerclustererplus.min.js'
            script.onload = () => {
              console.log('MarkerClusterer library loaded')
              // 初始化聚合器
              initializeMarkerClusterer()
            }
            script.onerror = () => {
              console.warn('Failed to load MarkerClusterer, using fallback')
              // 如果加载失败，继续使用普通标记
            }
            document.head.appendChild(script)
          } catch (err) {
            console.warn('MarkerClusterer not available, using regular markers')
          }

          // Add user location marker
          addUserLocationMarker()

          // Map creation complete
          loading.value = false
        } catch (mapError) {
          console.error('Failed to create map instance:', mapError)

          // 静默处理错误，不显示弹窗
          console.warn('Map creation failed, continuing without showing modal')

          loading.value = false
        }
      })
    } catch (apiError) {
      console.error('Error loading Google Maps API:', apiError)

      // 静默处理错误，不显示弹窗
      console.warn('Map API loading failed, continuing without showing modal')

      loading.value = false
    }
  } catch (error) {
    console.error('Error occurred during map initialization:', error)
    loading.value = false
  }
}
// #endif

// #ifdef H5
// 初始化标记聚合器
const initializeMarkerClusterer = () => {
  try {
    console.log('开始初始化MarkerClusterer')
    console.log('window.MarkerClusterer:', !!window.MarkerClusterer)
    console.log('map.value:', !!map.value)

    if (window.MarkerClusterer && map.value) {
      // 创建聚合器实例 - 使用 MarkerClustererPlus
      markerCluster.value = new window.MarkerClusterer(map.value, [], {
        gridSize: 60,        // 聚合网格大小
        maxZoom: 15,         // 最大聚合缩放级别
        minimumClusterSize: 2, // 最少2个点才聚合
        styles: [
          {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="50" height="50" xmlns="http://www.w3.org/2000/svg">
                <circle cx="25" cy="25" r="22" fill="#2196F3" stroke="#fff" stroke-width="3"/>
              </svg>
            `),
            height: 50,
            width: 50,
            textColor: 'white',
            textSize: 16,
            fontWeight: 'bold'
          }
        ]
      })

      console.log('✅ MarkerClusterer初始化成功:', !!markerCluster.value)
    } else {
      console.warn('⚠️ MarkerClusterer初始化失败，缺少依赖')
    }
  } catch (err) {
    console.error('❌ MarkerClusterer初始化异常:', err)
  }
}
// #endif

// Get charging station details
const fetchChargingStationDetail = async (stationId) => {
  try {
    const params = { id: stationId }

    // 验证坐标有效性并添加coordinate参数
    const isValidLat = userLocation.lat !== null && userLocation.lat !== undefined && !isNaN(userLocation.lat)
    const isValidLng = userLocation.lng !== null && userLocation.lng !== undefined && !isNaN(userLocation.lng)

    if (isValidLat && isValidLng) {
      params.coordinate = `${userLocation.lng},${userLocation.lat}`
      console.log('✅ 使用用户坐标获取站点详情:', params.coordinate)
    } else {
      console.log('⚠️ 用户坐标无效，详细信息:')
      console.log('  - userLocation.lat:', userLocation.lat, typeof userLocation.lat, 'isValid:', isValidLat)
      console.log('  - userLocation.lng:', userLocation.lng, typeof userLocation.lng, 'isValid:', isValidLng)
      console.log('⚠️ 不传递coordinate参数')
    }

    const response = await getChargingStationDetail(params)

    if (response && response.code === 200 && response.data) {
      // Update selected charging station info
      Object.assign(selectedStation, {
        id: response.data.id,
        name: response.data.name,
        type: response.data.connectorType,
        available: response.data.availableCount,
        total: response.data.totalCount,
        price: response.data.price,
        vipPrice: response.data.vipPrice,
        distance: formatDistance(response.data.distance),
        plotNum: item.plotNum
      })
    }
  } catch (error) {
    console.error('Failed to get charging station details:', error)
  }
}

// Get charging pile location list
const fetchChargingStationList = async () => {
  if (!userLocation.lat || !userLocation.lng) {
    console.warn('User location not obtained, cannot get charging pile location list')
    uni.showToast({
      title: t('toast.locationFailed') || 'Impossible d\'obtenir les informations de localisation',
      icon: 'none'
    })
    loading.value = false
    return
  }

  try {
    loading.value = true
    uni.showLoading({
      title: t('common.loading'),
      mask: true
    })

    // Build request parameters
    const params = {
      pageNum: 1,
      pageSize: 50,
      isAsc: 'asc',
      orderByColumn: 'distance',
      _t: Date.now() // Add timestamp to ensure each request is unique
    }

    // 验证坐标有效性并添加coordinate参数
    const isValidLat = userLocation.lat !== null && userLocation.lat !== undefined && !isNaN(userLocation.lat)
    const isValidLng = userLocation.lng !== null && userLocation.lng !== undefined && !isNaN(userLocation.lng)

    if (isValidLat && isValidLng) {
      params.coordinate = `${userLocation.lng},${userLocation.lat}`
      console.log('✅ 使用用户坐标获取充电站列表:', params.coordinate)
    } else {
      console.log('⚠️ 用户坐标无效，详细信息:')
      console.log('  - userLocation.lat:', userLocation.lat, typeof userLocation.lat, 'isValid:', isValidLat)
      console.log('  - userLocation.lng:', userLocation.lng, typeof userLocation.lng, 'isValid:', isValidLng)
      console.log('⚠️ 不传递coordinate参数')
    }

    console.log('Start getting charging pile location list:', params)

    // Make request with configuration to prevent duplicate requests
    const response = await getChargingStationList(params, {
      retry: 1,  // Retry once on failure
      timeout: 15000, // Increase timeout to 15 seconds
      showLoading: false, // Avoid duplicate loading display
      cancelToken: null // Don't use default cancel token
    })

    console.log('🎯🎯🎯 Get charging pile location list success!')
    console.log('📊 API response:', response)

    if (response && response.code === 200 && response.rows && Array.isArray(response.rows)) {
      console.log('✅ API response format correct')
      console.log('📊 Raw data rows:', response.rows.length)
      console.log('📊 Raw data example:', response.rows.slice(0, 2))

      // Convert data format
      const stations = response.rows.map(item => ({
        id: item.plotId,
        name: item.plotName,
        address: item.address,
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        status: item.plotStatus,
        price: item.price,
        vipPrice: item.vipPrice,
        distance: item.distance,
        fastCount: item.countMap?.fastCount || 0,
        slowCount: item.countMap?.slowCount || 0,
        totalCount: (item.countMap?.fastCount || 0) + (item.countMap?.slowCount || 0),
        imgUrl: item.plotImgUrls,
        plotNum: item.plotNum
      }))

      console.log(`🎉 Data conversion complete, got ${stations.length} charging stations`)
      console.log('📊 Converted data example:', stations.slice(0, 2))

      if (stations.length > 0) {
        // Add charging station markers
        console.log('🔄 Start adding charging station markers')
        addChargingStationMarkers(stations)
      } else {
        console.warn('⚠️ No charging stations found')
        uni.showToast({
          title: t('toast.noStationsNearby') || 'Aucune station de recharge à proximité',
          icon: 'none',
          duration: 2000
        })
        loading.value = false
      }
    } else {
      throw new Error('API returned incorrect data format')
    }
  } catch (error) {
    console.error('API call failed:', error)

    uni.showToast({
      title: $t('toast.requestFailed') || 'Failed to get charging stations, please try again later',
      icon: 'none',
      duration: 2000
    })

    loading.value = false
  } finally {
    loading.value = false
    uni.hideLoading()
  }
}

// 获取当前位置（针对非洲地区优化）
const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    console.log('开始获取用户位置（非洲地区优化）...')

    // 优先使用WGS84坐标系（国际标准）
    uni.getLocation({
      type: 'wgs84', // 使用国际标准坐标系，适用于非洲地区
      isHighAccuracy: true,
      timeout: 15000,
      maximumAge: 0,
      success: (res) => {
        console.log('WGS84定位成功:', res)

        // 验证定位精度和合理性
        const isHighAccuracy = res.accuracy && res.accuracy < 1000
        const isValidCoordinate = Math.abs(res.latitude) <= 90 && Math.abs(res.longitude) <= 180

        // 检查是否是中东地区坐标（可能受VPN影响）
        const isInMiddleEast = res.latitude >= 15 && res.latitude <= 35 && res.longitude >= 40 && res.longitude <= 65

        if (isHighAccuracy && isValidCoordinate && !isInMiddleEast) {
          userLocation.lat = res.latitude
          userLocation.lng = res.longitude

          // 如果地图已经初始化，则更新中心点
          if (map.value) {
            map.value.setCenter({ lat: userLocation.lat, lng: userLocation.lng })
            addUserLocationMarker()
          }

          // #ifdef APP-PLUS
          // 在APP环境中，立即发送位置给webview
          console.log('📍 APP环境：获取到位置，发送给webview')
          setTimeout(() => {
            sendUserLocationToWebView()
          }, 500)
          // #endif

          console.log('✅ 高精度定位成功，精度:', res.accuracy + 'm')
          resolve(res)
        } else if (isInMiddleEast) {
          console.error('🚨 检测到中东地区坐标，可能受VPN影响')
          console.error('🚨 原始坐标:', res.latitude, res.longitude)
          console.error('🚨 建议关闭VPN后重试')

          // 不更新用户位置，保持原有位置
          console.warn('⚠️ 保持原有位置，不使用可能错误的坐标')
          resolve({
            latitude: userLocation.lat,
            longitude: userLocation.lng,
            useDefaultLocation: true,
            message: 'VPN detected, using previous location'
          })
        } else {
          console.warn('⚠️ 定位精度不足，尝试浏览器GPS定位')
          tryBrowserGeolocation(resolve, reject)
        }
      },
      fail: (err) => {
        console.error('获取位置失败:', err)

        // 尝试使用浏览器 Geolocation API 作为备用
        if (navigator && navigator.geolocation) {
          console.log('尝试使用浏览器 Geolocation API 获取位置')
          navigator.geolocation.getCurrentPosition(
            (position) => {
              console.log('浏览器 Geolocation API 获取位置成功:', position)

              // 检查是否是中东地区坐标（可能受VPN影响）
              const isInMiddleEast = position.coords.latitude >= 15 && position.coords.latitude <= 35 &&
                                   position.coords.longitude >= 40 && position.coords.longitude <= 65

              if (isInMiddleEast) {
                console.error('🚨 浏览器定位返回中东地区坐标，可能受VPN影响')
                console.error('🚨 原始坐标:', position.coords.latitude, position.coords.longitude)
                console.error('🚨 建议关闭VPN后重试')

                // 不更新用户位置，保持原有位置
                console.warn('⚠️ 保持原有位置，不使用可能错误的坐标')
                resolve({
                  latitude: userLocation.lat,
                  longitude: userLocation.lng,
                  useDefaultLocation: true,
                  message: 'VPN detected, using previous location'
                })
                return
              }

              userLocation.lat = position.coords.latitude
              userLocation.lng = position.coords.longitude

              if (map.value) {
                map.value.setCenter({ lat: userLocation.lat, lng: userLocation.lng })
                addUserLocationMarker()
              }

              // #ifdef APP-PLUS
              // 在APP环境中，立即发送位置给webview
              console.log('📍 APP环境：浏览器定位成功，发送给webview')
              setTimeout(() => {
                sendUserLocationToWebView()
              }, 500)
              // #endif

              resolve({
                latitude: position.coords.latitude,
                longitude: position.coords.longitude
              })
            },
            (geoError) => {
              console.error('浏览器 Geolocation API 获取位置失败:', geoError)
              reject(err)
            }
          )
        } else {
          reject(err)
        }
      }
    })
  })
}

// #ifdef H5
// 添加用户位置标记（仅H5环境）
const addUserLocationMarker = () => {
  try {
    // 原生地图有自带的show-location属性，不需要手动添加用户位置标记
    if (isApp.value) {
      console.log('原生地图使用show-location属性显示用户位置')
      return
    }

    // 检查地图和Google API是否已加载
    if (!map.value || !window.google) {
      console.warn('地图或Google API未加载，无法添加用户位置标记')
      return
    }

    // 如果已经有用户位置标记，先移除
    if (userMarker.value) {
      userMarker.value.setMap(null)
    }

    // 创建用户位置图标函数
    const createUserIcon = (heading = 0) => {
      return {
        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
          <svg width="36" height="36" xmlns="http://www.w3.org/2000/svg">
            <!-- 外圈光晕 -->
            <circle cx="18" cy="18" r="17" fill="#4285F4" opacity="0.3"/>
            <!-- 主体蓝点 -->
            <circle cx="18" cy="18" r="9" fill="#4285F4" stroke="#fff" stroke-width="3"/>
            <!-- 方向箭头 (旋转) -->
            <g transform="rotate(${heading} 18 18)">
              <path d="M18 6 L24 15 L12 15 Z" fill="#fff"/>
            </g>
          </svg>
        `),
        scaledSize: new window.google.maps.Size(36, 36),
        anchor: new window.google.maps.Point(18, 18)
      }
    }

    // 创建新的用户位置标记 - 使用小蓝点带箭头
    userMarker.value = new window.google.maps.Marker({
      position: { lat: userLocation.lat, lng: userLocation.lng },
      map: map.value,
              title: t('map.myLocation') || 'Ma position',
      icon: createUserIcon(userHeading.value),
      zIndex: 999
    })

    // 启动指南针监听
    // #ifdef H5
    if (window.DeviceOrientationEvent) {
      window.addEventListener('deviceorientation', (event) => {
        if (event.alpha !== null) {
          userHeading.value = event.alpha
          if (userMarker.value) {
            userMarker.value.setIcon(createUserIcon(userHeading.value))
          }
        }
      })
    }
    // #endif

    // #ifndef H5
    uni.onCompassChange((res) => {
      userHeading.value = res.direction
      if (userMarker.value) {
        userMarker.value.setIcon(createUserIcon(userHeading.value))
      }
    })
    // #endif

    console.log('用户位置标记添加成功')
  } catch (error) {
    console.error('添加用户位置标记失败:', error)
  }
}
// #endif

// 添加充电站标记
const addChargingStationMarkers = (stations) => {
  if (!stations || stations.length === 0) {
    console.warn('没有充电站数据需要添加标记')
    loading.value = false
    return
  }

  // 更新充电站数据
  chargingStations.value = stations

  console.log('开始添加充电站标记，数量:', stations.length)

  // #ifdef H5
  try {
    // H5环境：使用Google Maps标记
    if (!map.value || !window.google) {
      console.warn('地图或Google API未加载，跳过添加标记')
      loading.value = false
      return
    }

    // 清除现有标记和聚合
    if (markerCluster.value) {
      markerCluster.value.clearMarkers()
    }
    markers.value.forEach(marker => marker.setMap(null))
    markers.value = []

    // 添加新标记
    stations.forEach((station, index) => {
      try {
        // 使用简单的图标，避免打包后路径问题
        const customIcon = {
          url: '/static/images/scatter.png',
          scaledSize: new window.google.maps.Size(32, 32),
          anchor: new window.google.maps.Point(16, 16)
        }

        const marker = new window.google.maps.Marker({
          position: { lat: station.latitude, lng: station.longitude },
          title: station.name || `充电站 ${index + 1}`,
          icon: customIcon
        })

        // 添加点击事件
        marker.addListener('click', () => {
          console.log('H5环境：充电站标记被点击', station)
          selectChargingStation(station)
        })

        markers.value.push(marker)
      } catch (err) {
        console.error('添加标记失败:', err)
      }
    })

    // 使用聚合器显示标记
    if (markers.value.length > 0) {
      console.log('准备显示标记，聚合器状态:', !!markerCluster.value)
      if (markerCluster.value && markerCluster.value.addMarkers) {
        // 使用聚合器
        markerCluster.value.addMarkers(markers.value)
        console.log('✅ 标记已添加到聚合器，数量:', markers.value.length)
      } else {
        // 降级方案：直接显示标记
        console.log('⚠️ 聚合器不可用，使用降级方案')
        markers.value.forEach(marker => marker.setMap(map.value))
        console.log('✅ 使用降级方案显示标记，数量:', markers.value.length)
      }

      // 自动调整地图视野以包含所有充电桩
      const bounds = new window.google.maps.LatLngBounds()

      // 添加用户位置到边界
      if (userMarker.value) {
        bounds.extend(userMarker.value.getPosition())
      }

      // 添加所有充电桩位置到边界
      markers.value.forEach(marker => {
        bounds.extend(marker.getPosition())
      })

      // 调整地图视野，添加适当的内边距
      map.value.fitBounds(bounds, {
        top: 50,
        right: 50,
        bottom: 50,
        left: 50
      })

      // 确保缩放级别在合理范围内
      window.google.maps.event.addListenerOnce(map.value, 'bounds_changed', function () {
        if (map.value.getZoom() < 6) {
          map.value.setZoom(6)
        }
        if (map.value.getZoom() > 16) {
          map.value.setZoom(16)
        }
      })
    }

    console.log('标记添加完成:', markers.value.length)
    loading.value = false
  } catch (error) {
    console.error('H5环境添加充电站标记失败:', error)
    loading.value = false
  }
  // #endif

  // #ifdef APP-PLUS
  try {
    // APP环境：通过web-view发送充电站数据
    console.log('APP环境：发送充电站数据到web-view')

    // 延迟发送，确保web-view完全加载
    setTimeout(() => {
      sendChargingStationsToWebView()
    }, 1000)

    // 也尝试立即发送一次
    sendChargingStationsToWebView()
    loading.value = false
  } catch (error) {
    console.error('APP环境添加充电站标记失败:', error)
    loading.value = false
  }
  // #endif
}

// 选择充电站
const selectChargingStation = (station) => {
  if (!station) {
    console.warn('选择的充电站为空')
    return
  }

  console.log('选择充电站:', station)
  console.log('当前isShowDetail状态:', isShowDetail.value)

  // 更新选中的充电站
  updateSelectedStationInfo(station)

  // 显示充电站详情卡片
  console.log('准备显示充电站详情卡片')
  showStationDetail()
  console.log('显示充电站详情卡片后，isShowDetail状态:', isShowDetail.value)

  // 在地图上居中显示选中的充电站
  if (isApp.value) {
    // APP环境下，使用原生地图API设置中心点
    console.log('APP环境：地图居中到选中充电站')
    // 这里需要根据实际的原生插件API进行调用
  } else if (map.value && station.latitude && station.longitude) {
    // H5环境下，使用Google Maps JavaScript API设置中心点
    map.value.setCenter({ lat: station.latitude, lng: station.longitude })
    map.value.setZoom(16) // 放大地图
  }
}

// 更新选中的充电站信息
const updateSelectedStationInfo = (station) => {
  console.log('更新选中的充电站信息:', station)

  // 更新到selectedStation对象
  selectedStation.id = station.id || ''
  selectedStation.name = station.name || 'Charging Station'

  // 处理充电桩类型和数量
  if (station.fastCount !== undefined || station.slowCount !== undefined) {
    // 如果有fastCount和slowCount字段
    selectedStation.type = 'GBT DC'
    selectedStation.available = station.fastCount || 0
    selectedStation.total = (station.fastCount || 0) + (station.slowCount || 0)
  } else if (station.availableCount !== undefined && station.totalCount !== undefined) {
    // 如果有availableCount和totalCount字段
    selectedStation.type = station.connectorType || 'GBT DC'
    selectedStation.available = station.availableCount
    selectedStation.total = station.totalCount
  } else {
    // 默认值
    selectedStation.type = 'GBT DC'
    selectedStation.available = 0
    selectedStation.total = 0
  }

  // 处理价格
  selectedStation.price = station.price || 0
  selectedStation.vipPrice = station.vipPrice || 0

  // 处理距离
  selectedStation.distance = formatDistance(station.distance || 0)

  // 处理plotNum
  selectedStation.plotNum = station.plotNum || ''

  console.log('更新后的充电站信息:', selectedStation)
}

// 格式化距离
const formatDistance = (distance) => {
  if (distance === undefined || distance === null) return '未知'

  // 如果已经是字符串且包含单位，直接返回
  if (typeof distance === 'string' && (distance.includes('km') || distance.includes('m'))) {
    return distance
  }

  // 转换为数字
  const distanceNum = parseFloat(distance)

  if (isNaN(distanceNum)) return '未知'

  // 如果大于等于1公里，显示为公里
  if (distanceNum >= 1000) {
    return (distanceNum / 1000).toFixed(2) + 'km'
  }

  // 否则显示为米
  return Math.round(distanceNum) + 'm'
}

// 开始实时定位
const startWatchingLocation = () => {
  if (isWatchingLocation.value) {
    console.log('已经在实时定位中')
    return
  }

  console.log('开始实时定位...')
  isWatchingLocation.value = true

  // 使用HTML5 Geolocation API进行实时定位
  if (navigator.geolocation) {
    watchId.value = navigator.geolocation.watchPosition(
      (position) => {
        console.log('实时位置更新:', position)

        const lat = position.coords.latitude
        const lng = position.coords.longitude
        const accuracy = position.coords.accuracy

        // 验证位置的合理性，避免VPN导致的错误位置
        const isValidCoordinate = Math.abs(lat) <= 90 && Math.abs(lng) <= 180
        const isHighAccuracy = accuracy < 1000 // 精度小于1000米

        // 检查是否是明显错误的位置（如中东地区）
        const isInMiddleEast = lat >= 15 && lat <= 35 && lng >= 40 && lng <= 65

        if (isValidCoordinate && isHighAccuracy && !isInMiddleEast) {
          console.log('✅ 实时位置验证通过，精度:', accuracy + 'm')

          // 更新用户位置
          userLocation.lat = lat
          userLocation.lng = lng

          // #ifdef H5
          // H5环境：更新地图中心和用户标记
          if (map.value) {
            map.value.setCenter({ lat, lng })
            addUserLocationMarker()
          }
          // #endif

          // #ifdef APP-PLUS
          // APP环境：只发送位置给webview，不更新本地地图
          console.log('📱 APP环境：发送实时位置更新给webview')
          setTimeout(() => {
            sendUserLocationToWebView()
          }, 500)
          // #endif

          console.log('位置已更新:', { lat, lng })
        } else {
          console.warn('⚠️ 实时位置验证失败，可能受VPN影响')
          console.warn('位置信息:', { lat, lng, accuracy, isInMiddleEast })
          console.warn('建议关闭VPN或检查网络设置')
        }
      },
      (error) => {
        console.error('实时定位失败:', error)
        stopWatchingLocation()

        let errorMsg = '实时定位失败'
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMsg = '位置权限被拒绝'
            break
          case error.POSITION_UNAVAILABLE:
            errorMsg = '位置信息不可用'
            break
          case error.TIMEOUT:
            errorMsg = '定位请求超时'
            break
        }

        uni.showToast({
          title: errorMsg || $t('toast.locationFailed') || 'Location failed',
          icon: 'none',
          duration: 2000
        })
      },
      {
        enableHighAccuracy: true, // 启用高精度GPS定位
        timeout: 15000,          // 15秒超时，与其他页面保持一致
        maximumAge: 1000         // 1秒内的缓存位置可用，实时定位需要更新频率
      }
    )
  } else {
    console.error('浏览器不支持Geolocation')
    uni.showToast({
      title: $t('toast.deviceNoLocation') || 'Device does not support location',
      icon: 'none',
      duration: 2000
    })
  }
}

// 停止实时定位
const stopWatchingLocation = () => {
  if (watchId.value !== null) {
    navigator.geolocation.clearWatch(watchId.value)
    watchId.value = null
  }
  isWatchingLocation.value = false
  console.log('已停止实时定位')
}

// 定位到我的位置（单次定位）
const goToMyLocation = () => {
  getCurrentLocation().catch(err => {
    console.warn('定位到我的位置失败:', err)
  })

  // #ifdef H5
  // H5环境：手动设置Google Maps中心点
  if (map.value) {
    map.value.setCenter({ lat: userLocation.lat, lng: userLocation.lng })
    map.value.setZoom(16)
  }
  // #endif

  // #ifdef APP-PLUS
  // APP环境：web-view地图会自动更新到用户位置
  console.log('APP环境：定位到我的位置')
  // #endif
}

// 切换实时定位
const toggleWatchLocation = () => {
  if (isWatchingLocation.value) {
    stopWatchingLocation()
    uni.showToast({
      title: $t('map.realTimeLocationOff') || 'Realtime location off',
      icon: 'none',
      duration: 1000
    })
  } else {
    startWatchingLocation()
    uni.showToast({
      title: $t('map.realTimeLocationOn') || 'Realtime location on',
      icon: 'none',
      duration: 1000
    })
  }
}

// 方法
const goBack = () => {
  uni.navigateBack()
}

const showStationDetail = () => {
  console.log('showStationDetail被调用，设置isShowDetail为true')
  isShowDetail.value = true
  console.log('showStationDetail执行后，isShowDetail值:', isShowDetail.value)
}

const hideStationDetail = () => {
  console.log('hideStationDetail被调用，设置isShowDetail为false')
  isShowDetail.value = false
}



// 跳转到搜索页面
const goToSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index'
  })
}

// 跳转到站点列表页面
const goToStationList = () => {
  uni.navigateTo({
    url: '/pages/station-list/index'
  })
}



// 跳转到站点详情页面
const goToStationDetail = () => {
  if (!selectedStation.id) {
    console.warn('没有选中的充电站ID，无法跳转到详情页')
    uni.showToast({
      title: $t('toast.stationInfoMissing') || 'Unable to get charging station information',
      icon: 'none'
    })
    return
  }

  console.log('跳转到充电站详情页，ID:', selectedStation.id)
  console.log('跳转到充电站详情页，plotNum:', selectedStation.plotNum)
  console.log('完整的selectedStation对象:', selectedStation)

  uni.navigateTo({
    url: `/pages/station-detail/index?plotId=${selectedStation.id}&plotNum=${selectedStation.plotNum}`
  })
}
</script>
<style lang="less" scoped>
.map-container {
  min-height: 100vh;
  background-color: #fff;
  position: relative;
}

.container {
  width: 100%;
  height: 100vh;
}

.header {
  padding: 20rpx 32rpx 0;
  background-color: #fff;

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;

    .back-btn {
      position: absolute;
      left: 0;
      font-size: 32rpx;
      color: #000;
    }

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #000;
    }
  }
}

.search-section {
  padding: 0 32rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 0;
  background: #FFFFFF;

  .search-bar {
    flex: 1;
    height: 80rpx;
    background-color: #F5F5F5;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    cursor: pointer;

    .search-icon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 12rpx;
    }

    .search-input {
      flex: 1;
      height: 100%;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }

  .menu-btn {
    width: 80rpx;
    height: 80rpx;
    background-color: #F5F5F5;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .menu-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}

.map-area {
  flex: 1;
  width: 100%;
  height: calc(100vh - 255rpx);
  position: relative;

  .google-map {
    width: 100%;
    height: 100%;
    min-height: 500px;
    /* 确保在所有环境中有足够的高度 */
  }

  /* #ifdef APP-PLUS */
  .native-map {
    width: 100%;
    height: 100%;
    min-height: 500px;
    /* 确保在所有环境中有足够的高度 */
  }

  .container {
    width: 100%;
    height: 100vh;
  }

  /* #endif */

  .map-controls {
    position: absolute;
    right: 32rpx;
    bottom: 80rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    z-index: 1000;

    .control-btn {
      width: 80rpx;
      height: 80rpx;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
      z-index: 1000;

      .control-icon {
        width: 40rpx;
        height: 40rpx;
      }

      .watch-icon {
        font-size: 28rpx;
      }

      &.active {
        background-color: #4CAF50;
        color: white;
      }
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;

    .loading-spinner {
      width: 80rpx;
      height: 80rpx;
      border: 6rpx solid #f3f3f3;
      border-top: 6rpx solid #FF0000;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.station-card {
  position: fixed;
  left: 8rpx;
  right: 8rpx;
  bottom: -300rpx;
  background: #F7F7F7;
  border-radius: 12rpx;
  padding: 20rpx 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
  cursor: pointer;
  z-index: 10001;
  min-height: 160rpx;

  &.show {
    transform: translateY(-320rpx);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12rpx;

    .station-name {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .close-btn {
      font-size: 32rpx;
      color: #999;
      padding: 4rpx;
    }
  }

  .card-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    gap: 12rpx;

    .left-info {
      display: flex;
      align-items: center;
      gap: 8rpx;
      flex: 1;
      min-width: 0;

      .parking {
        background: #F5F5F5;
        padding: 2rpx 8rpx;
        font-size: 24rpx;
        color: #666;
        border-radius: 4rpx;
        white-space: nowrap;
      }

      .free-text {
        font-size: 24rpx;
        color: #666;
        white-space: nowrap;
      }

      .divider {
        font-size: 24rpx;
        color: #333;
        margin-left: 8rpx;
      }
    }

    .status {
      flex-shrink: 0;
      text-align: right;

      .status-text {
        color: #00C853;
        font-size: 24rpx;
        white-space: nowrap;
      }

      .total {
        color: #999;
        font-size: 24rpx;
        white-space: nowrap;
      }
    }
  }

  .price-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8rpx;

    .normal-price {
      flex-shrink: 0;
      max-width: 160rpx;

      .amount {
        font-size: 28rpx;
        color: #FF0000;
        font-weight: 600;
      }

      .currency {
        font-size: 20rpx;
        color: #FF0000;
        margin-left: 4rpx;
      }

      .unit {
        font-size: 18rpx;
        color: #FF0000;
        margin-left: 4rpx;
      }
    }

    .vip-section {
      display: flex;
      align-items: center;
      gap: 2rpx;
      flex-shrink: 0;
      min-width: 90rpx;
      max-width: 120rpx;

      .vip-icon {
        width: 28rpx;
        height: 28rpx;
        flex-shrink: 0;
        opacity: 1 !important;
        visibility: visible !important;
        transition: none;
        display: block !important;
        background-color: transparent;
      }

      .vip-price {
        font-size: 16rpx;
        color: #B8860B;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.1;
      }
    }

    .distance {
      display: flex;
      align-items: center;
      gap: 2rpx;
      background: #F5F5F5;
      padding: 3rpx 6rpx;
      border-radius: 12rpx;
      flex-shrink: 0;
      min-width: 60rpx;

      text {
        font-size: 16rpx;
        color: #333;
        white-space: nowrap;
        line-height: 1.1;
      }

      .location-icon {
        width: 18rpx;
        height: 18rpx;
        flex-shrink: 0;
        opacity: 1 !important;
        visibility: visible !important;
        transition: none;
        display: block !important;
        background-color: transparent;
      }
    }
  }
}

/* 确保图片正常显示 */
.station-card image {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}
</style>
