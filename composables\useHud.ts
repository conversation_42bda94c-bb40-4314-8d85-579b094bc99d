import { reactive } from 'vue'

export type HudType = 'loading' | 'success' | 'error'

const state = reactive({
  visible: false,
  type: 'loading' as HudType,
  message: '' as string,
  // 活跃中的请求计数（用于并发时统一管理loading）
  activeCount: 0,
  timer: 0 as any
})

function clearTimer() {
  if (state.timer) {
    clearTimeout(state.timer)
    state.timer = 0
  }
}

function show(type: HudType, message = '') {
  clearTimer()
  state.type = type
  state.message = message
  state.visible = true
}

function loading(message = 'Loading...') {
  state.activeCount += 1
  show('loading', message)
}

function done() {
  // 用于请求完成后减少计数
  if (state.activeCount > 0) state.activeCount -= 1
  if (state.activeCount <= 0) {
    state.activeCount = 0
    hide()
  }
}

function success(message = 'Success', duration = 1000) {
  // 完成loading计数
  state.activeCount = 0
  show('success', message)
  clearTimer()
  state.timer = setTimeout(() => hide(), duration)
}

function error(message = 'Error', duration = 1500) {
  // 完成loading计数
  state.activeCount = 0
  show('error', message)
  clearTimer()
  state.timer = setTimeout(() => hide(), duration)
}

function hide() {
  clearTimer()
  state.visible = false
}

export function useHud() {
  return { state, loading, success, error, hide, done }
}

let singleton: ReturnType<typeof useHud> | null = null
export function useGlobalHud() {
  if (!singleton) singleton = useHud()
  return singleton
}

