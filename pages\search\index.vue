<template>
  <view class="search-page">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 头部 -->
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="goBack">
          <text class="iconfont icon-back"></text>
        </view>
        <text class="title">{{ $t('common.search') }}</text>
      </view>
    </view>
    
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <image src="/static/images/search.png" class="search-icon" />
        <input 
          type="text" 
          v-model="searchText" 
          :placeholder="$t('station.searchStations')"
          @focus="onSearchFocus"
          @blur="onSearchBlur"
          class="search-input"
        />
        <view class="clear-btn" v-if="searchText" @click="clearSearch">
          <image src="/static/images/close.png" />
        </view>
      </view>
      <view class="menu-btn" @click="toggleView">
        <image :src="viewMode === 'list' ? '/static/images/list.png' : '/static/images/map-mode.png'" class="menu-icon" />
      </view>
    </view>

    <!-- 充电站列表 -->
    <scroll-view 
      class="station-list" 
      scroll-y 
      style="height: calc(100vh - 250px);"
      @scrolltolower="loadMore"
      refresher-enabled
      @refresherrefresh="onRefresh"
      :refresher-triggered="isRefreshing"
    >
      <!-- 网络错误状态 -->
      <view class="empty-state" v-if="networkError">
        <image src="/static/images/no-network.png" class="empty-icon" />
        <text class="empty-title">{{ $t('error.networkError') }}</text>
        <text class="empty-desc">{{ $t('error.networkError') }}</text>
        <button class="retry-btn" @click="retrySearch">{{ $t('common.retry') }}</button>
      </view>

      <!-- 加载中状态 -->
      <view class="loading-state" v-else-if="loading && stations.length === 0">
        <view class="loading-spinner"></view>
        <text class="loading-text">Searching...</text>
      </view>

      <!-- 初始空状态 - 未开始搜索 -->
      <view class="empty-state" v-else-if="!searchText.trim()">
        <image src="/static/images/search.png" class="empty-icon" />
        <text class="empty-title">Search Charging Stations</text>
        <text class="empty-desc">Enter keywords to find charging stations</text>
      </view>

      <!-- 无搜索结果状态 -->
      <view class="empty-state" v-else-if="!networkError && searchText && stations.length === 0">
        <image src="/static/images/no-results.png" class="empty-icon" />
        <text class="empty-title">No Results Found</text>
        <text class="empty-desc">Try different keywords or filters</text>
      </view>

      <!-- 充电站列表 -->
      <view v-if="!networkError && stations.length > 0" class="station-cards">
        <view 
          class="station-card" 
          v-for="(station, index) in stations" 
          :key="index"
          @click="goToDetail(station)"
          :class="{'animate-in': showAnimation}"
        >
          <view class="station-header">
            <text class="station-name">{{station.name}}</text>
            <view class="distance-wrapper">
              <image src="/static/images/address.png" class="distance-icon" />
              <text class="distance">{{station.distance}}km</text>
            </view>
          </view>
          
          <view class="charging-info">
            <view class="parking-tag">
              <text class="parking-icon">P</text>
              <text>{{station.parking}}</text>
            </view>
            <text class="charging-type">{{station.spec}}</text>
            <view class="status-info">
              <text class="status available">{{ $t('station.available') }}</text>
              <text class="count">{{station.idle}}/{{station.total}}</text>
            </view>
          </view>

          <view class="price-section">
            <view class="regular-price">
              <text class="amount">{{station.price}}</text>
              <text class="unit">F/kWh</text>
            </view>
            <view class="vip-price" v-if="station.vipPrice">
              <image src="/static/images/VIP.png" class="vip-icon" />
              <text class="amount">{{station.vipPrice}}</text>
              <text class="unit">fafc/kWh</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="loading-more" v-if="loading && !networkError && stations.length > 0">
        <view class="loading-spinner"></view>
        <text>Loading more...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="no-more" v-if="!loading && !pagination.hasMore && stations.length > 0">
        <text>No more data</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted, watch, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { getChargingStationList } from '@/api'
import { useUserStore } from '@/store/user'

// 使用国际化
const { t } = useI18n()

// 状态数据
const statusBarHeight = ref(0)
const searchText = ref('')
const viewMode = ref('list')
const showAnimation = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)
const networkError = ref(false)
const loading = ref(false)

// 用户状态
const userStore = useUserStore()

// 用户位置
const userLocation = reactive({
  lat: 48.8566, // 默认位置（巴黎）
  lng: 2.3522
})

// 分页参数
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  hasMore: true
})

// 充电站列表
const stations = ref([])

// 生命周期函数
onMounted(async () => {
  // 获取状态栏高度
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })
  
  // 获取用户位置
  await getCurrentLocation()
  // 初始化时不自动搜索，等待用户输入
  showAnimation.value = true
})

// 监听搜索文本变化，延迟搜索以避免频繁请求
watch(searchText, (newVal) => {
  if (newVal.trim()) {
    debounceSearch(newVal)
  } else {
    // 如果搜索框为空，清空结果列表
    stations.value = []
  }
})

// 防抖搜索函数
let searchTimeout = null
const debounceSearch = (text) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  
  searchTimeout = setTimeout(() => {
    searchStations()
  }, 500)
}

// 获取当前位置（国际坐标系优化）
const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    console.log('开始获取用户位置（搜索页面）...')

    // 使用WGS84坐标系（国际标准）
    uni.getLocation({
      type: 'wgs84', // 使用国际标准坐标系
      isHighAccuracy: true, // 启用高精度定位
      timeout: 15000, // 增加超时时间
      maximumAge: 0, // 不使用缓存位置
      success: (res) => {
        console.log('WGS84定位成功:', res)

        // 验证定位精度和合理性
        const isHighAccuracy = res.accuracy && res.accuracy < 1000
        const isValidCoordinate = Math.abs(res.latitude) <= 90 && Math.abs(res.longitude) <= 180

        if (isHighAccuracy && isValidCoordinate) {
          userLocation.lat = res.latitude
          userLocation.lng = res.longitude
          console.log('✅ 搜索页面高精度定位成功，精度:', res.accuracy + 'm')
          resolve(res)
        } else {
          console.warn('⚠️ 搜索页面定位精度不足，使用默认位置')
          resolve({
            latitude: userLocation.lat,
            longitude: userLocation.lng,
            useDefaultLocation: true
          })
        }
      },
      fail: (err) => {
        console.error('搜索页面WGS84定位失败:', err)
        resolve({
          latitude: userLocation.lat,
          longitude: userLocation.lng,
          useDefaultLocation: true
        })
      }
    })
  })
}

// 方法
const goBack = () => {
  uni.navigateBack()
}

const clearSearch = () => {
  searchText.value = ''
  stations.value = []
}

const toggleView = () => {
  viewMode.value = viewMode.value === 'list' ? 'map' : 'list'
  
  // 如果切换到地图视图，跳转到地图页面
  if (viewMode.value === 'map') {
    uni.navigateTo({
      url: '/pages/map/index'
    })
  }
}

const onSearchFocus = () => {
  // 处理搜索框获得焦点
}

const onSearchBlur = () => {
  // 处理搜索框失去焦点
}

const goToDetail = (station) => {
  // 跳转到充电站详情页
  uni.navigateTo({
    url: `/pages/station-detail/index?plotId=${station.id}&plotNum=${station.plotNum}`
  })
}

const loadMore = async () => {
  if (!hasMore.value || loading.value) return
  
  pagination.pageNum++
  await searchStations(false)
}

const onRefresh = async () => {
  isRefreshing.value = true
  pagination.pageNum = 1
  pagination.hasMore = true
  await searchStations(true)
    isRefreshing.value = false
}

const retrySearch = async () => {
  networkError.value = false
  await searchStations()
}

// 格式化距离
const formatDistance = (distance) => {
  if (distance === undefined || distance === null) return '0'
  
  // 如果已经是字符串且包含单位，直接返回
  if (typeof distance === 'string' && (distance.includes('km') || distance.includes('m'))) {
    return distance.replace('km', '')
  }
  
  // 转换为数字
  const distanceNum = parseFloat(distance)
  
  if (isNaN(distanceNum)) return '0'
  
  // 如果大于等于1公里，显示为公里
  if (distanceNum >= 1000) {
    return (distanceNum / 1000).toFixed(2)
  }
  
  // 否则显示为米，但转换为公里
  return (distanceNum / 1000).toFixed(2)
}

const searchStations = async (reset = true) => {
  if (!searchText.value.trim()) {
    stations.value = []
    return
  }
  
  try {
    loading.value = true
    networkError.value = false
    
    if (reset) {
      pagination.pageNum = 1
      pagination.hasMore = true
    }
    
    if (!pagination.hasMore) {
      loading.value = false
      return
    }
    
    // 构建请求参数
    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      isAsc: 'asc',
      orderByColumn: 'distance',
      plotName: searchText.value, // 使用搜索文本作为plotName参数
      _t: Date.now() // 添加时间戳，确保每次请求都是唯一的
    }

    // 验证坐标有效性并添加coordinate参数
    const isValidLat = userLocation.lat !== null && userLocation.lat !== undefined && !isNaN(userLocation.lat)
    const isValidLng = userLocation.lng !== null && userLocation.lng !== undefined && !isNaN(userLocation.lng)

    if (isValidLat && isValidLng) {
      params.coordinate = `${userLocation.lng},${userLocation.lat}`
      console.log('✅ 搜索使用用户坐标:', params.coordinate)
    } else {
      console.log('⚠️ 搜索时用户坐标无效，详细信息:')
      console.log('  - userLocation.lat:', userLocation.lat, typeof userLocation.lat, 'isValid:', isValidLat)
      console.log('  - userLocation.lng:', userLocation.lng, typeof userLocation.lng, 'isValid:', isValidLng)
      console.log('⚠️ 不传递coordinate参数')
    }
    
    console.log('搜索充电站参数:', params)
    
    // 调用API获取充电站列表
    const response = await getChargingStationList(params, {
      cancelToken: null // 不使用默认的取消令牌
    })
    
    console.log('搜索充电站响应:', response)
    
    if (response && response.code === 200 && response.rows) {
      const newStations = response.rows.map(item => ({
        id: item.plotId,
        name: item.plotName || 'Charging Station',
        distance: formatDistance(item.distance),
        parking: item.freeTime ? `Free${item.freeTime} hour` : 'Parking available',
        spec: `GBT DC ${item.pilePowerType === '5' ? '50KW' : '22KW'}`,
        idle: item.countMap?.fastCount || 0,
        total: item.countMap?.fastSumCount || 0,
        price: item.price || '0',
        vipPrice: item.vipPrice || '0',
        plotNum: item.plotNum,

      }))
      
      if (reset) {
        stations.value = newStations
      } else {
        stations.value = [...stations.value, ...newStations]
      }
      
      pagination.total = response.total || 0
      pagination.hasMore = stations.value.length < pagination.total
    } else {
      if (reset) {
        stations.value = []
      }
    }
  } catch (error) {
    console.error('搜索充电站失败:', error)
    networkError.value = true
    if (reset) {
      stations.value = []
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss">
.search-page {
  min-height: 100vh;
  background: #F8F8F8;
}

.status-bar {
  background: #fff;
}

.header {
  padding: 20rpx 32rpx 0;
  background-color: #fff;
}

.header-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 88rpx;
}

.back-btn {
  position: absolute;
  left: 0;
  font-size: 32rpx;
  color: #000;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000;
}

/* 搜索栏样式 */
.search-section {
  padding: 0 32rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 0;
  background: #FFFFFF;
}

.search-bar {
  flex: 1;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-btn image {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

.menu-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: #F5F5F5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
}

.station-list {
  padding: 16rpx 32rpx;
  box-sizing: border-box;
}

.station-cards {
  padding: 0;
}

.station-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  transform: translateY(20rpx);
  opacity: 0;
  transition: all 0.3s;
}

.station-card.animate-in {
  transform: translateY(0);
  opacity: 1;
}

.station-card:active {
  transform: scale(0.98);
}

.station-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.station-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.distance-wrapper {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.distance-icon {
  width: 24rpx;
  height: 24rpx;
}

.distance {
  font-size: 28rpx;
  color: #666;
}

.charging-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.parking-tag {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 4rpx 12rpx;
  background: #F5F5F5;
  border-radius: 8rpx;
}

.parking-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background: #666;
  color: #FFF;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.parking-tag text {
  font-size: 24rpx;
  color: #666;
}

.charging-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.status-info {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status {
  font-size: 28rpx;
  color: #4CAF50;
}

.status.available {
  color: #4CAF50;
}

.status.occupied {
  color: #FF9800;
}

.status.offline {
  color: #999;
}

.count {
  font-size: 28rpx;
  color: #666;
}

.price-section {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.regular-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.regular-price .amount {
  font-size: 48rpx;
  font-weight: 600;
  color: #FF0000;
}

.regular-price .unit {
  font-size: 24rpx;
  color: #666;
}

.vip-price {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 4rpx 12rpx;
  background: rgba(184, 134, 11, 0.1);
  border-radius: 8rpx;
  width: fit-content;
}

.vip-price .vip-icon {
  width: 32rpx;
  height: 20rpx;
}

.vip-price .amount {
  font-size: 28rpx;
  font-weight: 500;
  color: #B8860B;
}

.vip-price .unit {
  font-size: 24rpx;
  color: #B8860B;
}

.loading-more {
  text-align: center;
  padding: 32rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.empty-icon {
  width: 320rpx;
  height: 320rpx;
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #212529;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #868E96;
  text-align: center;
  margin-bottom: 32rpx;
}

.retry-btn {
  width: 240rpx;
  height: 88rpx;
  line-height: 88rpx;
  background: #FF3B30;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  text-align: center;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF0000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.loading-more .loading-spinner {
  width: 32rpx;
  height: 32rpx;
  margin: 0 16rpx 0 0;
}

.loading-more text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 