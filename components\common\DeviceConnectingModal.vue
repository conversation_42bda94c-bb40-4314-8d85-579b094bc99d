<template>
  <view v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <view class="modal-container" @click.stop>
      <view class="modal-content">
        <!-- 连接中状态 -->
        <view v-if="!isTimeout" class="connecting-state">
          <!-- 加载动画 -->
          <view class="loading-container">
            <view class="loading-spinner">
              <view class="spinner-ring"></view>
              <view class="spinner-ring"></view>
              <view class="spinner-ring"></view>
            </view>
          </view>

          <!-- 标题 -->
          <view class="modal-title">
            {{ $t('popup.deviceConnecting') || '设备连接中' }}
          </view>

          <!-- 提示文本 -->
          <view class="modal-message">
            {{ $t('popup.connectingDevice') || '正在连接设备中...' }}
          </view>
        </view>

        <!-- 超时状态 -->
        <view v-else class="timeout-state">
          <!-- 右上角关闭按钮 -->
          <view class="close-timeout-btn" @click="handleCloseTimeout">
            <text class="close-timeout-icon">×</text>
          </view>

          <!-- 超时图标 -->
          <view class="timeout-icon">
            <text class="icon-text">⚠</text>
          </view>

          <!-- 超时标题 -->
          <view class="modal-title">
            {{ $t('popup.connectionTimeout') || '连接超时' }}
          </view>

          <!-- 超时提示 -->
          <view class="modal-message">
            {{ $t('popup.connectionTimeoutMessage') || '连接设备失败' }}
          </view>

          <!-- 重试按钮 -->
          <view class="single-button-container">
            <view class="action-btn retry-btn" @click="handleRetryConnection">
              {{ $t('popup.retryConnection') || '再次尝试连接' }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'

// 使用国际化
const { t } = useI18n()

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  allowClickOutside: {
    type: Boolean,
    default: false
  },
  // 新增：失败/断开时强制显示重试态
  failed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close', 'timeout', 'retry', 'cancel', 'stop-charging'])

// 响应式数据
const countdown = ref(15)
const isTimeout = ref(false)
const cancelClickCount = ref(0)
let countdownTimer = null
let cancelResetTimer = null

// 计算属性：取消按钮文字
const cancelButtonText = computed(() => {
  if (cancelClickCount.value === 0) {
    return t('popup.cancelOrder') || '取消订单'
  } else {
    return t('popup.clickAgainToCancel') || '再次点击取消订单'
  }
})

// 监听弹窗显示状态
watch(() => props.visible, (visible) => {
  if (visible) {
    // 重置状态
    countdown.value = 15
    // 如果是失败/断开态，直接进入超时视图，显示重试按钮
    isTimeout.value = props.failed ? true : false
    cancelClickCount.value = 0
    clearCancelResetTimer()
    // 失败态不启动倒计时；连接中态启动倒计时
    if (!props.failed) startCountdown()
  } else {
    // 清除定时器
    clearCountdown()
    clearCancelResetTimer()
  }
})

// 开始倒计时
const startCountdown = () => {
  clearCountdown() // 清除之前的定时器

  countdownTimer = setInterval(() => {
    countdown.value--

    if (countdown.value <= 0) {
      // 倒计时结束，显示超时状态
      isTimeout.value = true
      clearCountdown()
      emit('timeout')
    }
  }, 1000)
}

// 清除倒计时
const clearCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 处理点击遮罩层
const handleOverlayClick = () => {
  if (props.allowClickOutside && !isTimeout.value) {
    emit('close')
  }
}

// 清除取消重置定时器
const clearCancelResetTimer = () => {
  if (cancelResetTimer) {
    clearTimeout(cancelResetTimer)
    cancelResetTimer = null
  }
}

// 处理取消订单
const handleCancelOrder = () => {
  cancelClickCount.value++

  if (cancelClickCount.value === 1) {
    // 第一次点击，改变按钮文字，3秒后重置
    clearCancelResetTimer()
    cancelResetTimer = setTimeout(() => {
      cancelClickCount.value = 0
    }, 3000)
  } else if (cancelClickCount.value >= 2) {
    // 第二次点击，执行取消操作
    clearCancelResetTimer()
    emit('cancel')
    emit('close')
  }
}

// 处理重试连接
const handleRetryConnection = () => {
  // 重置状态并重新开始倒计时
  countdown.value = 15
  isTimeout.value = false
  cancelClickCount.value = 0
  clearCancelResetTimer()
  startCountdown()
  emit('retry')
}

// 处理关闭超时弹窗
const handleCloseTimeout = () => {
  console.log('点击关闭超时弹窗，准备停止充电')
  clearCountdown()
  clearCancelResetTimer()
  emit('stop-charging')
}

// 停止倒计时（外部调用）
const stopCountdown = () => {
  clearCountdown()
}

// 暴露方法给父组件
defineExpose({
  stopCountdown
})

// 组件卸载时清除定时器
onUnmounted(() => {
  clearCountdown()
  clearCancelResetTimer()
})
</script>

<style lang="less" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-container {
  width: 600rpx;
  max-width: 90%;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.modal-content {
  padding: 60rpx 40rpx 50rpx;
  text-align: center;
}

.loading-container {
  margin-bottom: 40rpx;
  display: flex;
  justify-content: center;
}

.loading-spinner {
  position: relative;
  width: 80rpx;
  height: 80rpx;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4rpx solid transparent;
  border-top: 4rpx solid #007AFF;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(1) {
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.4s;
  border-top-color: #34C759;
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  left: 10rpx;
}

.spinner-ring:nth-child(3) {
  animation-delay: -0.8s;
  border-top-color: #FF9500;
  width: 40rpx;
  height: 40rpx;
  top: 20rpx;
  left: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.modal-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}



// 超时状态样式
.timeout-state {
  text-align: center;
  position: relative;
}

.timeout-icon {
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  .icon-text {
    font-size: 80rpx;
    color: #FF9500;
    background: rgba(255, 149, 0, 0.1);
    width: 120rpx;
    height: 120rpx;
    border-radius: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid rgba(255, 149, 0, 0.2);
  }
}

// 超时状态关闭按钮
.close-timeout-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
    background: rgba(0, 0, 0, 0.8);
  }

  .close-timeout-icon {
    color: #FFFFFF;
    font-size: 36rpx;
    font-weight: bold;
    line-height: 1;
  }
}

// 单个按钮容器
.single-button-container {
  display: flex;
  justify-content: center;
  margin-top: 50rpx;
  padding: 0 20rpx;
}

// 按钮容器
.button-container {
  display: flex;
  gap: 24rpx;
  margin-top: 50rpx;
  padding: 0 20rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:active::before {
    opacity: 1;
  }
}

.cancel-btn {
  background: linear-gradient(135deg, #FF4757 0%, #FF3742 100%);
  color: #FFFFFF;
  border: 2rpx solid #FF4757;
  box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.3);

  &:active {
    background: linear-gradient(135deg, #FF3742 0%, #E63946 100%);
    box-shadow: 0 2rpx 12rpx rgba(255, 71, 87, 0.4);
  }
}

.retry-btn {
  background: linear-gradient(135deg, #2ED573 0%, #20BF6B 100%);
  color: #FFFFFF;
  border: 2rpx solid #2ED573;
  box-shadow: 0 4rpx 16rpx rgba(46, 213, 115, 0.3);

  &:active {
    background: linear-gradient(135deg, #20BF6B 0%, #0FB9B1 100%);
    box-shadow: 0 2rpx 12rpx rgba(46, 213, 115, 0.4);
  }
}
</style>
