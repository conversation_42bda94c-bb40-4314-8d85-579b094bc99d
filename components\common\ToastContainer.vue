<template>
    <!-- 全局Toast容器 -->
    <CustomToast 
        :visible="toastState.visible"
        :message="toastState.message"
        :title="toastState.title"
        :type="toastState.type"
        :duration="toastState.duration"
        :show-icon="toastState.showIcon"
        :show-close="toastState.showClose"
        :position="toastState.position"
        :confirm-text="toastState.confirmText"
        :cancel-text="toastState.cancelText"
        :close-on-click-overlay="toastState.closeOnClickOverlay"
        @confirm="handleConfirm"
        @cancel="handleCancel"
        @update:visible="handleVisibleUpdate"
    />
</template>

<script setup>
import { inject } from 'vue'
import CustomToast from './CustomToast.vue'

// 注入Toast实例
const toast = inject('toast')
const { toastState } = toast

// 处理visible更新事件
const handleVisibleUpdate = (visible) => {
    toastState.visible = visible
}

// 处理确认事件
const handleConfirm = () => {
    if (toastState.onConfirm) {
        toastState.onConfirm()
    }
}

// 处理取消事件
const handleCancel = () => {
    if (toastState.onCancel) {
        toastState.onCancel()
    }
}
</script>

<style scoped>
/* 容器样式 */
</style>
