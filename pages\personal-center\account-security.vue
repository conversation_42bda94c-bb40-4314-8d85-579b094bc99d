<template>
    <view class="account-security-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ t('settings.accountSecurity') }}</text>
                </view>
            </view>
        </view>
        
        <!-- 安全设置区域 -->
        <view class="security-card">
            <!-- 绑定手机 -->
            <view class="security-item">
                <text class="security-label">{{ t('settings.bindMobilePhone') }}</text>
                <text class="security-value">{{ userPhoneNumber || t('common.notSet') }}</text>
            </view>

            <!-- 分隔线 -->
            <view class="divider"></view>

            <!-- 登录密码 -->
            <view class="security-item" @click="navigateTo('/pages/personal-center/login-password')">
                <text class="security-label">{{ t('settings.loginPassword') }}</text>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
        </view>
        
        <!-- 第三方服务标题 -->
        <view class="section-title">
            <text>{{ t('settings.thirdPartyServices') }}</text>
        </view>
        
        <!-- 第三方服务区域 -->
        <view class="third-party-card">
            <!-- Google -->
            <view class="third-party-item" @click="handleThirdParty('google')">
                <view class="third-party-left">
                    <image src="/static/images/google-small.png" class="third-party-icon"></image>
                    <text class="third-party-name">Google</text>
                </view>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
            
            <!-- 分隔线 -->
            <view class="divider"></view>
            
            <!-- Facebook -->
            <view class="third-party-item" @click="handleThirdParty('facebook')">
                <view class="third-party-left">
                    <image src="/static/images/facebook-small.png" class="third-party-icon"></image>
                    <text class="third-party-name">Facebook</text>
                </view>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
            
            <!-- 分隔线 -->
            <view class="divider"></view>
            
            <!-- Apple -->
            <view class="third-party-item" @click="handleThirdParty('apple')">
                <view class="third-party-left">
                    <image src="/static/images/apple-small.png" class="third-party-icon"></image>
                    <text class="third-party-name">Apple</text>
                </view>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
        </view>
        
        <!-- 账户注销 -->
        <view class="cancellation-card" @click="showCancellationConfirm">
            <text class="cancellation-text">Account Cancellation</text>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { useUserStore } from '@/store/user.js'

// 国际化
const { t } = useI18n()

// 用户store
const userStore = useUserStore()

const statusBarHeight = ref(0)
const userPhoneNumber = ref('')

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight

    // 获取用户手机号
    getUserPhoneNumber()
})

// 获取用户手机号
const getUserPhoneNumber = () => {
    // 从Pinia store获取用户信息
    const userInfo = userStore.getUserInfo
    if (userInfo) {
        // 优先从 user_info 中获取，然后是根级别
        const userInfoData = userInfo.user_info || userInfo
        userPhoneNumber.value = userInfoData.accountPhone || userInfoData.phonenumber || userInfoData.phone || ''
        console.log('从Pinia获取到用户手机号:', userPhoneNumber.value)
    } else {
        // 如果Pinia中没有，尝试从本地存储获取
        try {
            const userInfoStr = uni.getStorageSync('userInfo')
            if (userInfoStr) {
                const userInfo = JSON.parse(userInfoStr)
                const userInfoData = userInfo.user_info || userInfo
                userPhoneNumber.value = userInfoData.accountPhone || userInfoData.phonenumber || userInfoData.phone || ''
                console.log('从本地存储获取到用户手机号:', userPhoneNumber.value)
            }
        } catch (error) {
            console.error('获取用户信息失败:', error)
        }
    }
}

const goBack = () => {
    uni.navigateBack()
}

const navigateTo = (url) => {
    uni.navigateTo({ url })
}

const handleThirdParty = (type) => {
    // 处理第三方服务绑定/解绑
    console.log(`处理${type}第三方服务`)
    uni.showToast({
        title: `${type} 绑定功能开发中`, // 这个页面需要添加 i18n 支持
        icon: 'none',
        duration: 2000
    })
}

const showCancellationConfirm = () => {
    uni.showModal({
        title: t('account.accountDeletion') || 'Suppression de compte', // 这个页面需要添加 i18n 支持
        content: '确定要注销您的账户吗？此操作不可逆，您的所有数据将被删除。',
        confirmText: '确认注销',
        confirmColor: '#FF3B30',
        cancelText: '取消',
        success: function (res) {
            if (res.confirm) {
                // 处理账户注销逻辑
                console.log('用户确认注销账户')
            }
        }
    })
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.account-security-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.security-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .security-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        
        .security-label {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
        }
        
        .security-value {
            font-size: 32rpx;
            color: #999;
        }
        
        .arrow-container {
            .iconfont {
                color: #ccc;
                font-size: 32rpx;
            }
        }
    }
    
    .divider {
        height: 1rpx;
        background-color: #f0f0f0;
        margin-left: 32rpx;
        margin-right: 32rpx;
    }
}

.section-title {
    padding: 32rpx;
    padding-bottom: 16rpx;
    
    text {
        font-size: 28rpx;
        color: #666;
    }
}

.third-party-card {
    margin: 0 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .third-party-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        
        .third-party-left {
            display: flex;
            align-items: center;
            
            .third-party-icon {
                width: 40rpx;
                height: 40rpx;
                margin-right: 16rpx;
            }
            
            .third-party-name {
                font-size: 32rpx;
                color: #333;
            }
        }
        
        .arrow-container {
            .iconfont {
                color: #ccc;
                font-size: 32rpx;
            }
        }
    }
    
    .divider {
        height: 1rpx;
        background-color: #f0f0f0;
        margin-left: 32rpx;
        margin-right: 32rpx;
    }
}

.cancellation-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .cancellation-text {
        font-size: 32rpx;
        color: #333;
        text-align: center;
    }
    
    &:active {
        background-color: #f5f5f5;
    }
}
</style> 