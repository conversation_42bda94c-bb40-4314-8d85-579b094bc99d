<template>
  <view class="action-sheet-container" v-if="visible" @touchmove.stop.prevent>
    <!-- 遮罩层 -->
    <view class="action-sheet-mask" @click="maskClose && close()"></view>
    
    <!-- 内容区域 -->
    <view class="action-sheet-content" :class="{ 'has-title': title }">
      <!-- 标题区域 -->
      <view class="action-sheet-header" v-if="title">
        <text class="action-sheet-title">{{ title }}</text>
        <view class="action-sheet-close" v-if="showClose" @click="close">
          <text class="action-sheet-close-icon">×</text>
        </view>
      </view>
      
      <!-- 描述文本 -->
      <view class="action-sheet-description" v-if="description">
        <text>{{ description }}</text>
      </view>
      
      <!-- 选项列表 -->
      <view class="action-sheet-list">
        <view 
          class="action-sheet-item"
          v-for="(item, index) in items"
          :key="index"
          :class="{ 
            'action-sheet-item-disabled': item.disabled,
            'action-sheet-item-danger': item.type === 'danger',
            'action-sheet-item-warning': item.type === 'warning',
            'action-sheet-item-success': item.type === 'success'
          }"
          @click="handleItemClick(item, index)"
        >
          <image v-if="item.icon" :src="item.icon" class="action-sheet-item-icon" />
          <text class="action-sheet-item-text">{{ item.name }}</text>
          <text v-if="item.description" class="action-sheet-item-description">{{ item.description }}</text>
        </view>
      </view>
      
      <!-- 取消按钮 -->
      <view class="action-sheet-cancel" @click="close">
        <text>{{ cancelText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActionSheet',
  props: {
    // 是否可见
    visible: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 描述文本
    description: {
      type: String,
      default: ''
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: false
    },
    // 选项列表
    items: {
      type: Array,
      default: function() {
        return []
      }
    },
    // 点击遮罩是否关闭
    maskClose: {
      type: Boolean,
      default: true
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    }
  },
  methods: {
    // 关闭菜单
    close() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    
    // 处理选项点击
    handleItemClick(item, index) {
      if (item.disabled) return
      
      this.$emit('select', item, index)
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.action-sheet-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.action-sheet-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  animation: fade-in 0.3s ease;
}

.action-sheet-content {
  position: relative;
  background-color: #f8f8f8;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  transform: translateY(100%);
  animation: slide-up 0.3s ease forwards;
  
  &.has-title {
    .action-sheet-list {
      border-radius: 0;
    }
  }
}

.action-sheet-header {
  position: relative;
  padding: 32rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  
  .action-sheet-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
  }
  
  .action-sheet-close {
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .action-sheet-close-icon {
      font-size: 48rpx;
      color: #999;
      line-height: 1;
    }
  }
}

.action-sheet-description {
  padding: 24rpx 32rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  
  text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
  }
}

.action-sheet-list {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.action-sheet-item {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 32rpx;
    right: 32rpx;
    bottom: 0;
    height: 1rpx;
    background-color: #f0f0f0;
  }
  
  &:active {
    background-color: #f5f5f5;
  }
  
  &.action-sheet-item-disabled {
    opacity: 0.5;
    
    &:active {
      background-color: transparent;
    }
  }
  
  &.action-sheet-item-danger {
    .action-sheet-item-text {
      color: #ff4d4f;
    }
  }
  
  &.action-sheet-item-warning {
    .action-sheet-item-text {
      color: #ff9900;
    }
  }
  
  &.action-sheet-item-success {
    .action-sheet-item-text {
      color: #07c160;
    }
  }
  
  .action-sheet-item-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 16rpx;
  }
  
  .action-sheet-item-text {
    font-size: 32rpx;
    color: #333;
    text-align: center;
  }
  
  .action-sheet-item-description {
    font-size: 24rpx;
    color: #999;
    margin-top: 8rpx;
    text-align: center;
  }
}

.action-sheet-cancel {
  margin-top: 16rpx;
  padding: 32rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  
  text {
    font-size: 32rpx;
    color: #333;
    font-weight: bold;
  }
  
  &:active {
    background-color: #f5f5f5;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}
</style> 