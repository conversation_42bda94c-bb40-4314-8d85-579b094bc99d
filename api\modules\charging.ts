/**
 * 充电桩相关API接口
 */
import Request from '../../utils/request'

// 充电桩相关类型定义
export interface PlotInfo {
  plotId: string
  plotName: string
  address: string
  latitude: number
  longitude: number
  distance?: number
  totalPiles: number
  availablePiles: number
  chargingPiles: ChargingPileInfo[]
  priceInfo?: PriceInfo
  facilities?: string[]
  openTime: string
  closeTime: string
  isOpen24Hours: boolean
  hasParking: boolean
  hasFreeParking: boolean
  parkingFee?: number
  contactPhone?: string
  images?: string[]
}

export interface ChargingPileInfo {
  chargingId: string
  spearId: string
  pileName: string
  connectorType: string
  maxPower: number
  status: 'available' | 'charging' | 'offline' | 'maintenance'
  currentPower?: number
  voltage?: number
  current?: number
}

export interface PriceInfo {
  plotId: string
  timeSlots: PriceTimeSlot[]
  serviceFee: number
  parkingFee?: number
}

export interface PriceTimeSlot {
  startTime: string
  endTime: string
  price: number
  priceType: 'peak' | 'normal' | 'valley'
  description?: string
}

export interface CreateOrderParams {
  chargingId: number
  spearId: number
  carId: number
  isAutoPay: boolean
  isRefund: boolean
  orderType?: string
  isStopWhenFull: boolean
}

export interface ChargingOrder {
  orderNum: string
  orderId: string
  chargingId: string
  spearId: string
  carId: string
  status: 'created' | 'charging' | 'completed' | 'cancelled'
  createTime: string
  startTime?: string
  endTime?: string
  totalAmount: number
  electricityAmount: number
  serviceAmount: number
  parkingAmount?: number
}

// 订单详情接口返回的数据类型
export interface OrderDetail {
  orderId: string
  chargeId: string
  billId: string | null
  openId: string
  phone: string
  orderState: string
  carId: string | null
  carNum: string | null
  paymentMethod: string | null
  payType: string
  plotId: string
  plotName: string
  spearNum: string
  spearName: string
  couponPrice: string | null
  parkId: string
  parkLockId: string
  orderNumber: string
  orderNumList: string | null
  chargeStatus: string
  isFee: string
  degree: number | null
  consumePower: number | null
  chargingCurrent: number | null
  chargingCdgl: number | null
  customPriceId: string | null
  prepaidFees: string
  difference: number | null
  hour: number
  startTime: string | null
  endTime: string | null
  price: string
  mobile: string | null
  code: string | null
  cardNo: string | null
  orderPrice: string
  actualPaymentPrice: string | null
  orderType: string
  outTradeNo: string | null
  realHour: string
  realMinute: string
  realEndTime: string | null
  remainingMinute: string
  startDegree: number
  currentDegree: number
  refundAmount: string
  deviceType: number
  chargeFee: string
  serviceFee: string
  payTime: string | null
  orderSource: number
  isInvoice: boolean
  invoiceState: string | null
  invoiceId: string | null
  inviterList: any | null
  remoteUserEvaluateVo: any | null
  startLog: string | null
  stopLog: string | null
  stopMsg: string | null
  createTime: string
  remoteChargingRealTimeVo: any | null
  sharingRatio: number | null
  shareAmount: number | null
  shareServiceFee: number | null
}

export interface NearbyStationsParams {
  latitude: number
  longitude: number
  radius?: number
  pageNum?: number
  pageSize?: number
  sortBy?: 'distance' | 'price' | 'availability'
}

export interface SearchStationsParams {
  latitude: number
  longitude: number
  radius?: number
  keyword?: string
  connectorTypes?: string[]
  powerRanges?: string[]
  hasParking?: boolean
  isOpen24Hours?: boolean
  hasFreeParking?: boolean
  pageNum?: number
  pageSize?: number
}

export interface StationListParams {
  pageNum: number
  pageSize: number
  isAsc?: string
  orderByColumn?: string
  coordinate: string
}

// 导入通用类型，避免重复定义
import type { ApiResponse, PageResponse } from '../types'

/**
 * 根据充电桩ID查询站点信息
 * @param {string} spearId - 充电桩ID（从扫码URL中提取的第二段）
 * @returns {Promise<ApiResponse<PlotInfo>>} 站点信息
 */
export function queryPlotVoByChargingId(spearId: string): Promise<ApiResponse<PlotInfo>> {
  return Request.get(`/device/applet/plot/queryPlotVoByChargingId/${spearId}`, {
    cancelToken: null,
    headers: {}
  });
}

/**
 * 创建充电订单
 * @param {CreateOrderParams} params - 订单参数
 * @returns {Promise<ApiResponse<ChargingOrder>>} 订单信息
 */
export function createChargingOrder(params: CreateOrderParams): Promise<ApiResponse<ChargingOrder>> {
  console.log('创建充电订单参数:', params);
  return Request.post('/order/applet/createOrder', params);
}

/**
 * 开始充电
 * @param {string} orderNumber - 订单号，从创建订单接口获取
 * @returns {Promise<ApiResponse<boolean>>} 充电结果
 */
export function startCharging(orderNumber: string): Promise<ApiResponse<boolean>> {
  console.log('开始充电订单号:', orderNumber);
  return Request.post(`/order/applet/charging/start/${orderNumber}`,);
}

/**
 * 停止充电
 * @param {string} orderNumber - 订单号，从创建订单接口获取
 * @returns {Promise<ApiResponse<boolean>>} 停止充电结果
 */
export function stopCharging(orderNumber: string): Promise<ApiResponse<boolean>> {
  console.log('停止充电订单号:', orderNumber);
  return Request.post(`/order/applet/charging/stop/${orderNumber}`,);
}

/**
 * 取消订单
 * @param {string} orderNumber - 订单号
 * @returns {Promise<ApiResponse<boolean>>} 取消订单结果
 */
export function cancelOrder(orderNumber: string): Promise<ApiResponse<boolean>> {

  return Request.post(`/order/applet/order/edit/${orderNumber}`);
}

/**
 * 获取订单详情
 * @param {string} orderNumber - 订单号
 * @returns {Promise<ApiResponse<OrderDetail>>} 订单详情
 */
export function getOrderDetail(orderNumber: string): Promise<ApiResponse<OrderDetail>> {
  console.log('获取订单详情，订单号:', orderNumber);
  if (!orderNumber) {
    return Promise.reject(new Error('订单号不能为空'));
  }
  return Request.get(`/app/order/details/${orderNumber}`);
}/**
 * 获取订单详情
 * @param {string} orderID - 订单ID（注意：接口需要的是orderId，不是orderNumber）
 * @returns {Promise<ApiResponse<OrderDetail>>} 订单详情
 */
export function getOrderAppletDetail(orderID: string): Promise<ApiResponse<OrderDetail>> {
  console.log('获取订单详情，订单ID:', orderID);
  if (!orderID) {
    return Promise.reject(new Error('订单ID不能为空'));
  }
  return Request.get(`/order/applet/details/${orderID}`);
}

/**
 * 获取结算订单详情
 * @param {string} orderId - 订单ID（注意：接口需要的是orderId，不是orderNumber）
 * @returns {Promise<ApiResponse<OrderDetail>>} 订单详情
 */
export function getOrderDetailPay(orderId: string): Promise<ApiResponse<OrderDetail>> {
  console.log('获取结算订单详情，订单ID:', orderId);
  if (!orderId) {
    return Promise.reject(new Error('订单ID不能为空'));
  }
  return Request.get(`/order/applet/details/${orderId}`);
}

/**
 * 测试API接口连通性
 * @returns {Promise<ApiResponse<any>>} 测试结果
 */
export function testApiConnection(): Promise<ApiResponse<any>> {
  console.log('测试API接口连通性...');
  return queryPlotVoByChargingId('test');
}

/**
 * 获取附近充电站列表
 * @param {NearbyStationsParams} params - 查询参数
 * @returns {Promise<ApiResponse<PageResponse<PlotInfo>>>} 充电站列表
 */
export function getNearbyChargingStations(params: NearbyStationsParams): Promise<ApiResponse<PageResponse<PlotInfo>>> {
  const defaultParams: Required<NearbyStationsParams> = {
    latitude: params.latitude,
    longitude: params.longitude,
    radius: params.radius || 5000,
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 20,
    sortBy: params.sortBy || 'distance'
  };

  return Request.get('/device/plot/nearby', { params: defaultParams });
}

/**
 * 获取充电站详情
 * @param {string | {id: string, coordinate?: string}} params - 充电站ID或参数对象
 * @returns {Promise<ApiResponse<PlotInfo>>} 充电站详情
 */
export function getChargingStationDetail(params: string | {id: string, coordinate?: string}): Promise<ApiResponse<PlotInfo>> {
  // 兼容旧的字符串参数和新的对象参数
  if (typeof params === 'string') {
    if (!params) {
      return Promise.reject(new Error('充电站ID不能为空'));
    }
    return Request.get(`/device/applet/plot/queryPlotVo/${params}`);
  } else {
    if (!params.id) {
      return Promise.reject(new Error('充电站ID不能为空'));
    }
    // id作为路径参数，coordinate作为查询参数
    const queryParams = params.coordinate ? { coordinate: params.coordinate } : {};
    return Request.get(`/device/applet/plot/queryPlotVo/${params.id}`, { params: queryParams });
  }
}

/**
 * 获取充电站筛选条件
 * @returns {Promise<ApiResponse<any>>} 筛选条件列表
 */
export function getChargingStationFilters(): Promise<ApiResponse<any>> {
  return Request.get('/device/plot/filters');
}

/**
 * 根据筛选条件搜索充电站
 * @param {SearchStationsParams} params - 筛选参数
 * @returns {Promise<ApiResponse<PageResponse<PlotInfo>>>} 充电站列表
 */
export function searchChargingStations(params: SearchStationsParams): Promise<ApiResponse<PageResponse<PlotInfo>>> {
  return Request.post('/device/plot/search', params);
}

/**
 * 获取充电桩点位列表
 * @param {StationListParams} params - 查询参数
 * @param {any} [config] - 请求配置
 * @returns {Promise<ApiResponse<PageResponse<PlotInfo>>>} 充电站列表
 */
export function getChargingStationList(params: StationListParams, config?: any): Promise<ApiResponse<PageResponse<PlotInfo>>> {
  const defaultParams: Required<StationListParams> = {
    pageNum: params.pageNum,
    pageSize: params.pageSize,
    isAsc: params.isAsc || 'asc',
    orderByColumn: params.orderByColumn || 'distance',
    coordinate: params.coordinate
  };

  return Request.get('/device/applet/plot/page', { params: defaultParams, ...config });
}

/**
 * 获取充电桩列表
 * @param {string} plotId - 站点ID
 * @returns {Promise<ApiResponse<ChargingPileInfo[]>>} 充电桩列表
 */
export function getChargingPileList(plotId: string): Promise<ApiResponse<ChargingPileInfo[]>> {
  if (!plotId) {
    return Promise.reject(new Error('站点ID不能为空'));
  }
  return Request.get(`/app/plot/charging/${plotId}`);
}

/**
 * 获取价格详情列表
 * @param {string} plotId - 站点ID
 * @returns {Promise<ApiResponse<PriceInfo>>} 价格详情列表
 */
export function getPriceDetails(plotId: string): Promise<ApiResponse<PriceInfo>> {
  if (!plotId) {
    return Promise.reject(new Error('站点ID不能为空'));
  }
  return Request.get(`/app/plot/price/${plotId}`);
}

/**
 * 从URL中提取spearId参数
 * @param {string} url - 扫描的URL
 * @returns {string|null} 提取的spearId，如果没有找到则返回null
 */
export function extractSpearIdFromUrl(url: string): string | null {
  try {
    console.log('开始解析URL:', url);

    if (!url) {
      console.error('URL为空');
      return null;
    }

    // 检查URL是否包含code参数
    if (url.includes('code=')) {
      // 使用正则表达式提取code参数
      const codeMatch = url.match(/[?&]code=([^&]+)/);

      if (codeMatch && codeMatch[1]) {
        const code = decodeURIComponent(codeMatch[1]);
        console.log('提取到的code参数:', code);

        // 如果code本身是数字，直接返回
        if (/^\d+$/.test(code)) {
          console.log('code是纯数字，直接作为spearId返回:', code);
          return code;
        }

        // 从code中提取第二段作为spearId
        // 根据实际URL格式，可能是用-分隔或/分隔
        let segments: string[];

        if (code.includes('-')) {
          segments = code.split('-');
          if (segments.length >= 2) {
            console.log('从code中提取的spearId:', segments[1]);
            return segments[1]; // 返回第二段作为spearId
          }
        } else if (code.includes('/')) {
          segments = code.split('/');
          if (segments.length >= 2) {
            console.log('从code中提取的spearId:', segments[1]);
            return segments[1]; // 返回第二段作为spearId
          }
        } else {
          // 如果没有分隔符，直接返回code
          console.log('使用完整code作为spearId:', code);
          return code;
        }
      }
    }

    // 尝试解析整个URL是否为纯粹的充电桩ID（可能是数字字符串）
    if (/^\d+$/.test(url)) {
      console.log('URL是纯数字，直接作为spearId返回:', url);
      return url;
    }

    // 如果URL中包含spearId=参数
    if (url.includes('spearId=') || url.includes('pileId=') || url.includes('id=')) {
      // 尝试提取spearId参数
      const idMatch = url.match(/[?&](spearId|pileId|id)=([^&]+)/);
      if (idMatch && idMatch[2]) {
        const id = decodeURIComponent(idMatch[2]);
        console.log('从URL参数中提取的ID:', id);
        return id;
      }
    }

    console.warn('无法从URL中提取spearId:', url);
    return null;
  } catch (error) {
    console.error('提取spearId失败:', error);
    return null;
  }
}
