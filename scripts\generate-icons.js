/**
 * 图标生成脚本
 * 用于从一个高清图标生成多种尺寸的图标文件
 * 
 * 使用方法:
 * 1. 安装依赖: npm install sharp
 * 2. 运行脚本: node scripts/generate-icons.js
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// 图标尺寸配置
const iconSizes = {
  // 通用尺寸
  common: [
    { size: 72, name: 'icon-72.png' },
    { size: 96, name: 'icon-96.png' },
    { size: 128, name: 'icon-128.png' },
    { size: 144, name: 'icon-144.png' }
  ],
  // Android尺寸
  android: [
    { size: 72, name: 'hdpi.png' },      // hdpi
    { size: 96, name: 'xhdpi.png' },     // xhdpi  
    { size: 144, name: 'xxhdpi.png' },   // xxhdpi
    { size: 192, name: 'xxxhdpi.png' }   // xxxhdpi
  ],
  // iOS尺寸
  ios: [
    { size: 120, name: '<EMAIL>' },        // iPhone App @2x
    { size: 180, name: '<EMAIL>' },        // iPhone App @3x
    { size: 152, name: '<EMAIL>' },   // iPad App @2x
    { size: 1024, name: 'appstore.png' },     // App Store
    { size: 58, name: '<EMAIL>' },    // Settings @2x
    { size: 87, name: '<EMAIL>' },    // Settings @3x
    { size: 80, name: '<EMAIL>' },   // Spotlight @2x
    { size: 120, name: '<EMAIL>' },  // Spotlight @3x
    { size: 40, name: '<EMAIL>' }, // Notification @2x
    { size: 60, name: '<EMAIL>' }  // Notification @3x
  ]
};

// 源图标路径
const sourceIcon = path.join(__dirname, '../static/logo.png');
const outputDir = path.join(__dirname, '../static/icons');

// 创建输出目录
function createDirectories() {
  const dirs = [
    path.join(outputDir, 'common'),
    path.join(outputDir, 'android'), 
    path.join(outputDir, 'ios')
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ 创建目录: ${dir}`);
    }
  });
}

// 生成图标
async function generateIcon(inputPath, outputPath, size) {
  try {
    await sharp(inputPath)
      .resize(size, size, {
        kernel: sharp.kernel.lanczos3,
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 } // 透明背景
      })
      .png()
      .toFile(outputPath);
    
    console.log(`✅ 生成图标: ${outputPath} (${size}x${size})`);
  } catch (error) {
    console.error(`❌ 生成图标失败: ${outputPath}`, error.message);
  }
}

// 生成所有图标
async function generateAllIcons() {
  console.log('🚀 开始生成图标...\n');
  
  // 检查源文件是否存在
  if (!fs.existsSync(sourceIcon)) {
    console.error(`❌ 源图标文件不存在: ${sourceIcon}`);
    return;
  }
  
  // 创建目录
  createDirectories();
  
  // 生成通用图标
  console.log('📱 生成通用图标...');
  for (const icon of iconSizes.common) {
    const outputPath = path.join(outputDir, 'common', icon.name);
    await generateIcon(sourceIcon, outputPath, icon.size);
  }
  
  // 生成Android图标
  console.log('\n🤖 生成Android图标...');
  for (const icon of iconSizes.android) {
    const outputPath = path.join(outputDir, 'android', icon.name);
    await generateIcon(sourceIcon, outputPath, icon.size);
  }
  
  // 生成iOS图标
  console.log('\n🍎 生成iOS图标...');
  for (const icon of iconSizes.ios) {
    const outputPath = path.join(outputDir, 'ios', icon.name);
    await generateIcon(sourceIcon, outputPath, icon.size);
  }
  
  console.log('\n✨ 图标生成完成！');
  console.log('\n📋 下一步操作:');
  console.log('1. 检查生成的图标文件');
  console.log('2. 更新 manifest.json 中的图标路径');
  console.log('3. 测试应用图标显示效果');
}

// 更新manifest.json的函数
function updateManifestPaths() {
  const manifestPath = path.join(__dirname, '../manifest.json');
  
  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    // 更新通用图标路径
    manifest.icons = {
      "72": "static/icons/common/icon-72.png",
      "96": "static/icons/common/icon-96.png", 
      "128": "static/icons/common/icon-128.png",
      "144": "static/icons/common/icon-144.png"
    };
    
    // 更新App Plus图标路径
    manifest['app-plus'].icons = {
      "android": {
        "hdpi": "static/icons/android/hdpi.png",
        "xhdpi": "static/icons/android/xhdpi.png",
        "xxhdpi": "static/icons/android/xxhdpi.png",
        "xxxhdpi": "static/icons/android/xxxhdpi.png"
      },
      "ios": {
        "appstore": "static/icons/ios/appstore.png",
        "iphone": {
          "app@2x": "static/icons/ios/<EMAIL>",
          "app@3x": "static/icons/ios/<EMAIL>",
          "settings@2x": "static/icons/ios/<EMAIL>",
          "settings@3x": "static/icons/ios/<EMAIL>",
          "spotlight@2x": "static/icons/ios/<EMAIL>",
          "spotlight@3x": "static/icons/ios/<EMAIL>",
          "notification@2x": "static/icons/ios/<EMAIL>",
          "notification@3x": "static/icons/ios/<EMAIL>"
        },
        "ipad": {
          "app": "static/icons/common/icon-72.png",
          "app@2x": "static/icons/ios/<EMAIL>",
          "settings": "static/icons/common/icon-72.png",
          "settings@2x": "static/icons/ios/<EMAIL>",
          "spotlight": "static/icons/common/icon-72.png",
          "spotlight@2x": "static/icons/ios/<EMAIL>",
          "notification": "static/icons/common/icon-72.png",
          "notification@2x": "static/icons/ios/<EMAIL>",
          "proapp@2x": "static/icons/ios/<EMAIL>"
        }
      }
    };
    
    // 写回文件
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 4));
    console.log('✅ manifest.json 已更新');
    
  } catch (error) {
    console.error('❌ 更新 manifest.json 失败:', error.message);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--update-manifest')) {
    updateManifestPaths();
  } else {
    await generateAllIcons();
    console.log('\n💡 提示: 运行 "node scripts/generate-icons.js --update-manifest" 来自动更新 manifest.json');
  }
}

// 运行脚本
main().catch(console.error);
