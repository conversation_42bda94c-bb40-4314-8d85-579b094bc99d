<template>
  <view class="phone-container">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 页面头部 -->
    <view class="header">
      <view class="header-content">
        <text class="iconfont icon-back back-icon" @click="navigateBack"></text>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- Logo -->
      <view class="logo-section">
        <image class="app-logo" src="/static/images/login_logo.png" mode="aspectFit"></image>
      </view>

      <!-- 注册文本 -->
      <view class="welcome-section">
        <text class="welcome-title">{{ $t('auth.createAccount') }}</text>
        <text class="welcome-subtitle">{{ $t('auth.enterPhoneNumber') }}</text>
      </view>

      <!-- 手机号输入 -->
      <view class="phone-input-section">
        <!-- 手机号输入框 -->
        <view class="phone-input-wrapper">
          <input 
            type="tel" 
            class="phone-input" 
            v-model="phoneNumber"
            :placeholder="$t('auth.phoneNumberPlaceholder')" 
            @input="validatePhoneNumber"
            @focus="inputFocused = true"
            @blur="inputFocused = false"
            maxlength="13"
          />
          <view v-if="phoneNumber && isValidPhone" class="success-icon">
            <text class="iconfont icon-check"></text>
          </view>
        </view>
      </view>

      <!-- 错误提示 -->
      <view v-if="errorMessage" class="error-section">
        <text class="error-text">{{ errorMessage }}</text>
      </view>

      <!-- 继续按钮 -->
      <view class="continue-section">
        <button 
          class="continue-btn" 
          :class="{ active: isValidPhone, disabled: !isValidPhone }"
          :disabled="!isValidPhone"
          @click="continueToVerification"
        >
          {{ $t('common.continue') }}
        </button>
      </view>

      <!-- 登录链接 -->
      <view class="login-link-section">
        <text class="login-text">{{ $t('auth.alreadyHaveAccount') }}</text>
        <text class="login-link" @click="goToLogin">{{ $t('user.login') }}</text>
      </view>
    </view>

    <!-- 全局HUD -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'
import { useGlobalHud } from '@/composables/useHud'
import GlobalHUD from '@/components/common/GlobalHUD.vue'

// 国际化
const { t: $t } = useI18n()

// 全局HUD
const hud = useGlobalHud()

// 状态栏高度
const statusBarHeight = ref(0)

// 表单数据
const phoneNumber = ref('')
const inputFocused = ref(false)
const errorMessage = ref('')
const showCountryPicker = ref(false)

// 验证手机号
const validatePhoneNumber = () => {
  const value = phoneNumber.value
  errorMessage.value = ''
  
  if (!value) {
    return
  }
  
  // 科特迪瓦手机号验证：225开头的完整手机号
  if (!/^225\d{8,10}$/.test(value)) {
    errorMessage.value = $t('auth.invalidPhoneFormat')
  }
}

// 手机号是否有效
const isValidPhone = computed(() => {
  return phoneNumber.value && /^225\d{8,10}$/.test(phoneNumber.value) && !errorMessage.value
})

// 完整手机号
const fullPhoneNumber = computed(() => {
  return phoneNumber.value
})

// 继续到验证码页面
const continueToVerification = () => {
  if (!isValidPhone.value) {
    return
  }

  // 保存手机号到临时存储
  const registerData = {
    phoneNumber: fullPhoneNumber.value,
    step: 1
  }
  
  uni.setStorageSync('registerData', JSON.stringify(registerData))
  
  // 跳转到验证码页面
  uni.navigateTo({
    url: '/pages/auth/register/verification'
  })
}

// 返回上一页
const navigateBack = () => {
  uni.navigateBack()
}

// 跳转到登录页
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/auth/login/index'
  })
}

// 组件挂载时获取状态栏高度
onMounted(() => {
  uni.getSystemInfo({
    success: (res) => {
      statusBarHeight.value = res.statusBarHeight
    }
  })
})
</script>

<style lang="less">
.phone-container {
  min-height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;

  .status-bar {
    background: #fff;
  }

  .header {
    padding: 20rpx 40rpx;
    background-color: #fff;
  }

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    height: 88rpx;

    .back-icon {
      font-size: 32rpx;
      color: #000;
      font-weight: bold;
      padding: 20rpx;
      margin-left: -20rpx;
    }
  }

  .main-content {
    flex: 1;
    padding: 0 40rpx;
    display: flex;
    flex-direction: column;

    .logo-section {
      text-align: center;
      margin: 40rpx 0 60rpx;

      .app-logo {
        width: 120rpx;
        height: 120rpx;
      }
    }

    .welcome-section {
      text-align: center;
      margin: 0 0 60rpx;

      .welcome-title {
        display: block;
        font-size: 48rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
        line-height: 1.3;
      }

      .welcome-subtitle {
        display: block;
        font-size: 32rpx;
        color: #666;
        line-height: 1.4;
      }
    }

    .phone-input-section {
      margin-bottom: 40rpx;


      .phone-input-wrapper {
        position: relative;

        .phone-input {
          width: 100%;
          height: 88rpx;
          background: #fff;
          border: 1rpx solid #E5E5E5;
          border-radius: 8rpx;
          padding: 0 32rpx;
          font-size: 28rpx;
          color: #333;
          box-sizing: border-box;

          &::placeholder {
            color: #999;
            font-size: 26rpx;
          }

          &:focus {
            border-color: #f23030;
          }
        }

        .success-icon {
          position: absolute;
          right: 32rpx;
          top: 50%;
          transform: translateY(-50%);
          
          .iconfont {
            font-size: 32rpx;
            color: #4CAF50;
          }
        }
      }
    }

    .error-section {
      margin-bottom: 40rpx;

      .error-text {
        color: #f23030;
        font-size: 28rpx;
        text-align: center;
      }
    }

    .continue-section {
      margin-bottom: 60rpx;

      .continue-btn {
        width: 100%;
        height: 88rpx;
        background: #ccc;
        color: #999;
        border-radius: 56rpx;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;

        &.active {
          background: #f23030;
          color: #fff;
        }

        &.disabled {
          opacity: 0.6;
        }
      }
    }

    .login-link-section {
      text-align: center;
      margin-top: auto;
      margin-bottom: 60rpx;

      .login-text {
        font-size: 28rpx;
        color: #666;
        margin-right: 8rpx;
      }

      .login-link {
        font-size: 28rpx;
        color: #f23030;
        font-weight: 500;
      }
    }
  }
}
</style>
