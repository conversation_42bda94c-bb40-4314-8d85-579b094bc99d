/**
 * Axios请求封装
 * 基于Promise的HTTP请求工具
 */
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
  AxiosAdapter,
  AxiosPromise
} from 'axios'
import env from '../config/env'
import { getCurrentLanguage } from '../locale/index.js'


// 声明uni-app全局函数和对象
declare const uni: {
  navigateTo: (options: { url: string }) => void;
  request: (options: any) => void;
  [key: string]: any;
};

declare const getCurrentPages: () => Array<{
  route: string;
  [key: string]: any;
}>;

// 响应数据接口
interface ApiResponse<T = any> {
  code: number | string
  message?: string
  msg?: string
  data: T
  success?: boolean
}

// 扩展Error类型以支持responseData
interface BusinessError extends Error {
  responseData?: any
}

// 请求配置扩展接口
interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  loadingText?: string
  showError?: boolean
  retry?: number
  retryDelay?: number
  params?: any
  // 当为 true 时，不注入 Authorization/ClientId
  skipAuth?: boolean
}

// 为 axios 创建 uni-app 适配器
const uniAppAdapter: AxiosAdapter = (config) => {
  return new Promise((resolve, reject) => {
    const { url, method, data, params, headers, baseURL, timeout } = config;

    // 构建完整 URL
    let fullURL = url;
    if (baseURL && url && !url.startsWith('http')) {
      // 确保baseURL和url之间有正确的分隔符
      const separator = baseURL.endsWith('/') || url.startsWith('/') ? '' : '/';
      fullURL = `${baseURL}${separator}${url}`;
    }

    // 转换请求方法为小写
    const methodLowerCase = method?.toLowerCase() || 'get';

    // 处理请求数据
    let requestData = methodLowerCase === 'get' ? params : data

    // 在APP环境下，如果是POST请求且Content-Type是application/json，需要确保数据格式正确
    if (methodLowerCase === 'post' && headers && typeof headers['Content-Type'] === 'string' && headers['Content-Type'].includes('application/json')) {
      // 如果data是字符串，尝试解析为对象
      if (typeof requestData === 'string') {
        try {
          requestData = JSON.parse(requestData)
          console.log('APP环境：将JSON字符串转换为对象:', requestData)
        } catch (e) {
          console.warn('APP环境：JSON解析失败，保持原格式:', e)
        }
      }
    }

    // 详细的请求日志
    console.log('uni.request 发起请求:', {
      fullURL,
      method: method?.toUpperCase() || 'GET',
      data: requestData,
      headers,
      originalData: methodLowerCase === 'get' ? params : data
    });

    // 发起 uni.request 请求
    uni.request({
      url: fullURL,
      data: requestData,
      method: method?.toUpperCase() || 'GET',
      header: headers,
      timeout: timeout || 10000,
      success: (response) => {
        const { data, statusCode, header } = response;

        console.log('uni.request 响应:', {
          url: fullURL,
          statusCode,
          data
        });

        // 构造 axios 响应格式
        const axiosResponse: AxiosResponse = {
          data,
          status: statusCode,
          statusText: statusCode === 200 ? 'OK' : '',
          headers: header,
          config,
          request: null
        };

        resolve(axiosResponse);
      },
      fail: (error) => {
        console.error('uni.request 失败:', {
          url: fullURL,
          error
        });
        
        // 构造标准的axios错误格式
        const axiosError = new Error(error.errMsg || 'Network request failed');
        // 添加request属性但没有response，符合网络错误的特征
        (axiosError as any).request = config;
        (axiosError as any).response = null;
        (axiosError as any).config = config;
        (axiosError as any).code = 'NETWORK_ERROR';
        
        reject(axiosError);
      }
    });
  }) as AxiosPromise;
};

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: env.BASE_API || 'https://arnioci.com/prod-api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  },
  adapter: uniAppAdapter
})

// 请求队列管理
const pendingRequests = new Map<string, () => void>()

// 生成请求唯一标识
const generateRequestKey = (config: AxiosRequestConfig): string => {
  const { method, url, params, data } = config
  return [method, url, JSON.stringify(params), JSON.stringify(data)].join('&')
}

// 添加请求到队列
const addPendingRequest = (config: AxiosRequestConfig) => {
  // 如果明确设置了cancelToken为null，则不进行重复请求检测
  if (config.cancelToken === null) {
    // 删除cancelToken为null的设置，避免影响后续逻辑
    delete config.cancelToken;
    return;
  }

  const requestKey = generateRequestKey(config)
  if (pendingRequests.has(requestKey)) {
    // 取消重复请求
    const cancel = pendingRequests.get(requestKey)!
    cancel()
  }

  // 创建新的取消token
  const source = axios.CancelToken.source()
  config.cancelToken = source.token
  pendingRequests.set(requestKey, source.cancel)
}

// 移除请求
const removePendingRequest = (config: AxiosRequestConfig) => {
  const requestKey = generateRequestKey(config)
  if (pendingRequests.has(requestKey)) {
    pendingRequests.delete(requestKey)
  }
}

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 处理重复请求
    addPendingRequest(config)

    // 添加token和ClientId（支持跳过）
    const customConfig = config as unknown as RequestConfig
    const token = customConfig.skipAuth ? '' : uni.getStorageSync('token')
    const userInfo = customConfig.skipAuth ? '' : uni.getStorageSync('userInfo')
    let clientId = ''

    // 如果有用户信息，尝试获取client_id
    if (userInfo) {
      try {
        const userInfoObj = typeof userInfo === 'string' ? JSON.parse(userInfo) : userInfo
        if (userInfoObj && userInfoObj.client_id) {
          clientId = userInfoObj.client_id
        }
      } catch (e) {
        console.error('解析用户信息失败:', e)
      }
    }

    // 对于登录接口，如果没有clientId，使用默认值
    if (!clientId && config.url?.includes('/auth/login')) {
      clientId = "10e2f22a9910c1393b3027f1ecbf3b6c"
      console.log('登录接口使用默认ClientId:', clientId)
    }

    // 添加headers
    if (config.headers) {
      // 添加ClientId（如果有的话）
      if (clientId) {
        config.headers['ClientId'] = clientId
        console.log('添加ClientId:', clientId)
      } else {
        console.warn('未找到clientId，某些请求可能会失败')
      }

      // 添加Authorization Bearer
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
        console.log('添加授权头:', `Bearer ${token.substring(0, 10)}...`)
      } else {
        console.warn('未找到token，请求可能会被拒绝')
      }

      // 添加Version头部
      try {
        // 只使用本地可修改的版本号
        const headerVersion = uni.getStorageSync('app_version') || '0.0.0'
        config.headers['Version'] = headerVersion
        console.log('添加Version头部(仅存储):', headerVersion)
      } catch (e) {
        console.warn('获取版本号失败:', e)
        config.headers['Version'] = '0.0.0'
      }

      // 添加通用请求头
      config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json;charset=UTF-8'
      config.headers['Accept'] = 'application/json, text/plain, */*'

      // 添加当前语言到请求头
      try {
        const currentLanguage = getCurrentLanguage()
        // 仅允许 en / fr，其它（包括 zh）统一回退为 en
        const normalized = currentLanguage === 'fr' ? 'fr' : 'en'
        const contentLanguage = normalized === 'fr' ? 'fr_FR' : 'en_US'
        config.headers['Content-Language'] = contentLanguage
        console.log('添加语言头:', contentLanguage, '(原始语言:', currentLanguage, ')')
      } catch (e) {
        console.warn('获取当前语言失败:', e)
        // 兜底：强制英文
        config.headers['Content-Language'] = 'en_US'
      }
    } else {
      console.warn('请求头对象不存在，无法添加授权信息')
    }

    // 显示loading（如果需要）
    if (customConfig.showLoading !== false) {
      try {
        // 动态获取全局HUD，避免循环依赖
        // @ts-ignore
        const mod = (globalThis as any).__hudModule || null
        const hud = mod ? mod.useGlobalHud() : null
        // if (hud && hud.loading) hud.loading(customConfig.loadingText || 'Loading...')
      } catch (e) {
        console.log('显示loading:', customConfig.loadingText || 'Loading...')
      }
    }

    // 详细的请求日志
    console.log('发起请求:', {
      url: `${config.baseURL || ''}${config.url || ''}`,
      method: config.method?.toUpperCase(),
      headers: config.headers ? {
        Authorization: config.headers.Authorization ? '已设置' : '未设置',
        ClientId: config.headers.ClientId,
        'Content-Type': config.headers['Content-Type'],
        'Content-Language': config.headers['Content-Language']
      } : '无请求头',
      params: config.params,
      data: config.data
    })

    return config
  },
  (error: any) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, status, config } = response

    // 移除已完成的请求
    removePendingRequest(config)


    // 隐藏loading
    const customConfig = config as RequestConfig
    if (customConfig.showLoading !== false) {
      try {
        // @ts-ignore
        const mod = (globalThis as any).__hudModule || null
        const hud = mod ? mod.useGlobalHud() : null
        if (hud && hud.done) hud.done()
      } catch {}
    }

    // 详细的响应日志
    console.log('请求响应:', {
      url: config.url,
      status,
      data
    })

    // HTTP状态码检查
    if (status === 200) {
      // 业务状态码检查 - 注意这里修改为同时支持code为200和code为"200"的情况
      if (
        data.code === 200 ||
        data.code === "200" ||
        String(data.code) === "200" ||
        data.success === true
      ) {
        // 成功时不再自动显示提示，由具体业务逻辑决定是否显示
        return Promise.resolve(data)
      } else {
        // 业务错误处理
        const errorMsg = data.message || data.msg || 'requestFailed'
        console.error('业务错误:', errorMsg, data)

        // 特殊业务码处理
        if (data.code === 401 || String(data.code) === "401") {
          // 只有在非登录接口时才处理token过期
          if (!config.url?.includes('/auth/login') && !config.url?.includes('/auth/register')) {
            handleTokenExpired()
          }
        }

        // 创建错误对象并保留原始响应数据
        const businessError: BusinessError = new Error(errorMsg)
        // 将原始响应数据附加到错误对象上
        businessError.responseData = data
        return Promise.reject(businessError)
      }
    } else {
      return Promise.reject(new Error(`HTTP Error: ${status}`))
    }
  },
  (error: any) => {
    // 移除失败的请求
    if (error.config) {
      removePendingRequest(error.config)
    }

    // 隐藏loading
    if (error.config) {
      const customConfig = error.config as RequestConfig
      if (customConfig.showLoading !== false) {
        console.log('隐藏loading')
      }
    }

    // 处理取消的请求
    if (axios.isCancel(error)) {
      console.log('请求被取消:', error.message)
      return Promise.reject(new Error('Requête annulée'))
    }

    // 检查网络异常并显示弹窗
    console.log('🔍 [响应拦截器] 开始检查网络异常:', error)
    console.log('🔍 [响应拦截器] 错误详情:', {
      message: error.message,
      request: !!error.request,
      response: !!error.response,
      code: error.code
    })
    
    try {
      // 使用简化版网络异常处理模块
      const simpleNetworkErrorModule = (globalThis as any).__simpleNetworkErrorModule || null
      console.log('🔍 [响应拦截器] 简化网络异常模块:', !!simpleNetworkErrorModule)
      
      if (simpleNetworkErrorModule && simpleNetworkErrorModule.isNetworkError) {
        console.log('✅ [响应拦截器] 检测网络异常')
        const isNetworkErr = simpleNetworkErrorModule.isNetworkError(error)
        console.log('🔍 [响应拦截器] 网络异常检测结果:', isNetworkErr)
        
        if (isNetworkErr) {
          console.log('🚨 [响应拦截器] 检测到网络异常，显示弹窗')
          simpleNetworkErrorModule.handleNetworkError(error)
          // 对于网络异常，返回特定的错误信息，但不显示HUD错误提示
          return Promise.reject(new Error('networkError'))
        } else {
          console.log('ℹ️ [响应拦截器] 不是网络异常，继续正常错误处理')
        }
      } else {
        console.warn('⚠️ [响应拦截器] 简化网络异常处理模块未找到')
      }
    } catch (e) {
      console.warn('❌ [响应拦截器] 网络异常检测失败:', e)
    }

    let errorMessage = 'requestFailed'

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 400:
          errorMessage = 'Erreur de paramètres de requête'
          break
        case 401:
          errorMessage = "unauthorized"
          // 只有在非登录接口时才处理token过期
          if (!error.config?.url?.includes('/auth/login') && !error.config?.url?.includes('/auth/register')) {
            handleTokenExpired()
          }
          break
        case 403:
          errorMessage = "forbidden"
          break
        case 404:
          errorMessage = "notFound"
          break
        case 408:
          errorMessage = "timeout"
          break
        case 500:
          errorMessage = "serverError"
          break
        case 502:
          errorMessage = "badGateway"
          break
        case 503:
          errorMessage = "serviceUnavailable"
          break
        case 504:
          errorMessage = "gatewayTimeout"
          break
        default:
          errorMessage = data?.message || data?.msg || `连接错误${status}`
      }
    } else if (error.request) {
      errorMessage = 'networkError'
    } else {
      errorMessage = error.message || 'Échec de la requête'
    }

    // 显示错误提示（如果需要）
    if (error.config) {
      const customConfig = error.config as RequestConfig
      if (customConfig.showError !== false && errorMessage !== 'networkError') {
        console.error('请求错误提示:', errorMessage)
        try {
          // @ts-ignore
          const mod = (globalThis as any).__hudModule || null
          const hud = mod ? mod.useGlobalHud() : null
          if (hud && hud.error) hud.error(errorMessage)
        } catch {}
      }
    }

    console.error('请求错误:', errorMessage)
    return Promise.reject(new Error(errorMessage))
  }
)

// 处理token过期
const handleTokenExpired = () => {
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')

  // 清空所有待处理的请求
  clearPendingRequests()

  // 清理Toast状态
  try {
    // 使用全局清理函数（避免require类型报错）
    // @ts-ignore
    const mod = (globalThis as any).__toastModule || null
    if (mod && typeof mod.clearGlobalToastState === 'function') {
      mod.clearGlobalToastState()
    }
  } catch (e) {
    console.warn('清理Toast状态失败:', e)
  }

  // 跳转到登录页，使用uni-app的导航API
  // 注意：这里不应该使用window.location，因为在uni-app中会导致整个应用刷新
  try {
    // 判断当前是否已经在登录页，避免重复跳转
    // 统一封装获取路由信息，避免小程序环境警告
    const pages = getCurrentPages()
    const currentPage: any = pages && pages[pages.length - 1]
    const currentRoute = currentPage ? (currentPage.route || currentPage.$page?.fullPath || '') : ''

    // 如果当前不是登录页，才进行跳转
    if (currentRoute && !String(currentRoute).includes('/auth/login/')) {
      uni.navigateTo({ url: '/pages/auth/login/index' })
    }
  } catch (e) {
    console.error('跳转到登录页失败:', e)
  }
}

// 清空所有待处理的请求
const clearPendingRequests = () => {
  for (const [, cancel] of pendingRequests) {
    cancel()
  }
  pendingRequests.clear()
}

// 封装请求方法
class Request {
  /**
   * GET请求（向后兼容）
   * 用法：
   * - get(url, config)
   * - get(url, params, config)
   */
  static get<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return service.get(url, config)
  }

  /**
   *
   * @param url 请求URL
   * @param params 查询参数（通常为null，表示不使用查询参数）
   * @param headers 自定义请求头对象，会与默认headers合并
   * @returns Promise<T> 返回响应数据
   */
  static getWithHeaders<T = any>(
    url: string,
    params: any = null,
    headers: Record<string, string> = {}
  ): Promise<T> {
    // 构建请求配置
    const config: RequestConfig = {
      // 不跳过认证，让拦截器自动添加Authorization和ClientId
      skipAuth: false,
      headers: {
        // 合并自定义headers，这些会在请求拦截器中与默认headers合并
        ...headers
      }
    }

    // 如果有查询参数，添加到配置中
    if (params !== null && params !== undefined) {
      config.params = params
    }

    console.log('getWithHeaders 请求配置:', {
      url,
      params,
      customHeaders: headers,
      finalConfig: config
    })

    return service.get(url, config)
  }

  /**
   * POST请求
   */
  static post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return service.post(url, data, config)
  }

  /**
   * PUT请求
   */
  static put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return service.put(url, data, config)
  }

  /**
   * DELETE请求
   */
  static delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return service.delete(url, config)
  }

  /**
   * PATCH请求
   */
  static patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return service.patch(url, data, config)
  }

  /**
   * 文件上传
   */
  static upload<T = any>(url: string, file: File | FormData, config?: RequestConfig): Promise<T> {
    const formData = file instanceof FormData ? file : (() => {
      const fd = new FormData()
      fd.append('file', file)
      return fd
    })()

    return service.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...(config as any)?.headers
      }
    })
  }

  /**
   * 下载文件
   */
  static async download(url: string, filename?: string, config?: RequestConfig): Promise<void> {
    try {
      console.log('开始下载文件:', url)

      const response = await service.get(url, {
        ...config,
        responseType: 'blob',
        timeout: config?.timeout || 60000, // 默认60秒超时
        onDownloadProgress: (progressEvent) => {
          if (progressEvent.lengthComputable && progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            console.log('下载进度:', percentCompleted + '%')
          }
        }
      })

      console.log('文件下载完成，开始创建下载链接')

      // 创建Blob对象
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)

      console.log('文件下载成功:', filename)
    } catch (error) {
      console.error('文件下载失败:', error)
      throw new Error('文件下载失败: ' + (error.message || '未知错误'))
    }
  }

  /**
   * 并发请求
   */
  static all<T = any>(requests: Promise<T>[]): Promise<T[]> {
    return Promise.all(requests)
  }

  /**
   * 带重试的请求
   */
  static async retry<T = any>(
    requestFn: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ): Promise<T> {
    let lastError: any

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await requestFn()
      } catch (error) {
        lastError = error

        if (i < maxRetries) {
          console.log(`请求失败，正在重试 (${i + 1}/${maxRetries})...`)
          await new Promise(resolve => setTimeout(resolve, retryDelay * (i + 1)))
        }
      }
    }

    throw lastError
  }

  /**
   * 取消所有待处理的请求
   */
  static cancelAllRequests(): void {
    clearPendingRequests()
  }

  /**
   * 获取axios实例
   */
  static getInstance(): AxiosInstance {
    return service
  }

  /**
   * 设置基础URL
   */
  static setBaseURL(baseURL: string): void {
    service.defaults.baseURL = baseURL
  }

  /**
   * 获取基础URL
   */
  static getBaseURL(): string {
    return service.defaults.baseURL || ''
  }

  /**
   * 设置默认超时时间
   */
  static setTimeout(timeout: number): void {
    service.defaults.timeout = timeout
  }

  /**
   * 设置默认请求头
   */
  static setDefaultHeaders(headers: Record<string, string>): void {
    Object.assign(service.defaults.headers, headers)
  }
}

export default Request
export { service, clearPendingRequests, type RequestConfig, type ApiResponse }
