<template>
  <view v-if="visible" class="na-overlay" @touchmove.stop.prevent>
    <view class="na-dialog" :class="[`na-${type}`]">
      <view class="na-icon" v-if="type">
        <view class="na-icon-circle">
          <text v-if="type==='success'" class="na-check">✓</text>
          <text v-else-if="type==='error'" class="na-cross">✕</text>
          <text v-else class="na-info">i</text>
        </view>
      </view>
      <view class="na-title" v-if="title">{{ title }}</view>
      <view class="na-content">{{ message }}</view>
      <view class="na-actions">
        <button class="na-btn" @click="$emit('close')">{{ confirmText }}</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

defineProps({
  visible: { type: <PERSON>olean, default: false },
  title: { type: String, default: '' },
  message: { type: String, default: '' },
  type: { type: String, default: 'info' }, // success | error | info
  confirmText: { type: String, default: 'OK' },
})

defineEmits(['close'])
</script>

<style lang="less" scoped>
.na-overlay {
  position: fixed;
  z-index: 9999;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0, 0, 0, 0.35);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.na-dialog {
  width: 600rpx;
  max-width: 88%;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0,0,0,0.15);
  padding: 40rpx 36rpx 28rpx;
  text-align: center;
}

.na-icon { margin-bottom: 16rpx; }
.na-icon-circle {
  width: 96rpx; height: 96rpx;
  border-radius: 50%;
  margin: 0 auto;
  display: flex; align-items: center; justify-content: center;
  background: linear-gradient(135deg, #F4F7FF, #EAF0FF);
}
.na-check { color: #11c26d; font-size: 56rpx; font-weight: 700; }
.na-cross { color: #ff4d4f; font-size: 56rpx; font-weight: 700; }
.na-info  { color: #3b82f6; font-size: 56rpx; font-weight: 700; }

.na-title {
  font-size: 34rpx;
  color: #111;
  font-weight: 600;
  margin: 8rpx 0 12rpx;
}

.na-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  word-break: break-word;
  white-space: pre-wrap;
  padding: 0 10rpx;
}

.na-actions { margin-top: 28rpx; }
.na-btn {
  width: 360rpx;
  height: 80rpx;
  line-height: 80rpx;
  margin: 0 auto;
  border-radius: 40rpx;
  border: none;
  color: #fff;
  font-weight: 600;
  font-size: 30rpx;
  background: linear-gradient(135deg, #ff4757, #ff3742);
}
</style>

