<template>
  <view class="pin-login-page">
    <!-- 顶部区域 -->
    <view class="header-section">
      <!-- <PERSON>go和标题 -->
      <view class="logo-section">
        <image class="logo" src="/static/images/login_logo.png" mode="aspectFit"></image>
        <text class="welcome-text">{{ $t('user.welcomeBack') || 'Welcome Back' }}</text>
      </view>
    </view>

    <!-- 手机号显示区域 -->
    <view class="phone-section">
      <view class="phone-display">
        <text class="phone-label">{{ $t('user.phone') || 'Phone' }}</text>
        <text class="phone-number">{{ formatPhoneNumber(phoneNumber) }}</text>
      </view>
      <view class="switch-account" @click="switchAccount">
        <text>{{ $t('user.switchAccount') || 'Switch Account' }}</text>
      </view>
    </view>

    <!-- PIN输入区域 -->
    <view class="pin-section">
      <text class="pin-title">{{ $t('user.enterPin') || 'Enter your 4-digit PIN' }}</text>
      
      <!-- PIN输入框显示 -->
      <view class="pin-display">
        <view 
          v-for="(digit, index) in 4" 
          :key="index" 
          class="pin-digit" 
          :class="{ 'filled': pinCode.length > index, 'active': pinCode.length === index }"
        >
          <text v-if="pinCode.length > index">●</text>
        </view>
      </view>

      <!-- 错误提示 -->
      <view v-if="errorMessage" class="error-message">
        <text>{{ errorMessage }}</text>
      </view>
    </view>

    <!-- 数字键盘 -->
    <view class="keypad-section">
      <view class="keypad">
        <!-- 第一行 -->
        <view class="keypad-row">
          <view class="key" @click="inputDigit('1')">
            <text class="key-number">1</text>
          </view>
          <view class="key" @click="inputDigit('2')">
            <text class="key-number">2</text>
            <text class="key-letters">ABC</text>
          </view>
          <view class="key" @click="inputDigit('3')">
            <text class="key-number">3</text>
            <text class="key-letters">DEF</text>
          </view>
        </view>
        
        <!-- 第二行 -->
        <view class="keypad-row">
          <view class="key" @click="inputDigit('4')">
            <text class="key-number">4</text>
            <text class="key-letters">GHI</text>
          </view>
          <view class="key" @click="inputDigit('5')">
            <text class="key-number">5</text>
            <text class="key-letters">JKL</text>
          </view>
          <view class="key" @click="inputDigit('6')">
            <text class="key-number">6</text>
            <text class="key-letters">MNO</text>
          </view>
        </view>
        
        <!-- 第三行 -->
        <view class="keypad-row">
          <view class="key" @click="inputDigit('7')">
            <text class="key-number">7</text>
            <text class="key-letters">PQRS</text>
          </view>
          <view class="key" @click="inputDigit('8')">
            <text class="key-number">8</text>
            <text class="key-letters">TUV</text>
          </view>
          <view class="key" @click="inputDigit('9')">
            <text class="key-number">9</text>
            <text class="key-letters">WXYZ</text>
          </view>
        </view>
        
        <!-- 第四行 -->
        <view class="keypad-row">
          <view class="key empty"></view>
          <view class="key" @click="inputDigit('0')">
            <text class="key-number">0</text>
          </view>
          <view class="key delete" @click="deleteDigit">
            <text class="iconfont icon-delete"></text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作区域 -->
    <view class="footer-section">
      <!-- 忘记PIN链接 -->
      <view class="forgot-pin" @click="forgotPin">
        <text>{{ $t('user.forgotPin') || 'Forgot PIN?' }}</text>
      </view>
      
      <!-- 生物识别登录（如果支持） -->
      <view v-if="supportBiometric" class="biometric-login" @click="biometricLogin">
        <text class="iconfont icon-fingerprint"></text>
        <text>{{ $t('user.biometricLogin') || 'Use biometric' }}</text>
      </view>
    </view>

    <!-- 全局HUD组件 -->
    <GlobalHUD />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { passwordLogin } from '@/api'
import { useUserStore } from '@/store/user'
import { useGlobalHud } from '@/composables/useHud'
import GlobalHUD from '@/components/common/GlobalHUD.vue'

// 使用国际化
const { t } = useI18n()

// 全局HUD
const hud = useGlobalHud()

// 使用用户状态store
const userStore = useUserStore()

// 响应式数据
const phoneNumber = ref('')
const pinCode = ref('')
const errorMessage = ref('')
const isLoading = ref(false)
const supportBiometric = ref(false)

// 页面加载时初始化
onMounted(() => {
  console.log('=== PIN登录页面加载 ===')
  
  // 双重检查手机号：优先使用 uni.storage，其次使用 store
  const uniPhone = uni.getStorageSync('savedPhoneNumber')
  const storePhone = userStore.getSavedPhoneNumber
  
  console.log('UniStorage中的手机号:', uniPhone)
  console.log('Store中的手机号:', storePhone)
  
  phoneNumber.value = uniPhone || storePhone
  
  if (!phoneNumber.value) {
    // 如果没有保存的手机号，跳转到正常登录页面
    console.warn('没有找到保存的手机号，跳转到登录页面')
    uni.reLaunch({
      url: '/pages/auth/login/index'
    })
    return
  }
  
  console.log('PIN登录页面加载，使用手机号:', phoneNumber.value)
  
  // 检查是否支持生物识别（这里可以根据平台和设备支持情况来判断）
  // supportBiometric.value = checkBiometricSupport()
})

// 格式化手机号显示
const formatPhoneNumber = (phone) => {
  if (!phone) return ''
  // 隐藏中间几位数字
  if (phone.length >= 8) {
    return phone.slice(0, 3) + '****' + phone.slice(-4)
  }
  return phone
}

// 输入数字
const inputDigit = (digit) => {
  if (pinCode.value.length < 4) {
    pinCode.value += digit
    errorMessage.value = ''
    
    // 如果输入了4位数字，自动尝试登录
    if (pinCode.value.length === 4) {
      setTimeout(() => {
        attemptLogin()
      }, 200)
    }
  }
}

// 删除数字
const deleteDigit = () => {
  if (pinCode.value.length > 0) {
    pinCode.value = pinCode.value.slice(0, -1)
    errorMessage.value = ''
  }
}

// 尝试登录
const attemptLogin = async () => {
  if (isLoading.value) return
  
  if (pinCode.value.length !== 4) {
    errorMessage.value = t('user.pinRequired') || 'Please enter 4-digit PIN'
    return
  }
  
  isLoading.value = true
  
  try {
    hud.loading(t('loading.authenticating') || 'Authenticating...')
    
    console.log('尝试PIN登录（使用passwordLogin接口）:', { phone: phoneNumber.value, pin: pinCode.value })
    
    // 调用密码登录API，将PIN码作为密码传递
    const response = await passwordLogin({
      clientId: "10e2f22a9910c1393b3027f1ecbf3b6c",
      grantType: "accountPassword",
      password: pinCode.value,
      rememberMe: false,
      tenantId: "000000",
      username: phoneNumber.value
    })
    
    console.log('PIN登录响应:', response)
    
    if (response && (response.code === 200 || response.code === "200") && response.data) {
      // 登录成功，使用登录成功方法
      userStore.loginSuccess(response.data)
      
      // 保存手机号用于下次快速登录
      userStore.savePhoneNumber(phoneNumber.value)
      
      // 同时也保存到本地存储
      uni.setStorageSync('userInfo', JSON.stringify(response.data))
      if (response.data.access_token) {
        uni.setStorageSync('token', response.data.access_token)
      } else if (response.data.token) {
        uni.setStorageSync('token', response.data.token)
      }
      
      // 成功提示
      hud.success(t('toast.loginSuccess') || 'Login successful!', 1000)
      
      console.log('PIN登录成功，准备跳转到首页')
      
      // 跳转到首页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/home/<USER>',
          success: () => {
            console.log('跳转到首页成功')
          },
          fail: (err) => {
            console.error('跳转到首页失败:', err)
          }
        })
      }, 1500)
      
    } else {
      throw new Error(response?.message || 'Login failed')
    }
    
  } catch (error) {
    console.error('PIN登录失败:', error)
    
    hud.done()
    
    // 清除输入的PIN码
    pinCode.value = ''
    
    // 显示错误消息
    let errorMsg = t('user.pinLoginFailed') || 'Invalid PIN. Please try again.'
    
    if (error.response) {
      errorMsg = error.response.data?.message || error.response.data?.msg || errorMsg
    } else if (error.message) {
      errorMsg = error.message
    }
    
    errorMessage.value = errorMsg
    
    // 震动反馈
    uni.vibrateShort()
    
  } finally {
    isLoading.value = false
  }
}

// 返回操作
const goBack = () => {
  uni.navigateBack({
    fail: () => {
      // 如果没有上一页，跳转到登录页面
      uni.reLaunch({
        url: '/pages/auth/login/index'
      })
    }
  })
}

// 切换账户
const switchAccount = () => {  
  // 跳转到登录页面
  uni.reLaunch({
    url: '/pages/auth/login/index'
  })
}

// 忘记PIN
const forgotPin = () => {
  // 跳转到正常登录页面
  uni.navigateTo({
    url: '/pages/auth/login/index'
  })
}

// 生物识别登录
const biometricLogin = () => {
  // 这里可以实现指纹/面容识别登录
  uni.showToast({
    title: t('common.comingSoon') || 'Coming soon',
    icon: 'none'
  })
}
</script>

<style lang="less">
@primary-color: #f23030;
@text-color: #000000;
@text-color-secondary: #999999;
@background-color: #ffffff;
@error-color: #ff4757;

.pin-login-page {
  min-height: 100vh;
  background-color: @background-color;
  display: flex;
  flex-direction: column;
  padding: 0;
  position: relative;
}

.header-section {
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  
  .back-btn {
    position: absolute;
    top: 60rpx;
    left: 40rpx;
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    
    .iconfont {
      font-size: 48rpx;
      color: @text-color;
    }
  }
  
  .logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40rpx;
    
    .logo {
      width: 160rpx;
      height: 120rpx;
      margin-bottom: 24rpx;
    }
    
    .welcome-text {
      font-size: 48rpx;
      font-weight: 600;
      color: @text-color;
      text-align: center;
    }
  }
}

.phone-section {
  padding: 0 40rpx 40rpx;
  
  .phone-display {
    background: #f8f9fa;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 20rpx;
    
    .phone-label {
      font-size: 28rpx;
      color: @text-color-secondary;
      display: block;
      margin-bottom: 8rpx;
    }
    
    .phone-number {
      font-size: 36rpx;
      font-weight: 600;
      color: @text-color;
      letter-spacing: 2rpx;
    }
  }
  
  .switch-account {
    text-align: center;
    
    text {
      font-size: 28rpx;
      color: @primary-color;
      text-decoration: underline;
    }
  }
}

.pin-section {
  padding: 40rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .pin-title {
    font-size: 32rpx;
    color: @text-color;
    margin-bottom: 60rpx;
    text-align: center;
  }
  
  .pin-display {
    display: flex;
    justify-content: center;
    gap: 24rpx;
    margin-bottom: 40rpx;
    
    .pin-digit {
      width: 64rpx;
      height: 64rpx;
      border: 4rpx solid #e1e1e1;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40rpx;
      color: @text-color;
      background: #fff;
      transition: all 0.3s;
      
      &.filled {
        border-color: @primary-color;
        background: @primary-color;
        color: #fff;
      }
      
      &.active {
        border-color: @primary-color;
        box-shadow: 0 0 0 4rpx rgba(242, 48, 48, 0.2);
      }
    }
  }
  
  .error-message {
    margin-top: 20rpx;
    
    text {
      font-size: 28rpx;
      color: @error-color;
      text-align: center;
    }
  }
}

.keypad-section {
  padding: 0 60rpx 40rpx;
  
  .keypad {
    max-width: 480rpx;
    margin: 0 auto;
    
    .keypad-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20rpx;
      
      .key {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        transition: all 0.2s;
        cursor: pointer;
        
        &:active {
          background: #e9ecef;
          transform: scale(0.95);
        }
        
        &.empty {
          background: transparent;
          cursor: default;
          
          &:active {
            transform: none;
          }
        }
        
        &.delete {
          .iconfont {
            font-size: 48rpx;
            color: @text-color-secondary;
          }
        }
        
        .key-number {
          font-size: 48rpx;
          font-weight: 600;
          color: @text-color;
          line-height: 1;
        }
        
        .key-letters {
          font-size: 20rpx;
          color: @text-color-secondary;
          margin-top: 4rpx;
          letter-spacing: 1rpx;
        }
      }
    }
  }
}

.footer-section {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
  
  .forgot-pin {
    text {
      font-size: 28rpx;
      color: @primary-color;
      text-decoration: underline;
    }
  }
  
  .biometric-login {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
    
    .iconfont {
      font-size: 64rpx;
      color: @text-color-secondary;
    }
    
    text {
      font-size: 24rpx;
      color: @text-color-secondary;
    }
  }
}
</style>