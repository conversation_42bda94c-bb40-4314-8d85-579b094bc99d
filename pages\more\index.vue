<template>
    <view class="more-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <!-- 移除标题 -->
                    <view class="right">
                        <text class="iconfont icon-dianhua" @click="handlePhone"></text>
                        <text class="iconfont icon-gerenzhongxin" @click="handleUser"></text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 账户余额卡片 -->
        <view class="balance-card" @click="navigateTo('/pages/account/index')">
            <text class="balance-amount">{{ userBalance }} F</text>
        </view>

        <!-- 功能图标区域 -->
        <view class="feature-grid">
            <view class="feature-item" @click="navigateTo('/pages/account/index')">
                <image src="/static/images/wallet.png" mode="aspectFit"></image>
                <text>{{ $t('account.addMoney') }}</text>
            </view>
            <!-- <view class="feature-item" @click="navigateTo('/pages/vip/index')">
                <image src="/static/images/VIP.png" mode="aspectFit"></image>
                <text>VIP</text>
            </view>
            <view class="feature-item" @click="navigateTo('/pages/coupon/index')">
                <image src="/static/images/coupon-icon.png" mode="aspectFit"></image>
                <text>Coupon</text>
            </view> -->
            <view class="feature-item" @click="navigateTo('/pages/my-car/index')">
                <image src="/static/images/my-car.png" mode="aspectFit"></image>
                <text>{{ $t('account.myCar') }}</text>
            </view>
        </view>

        <!-- 历史记录 -->
        <view class="history-section">
            <text class="history-title">HISTORIQUES</text>

            <!-- 加载状态 -->
            <view v-if="loading" class="loading-container">
                <text class="loading-text">{{ $t('common.loading') }}</text>
            </view>

            <!-- 历史记录列表 -->
            <view v-else class="history-list">
                <!-- 无数据提示 -->
                <view v-if="balanceStatements.length === 0" class="empty-container">
                    <text class="empty-text">{{ $t('account.noTransactions') }}</text>
                </view>

                <!-- 数据列表 -->
                <view
                    v-for="(item, index) in balanceStatements"
                    :key="item.statementId || index"
                    class="history-item"
                    @click="handleItemClick(item)"
                >
                    <view class="history-info">
                        <text class="history-main">{{ getTransactionTitle(item.remark, item.statementType) }}</text>
                        <text class="history-sub">{{ getTransactionSubtitle(item.remark, item.statementType) }}</text>
                    </view>
                    <view class="history-amount-block">
                        <text :class="['history-amount', getAmountClass(item.statementType, item.statementStatus)]">
                            {{ formatAmount(item.amount, item.statementType) }} F
                        </text>
                        <text class="history-meta">{{ item.statementTime }}</text>

                        <!-- 去支付按钮 - 仅当statementType为6且statementStatus为0时显示 -->
                        <view v-if="item.statementType === '6' && item.statementStatus === '0'" class="pay-button-container">
                            <view class="pay-button" @click.stop="handlePayment(item)">
                                <text class="pay-button-text">{{ $t('payment.goToPay') || 'Go to Pay' }}</text>
                            </view>
                        </view>
                    </view>

                    <!-- 箭头图标 - 仅当statementStatus为1时显示（可以点击进入详情） -->
                    <view v-if="item.statementStatus === '1'" class="arrow-icon">
                        <text class="iconfont icon-jinrujiantou"></text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/store/user'
import { useI18n } from '@/composables/useI18n.js'
import { getBalanceDetails } from '@/api'
import { onShow } from '@dcloudio/uni-app'

// 国际化
const { t: $t } = useI18n()

const statusBarHeight = ref(0)
const userStore = useUserStore()

// 账户流水数据
const balanceStatements = ref([])
const loading = ref(false)
const total = ref(0)

// 从 store 中获取用户余额
const userBalance = computed(() => {
    const userInfo = userStore.getUserInfo
    if (userInfo) {
        return userInfo.regularRechargeAccount || userInfo.accountBalance || '0.00'
    }
    return '0.00'
})

// 获取账户流水详情
const fetchBalanceDetails = async () => {
    try {
        loading.value = true
        console.log('开始获取账户流水详情...')

        const response = await getBalanceDetails()
        console.log('账户流水详情响应:', response)

        if (response && response.code === 200) {
            balanceStatements.value = response.rows || []
            total.value = response.total || 0

            console.log('账户流水数据:', balanceStatements.value)
        } else {
            console.error('获取账户流水失败:', response?.msg || '未知错误')
            uni.showToast({
                title: $t('toast.requestFailed') || $t('error.requestFailed') || 'Request failed',
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('获取账户流水详情失败:', error)
        uni.showToast({
            title: $t('toast.networkError') || $t('error.networkError') || 'Network error, please try again',
            icon: 'none'
        })
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

// 无论从哪个页面进入，页面显示时均刷新列表
onShow(() => {
    fetchBalanceDetails()
})

// 格式化金额显示
const formatAmount = (amount, statementType) => {
    const num = parseFloat(amount) || 0
    const formattedNum = Math.abs(num).toFixed(2)

    // 根据statementType确定正负号
    switch (statementType) {
        case '0': // 待支付/消费
            return `-${formattedNum}`
        case '1': // 充值
        case '5': // 赠送金额
            return `+${formattedNum}`
        case '4': // 退款
            return `+${formattedNum}`
        default:
            return formattedNum
    }
}

// 获取金额样式类
const getAmountClass = (statementType, statementStatus) => {
    // 如果statementStatus为1（已支付），显示绿色
    if (statementStatus === '1') {
        return 'paid'
    }

    // 否则按原来的逻辑
    switch (statementType) {
        case '0': // 待支付/消费
            return 'expense'
        case '1': // 充值
        case '5': // 赠送金额
        case '4': // 退款
            return 'income'
        default:
            return ''
    }
}

// 格式化时间显示
// const formatTime = (timeStr) => {
//     if (!timeStr) return ''

//     try {
//         const date = new Date(timeStr)
//         const year = date.getFullYear()
//         const month = String(date.getMonth() + 1).padStart(2, '0')
//         const day = String(date.getDate()).padStart(2, '0')
//         const hours = String(date.getHours()).padStart(2, '0')
//         const minutes = String(date.getMinutes()).padStart(2, '0')

//         return `${day}.${month}.${year} - ${hours}:${minutes}`
//     } catch (error) {
//         console.error('时间格式化失败:', error)
//         return timeStr
//     }
// }

// 获取交易类型显示文本
const getTransactionTitle = (remark, statementType) => {
    if (remark) {
        // 从备注中提取主要信息
        if (remark.includes('账户充值')) {
            return 'ARNIO'
        } else if (remark.includes('微信退款')) {
            return 'WeChat'
        } else if (remark.includes('赠送金额')) {
            return 'ARNIO'
        } else if (remark.includes('充电')) {
            return 'ARNIO'
        }
    }

    // 根据类型返回默认值
    switch (statementType) {
        case '1':
            return 'ARNIO'
        case '4':
            return 'WeChat'
        case '5':
            return 'ARNIO'
        default:
            return 'ARNIO'
    }
}

// 获取交易子标题
const getTransactionSubtitle = (remark, statementType) => {
    if (remark) {
        if (remark.includes('普通充值')) {
            return 'Recharge'
        } else if (remark.includes('活动充值')) {
            return 'Activity Recharge'
        } else if (remark.includes('微信退款')) {
            return 'Refund'
        } else if (remark.includes('赠送金额')) {
            return 'Gift Amount'
        } else if (remark.includes('充电')) {
            return 'Charging'
        }
    }

    switch (statementType) {
        case '1':
            return 'Recharge'
        case '4':
            return 'Refund'
        case '5':
            return 'Gift'
        case '0':
            return 'Charging'
        default:
            return 'Transaction'
    }
}

// 处理项目点击
const handleItemClick = (item) => {
    console.log('点击流水项目:', item)

    // 如果有"Go to Pay"按钮（statementType为6且statementStatus为0），点击列表不跳转
    if (item.statementType === '6' && item.statementStatus === '0') {
        return
    }

    // 只有statementStatus为1的才能进入详情
    if (item.statementStatus === '1') {
        // statementStatus为1，跳转到订单详情页面
        uni.navigateTo({
            url: `/pages/payment/order-detail?orderId=${item.orderId}`
        })
    } else {
        // 其他情况点击没有任何效果
        return
    }
}

// 处理支付按钮点击
const handlePayment = (item) => {
    console.log('点击支付按钮:', item)

    // 跳转到待支付页面，添加fromPayment参数
    uni.navigateTo({
        url: `/pages/payment/order-detail?orderId=${item.orderId}&fromPayment=true`
    })
}

const navigateTo = (url) => {
    uni.navigateTo({ url })
}

const goBack = () => {
    uni.navigateTo({
        url:'/pages/home/<USER>'
    })
}

const handlePhone = () => {
    // 直接跳转到WhatsApp
    console.log('Phone clicked - 跳转到WhatsApp')
    const whatsappUrl = 'https://api.whatsapp.com/send/?phone=2250713294946&text&type=phone_number&app_absent=0'

    // 根据平台打开WhatsApp
    // #ifdef APP-PLUS
    // APP环境：使用 plus.runtime.openURL 打开外部应用
    if (typeof plus !== 'undefined' && plus.runtime) {
        plus.runtime.openURL(whatsappUrl, (error) => {
            console.error('❌ 打开 WhatsApp 失败:', error)
        uni.showToast({
            title: $t('more.cannotOpenWhatsApp') || 'Impossible d\'ouvrir WhatsApp',
            icon: 'none',
            duration: 2000
        })
        })
    } else {
        console.warn('⚠️ plus 对象不可用')
        uni.showToast({
            title: $t('more.cannotOpenWhatsApp') || 'Impossible d\'ouvrir WhatsApp',
            icon: 'none'
        })
    }
    // #endif

    // #ifdef H5
    // H5环境：在新标签页打开
    window.open(whatsappUrl, '_blank')
    // #endif

    // #ifdef MP
    // 小程序环境：提示用户复制链接
    uni.setClipboardData({
        data: whatsappUrl,
        success: () => {
            uni.showToast({
                title: $t('more.whatsappLinkCopied') || 'Lien WhatsApp copié dans le presse-papiers',
                icon: 'success',
                duration: 2000
            })
        }
    })
    // #endif
}

const handleUser = () => {
    // 处理个人中心按钮点击
    uni.navigateTo({
        url: '/pages/personal-center/index'
    })
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.more-container {
    min-height: 100vh;
    background-color: #ffffff;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12px rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .right {
            display: flex;
            align-items: center;
            gap: 40rpx;
            margin-left: auto;

            .iconfont {
                font-size: 50rpx;
                color: #333;
            }
            .icon-gerenzhongxin{
                font-size: 55rpx;
            }
        }
    }
}

.balance-card {
    margin: 25rpx 32rpx 0 32rpx;
    background: #168CFA;
    border-radius: 24rpx;
    padding: 20rpx 0 20rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    box-shadow: 0 4rpx 20rpx rgba(22, 140, 250, 0.2);

    .balance-label {
        color: #fff;
        font-size: 24rpx;
        margin-top: 16rpx;
        margin-bottom: 16rpx;
        letter-spacing: 4rpx;
    }

    .balance-amount {
        color: #fff;
        font-size: 56rpx;
        font-weight: bold;
        letter-spacing: 6rpx;
    }
}

.feature-grid {
    margin: 32rpx 32rpx 0;
    display: flex;
    flex-wrap: wrap;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .feature-item {
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20rpx;
        padding: 0 10rpx;
        box-sizing: border-box;

        image {
            width: 80rpx;
            height: 80rpx;
            margin-bottom: 16rpx;
        }

        text {
            font-size: 24rpx;
            color: #333;
            text-align: center;
            line-height: 1.4;
            word-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
        }
    }
}

.history-section {
    margin: 48rpx 32rpx 32rpx;
    padding-bottom: 40rpx;

    .history-title {
        font-size: 28rpx;
        color: #222;
        font-weight: 600;
        letter-spacing: 2rpx;
        margin-bottom: 24rpx;
        display: block;
    }

    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60rpx 0;

        .loading-text {
            font-size: 28rpx;
            color: #999;
        }
    }

    .empty-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60rpx 0;

        .empty-text {
            font-size: 28rpx;
            color: #999;
        }
    }
    
    .history-list {
        .history-item {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx; // 减少间距从36rpx到16rpx
            padding: 24rpx 20rpx;
            border-radius: 16rpx;
            background: #fff;
            box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.03);
            transition: transform 0.2s;
            position: relative;
            
            &:active {
                transform: scale(0.98);
            }
                  

            
            .history-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                .history-main {
                    font-size: 28rpx;
                    color: #222;
                    font-weight: 600;
                }
                .history-sub {
                    font-size: 24rpx;
                    color: #888;
                }
            }
            .history-amount-block {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                position: relative;

                .history-amount {
                    font-size: 32rpx;
                    font-weight: 600;
                    &.income {
                        color: #1ED69E;
                    }
                    &.expense {
                        color: #FF4D4F;
                    }
                    &.paid {
                        color: #52c41a; // 已支付状态显示绿色
                    }
                }
                .history-meta {
                    font-size: 20rpx;
                    color: #bbb;
                    margin-top: 6rpx;
                }

                .pending-status {
                    margin-top: 8rpx;

                    .pending-text {
                        font-size: 20rpx;
                        color: #FF6B35;
                        background: #FFF2E8;
                        padding: 4rpx 12rpx;
                        border-radius: 12rpx;
                        border: 1rpx solid #FFD4B3;
                    }
                }

                .pay-button-container {
                    margin-top: 8rpx;

                    .pay-button {
                        background: linear-gradient(135deg, #168CFA 0%, #1E90FF 100%);
                        border-radius: 20rpx;
                        padding: 8rpx 20rpx;
                        box-shadow: 0 4rpx 12rpx rgba(22, 140, 250, 0.3);
                        transition: all 0.3s ease;

                        &:active {
                            transform: scale(0.95);
                            box-shadow: 0 2rpx 8rpx rgba(22, 140, 250, 0.4);
                        }

                        .pay-button-text {
                            font-size: 22rpx;
                            color: #ffffff;
                            font-weight: 600;
                            letter-spacing: 1rpx;
                        }
                    }
                }

            .arrow-icon {
                position: absolute;
                right: 20rpx;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40rpx;
                height: 40rpx;

                .iconfont {
                    font-size: 28rpx;
                    color: #999;
                    opacity: 0.6;
                }
            }
            }
        }
    }
}
</style>