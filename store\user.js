import { defineStore } from 'pinia'

// 定义用户状态store
export const useUserStore = defineStore('user', {
  // 状态
  state: () => ({
    // 用户信息
    userInfo: null,
    // 访问令牌
    accessToken: '',
    // 登录状态
    isLoggedIn: false,
    // 用户openId
    openId: '',
    // 存储的手机号(用于快速登录)
    savedPhoneNumber: '',
    // 是否启用快速登录
    enableQuickLogin: false,
    // 其他用户相关状态
    userSettings: {}
  }),
  
  // getters
  getters: {
    // 获取用户是否登录
    loggedIn: (state) => state.isLoggedIn,
    // 获取用户信息
    getUserInfo: (state) => state.userInfo,
    // 获取访问令牌
    getToken: (state) => state.accessToken,
    // 获取用户openId
    getOpenId: (state) => state.openId || (state.userInfo && (state.userInfo.openId || state.userInfo.openid)) || '',
    // 获取存储的手机号
    getSavedPhoneNumber: (state) => state.savedPhoneNumber,
    // 检查是否有保存的手机号用于快速登录
    hasQuickLoginPhone: (state) => Boolean(state.savedPhoneNumber && state.enableQuickLogin),
    // 获取是否启用快速登录
    isQuickLoginEnabled: (state) => state.enableQuickLogin
  },
  
  // actions
  actions: {
    // 从存储中初始化用户状态
    initUserFromStorage() {
      console.log('🔄 [用户Store] ========== 初始化用户状态 ==========')
      
      // 使用持久化插件，但同时从 uni.storage 中恢复手机号信息
      const savedPhone = uni.getStorageSync('savedPhoneNumber')
      const quickLoginEnabled = uni.getStorageSync('enableQuickLogin')
      
      console.log('📱 [初始化] 从 uni.storage 读取的手机号:', savedPhone)
      console.log('⚡ [初始化] 从 uni.storage 读取的快速登录状态:', quickLoginEnabled)
      
      console.log('🏪 [初始化] Store中的原有手机号:', this.savedPhoneNumber)
      console.log('✅ [初始化] Store中的原有快速登录状态:', this.enableQuickLogin)
      
      if (savedPhone && quickLoginEnabled) {
        console.log('✅ [初始化] 发现 uni.storage 中有数据，更新 Store')
        this.savedPhoneNumber = savedPhone
        this.enableQuickLogin = quickLoginEnabled
        console.log('🔄 [初始化] 已更新 Store 中的手机号:', this.savedPhoneNumber)
      } else {
        console.log('⚠️ [初始化] uni.storage 中没有快速登录数据')
      }
      
      console.log('🎯 [初始化] 最终状态 - 手机号:', this.savedPhoneNumber)
      console.log('🎯 [初始化] 最终状态 - 快速登录启用:', this.enableQuickLogin)
      console.log('🎯 [初始化] 最终状态 - hasQuickLoginPhone:', Boolean(this.savedPhoneNumber && this.enableQuickLogin))
      console.log('🔄 [用户Store] ========== 初始化完成 ==========\n')
    },
    
    // 设置用户信息
    setUserInfo(userInfo) {
      this.userInfo = userInfo
      this.isLoggedIn = true
      
      // 设置openId
      if (userInfo && (userInfo.openId || userInfo.openid)) {
        this.openId = userInfo.openId || userInfo.openid
      }
    },
    
    // 更新用户信息
    updateUserInfo(userData) {
      if (!this.userInfo) {
        this.userInfo = userData;
      } else {
        this.userInfo = {
          ...this.userInfo,
          ...userData
        };
      }
    },
    
    // 清除用户信息
    clearUserInfo() {
      this.logout();
    },
    
    // 设置访问令牌
    setToken(token) {
      this.accessToken = token
    },
    
    // 设置openId
    setOpenId(openId) {
      this.openId = openId
    },

    // 保存手机号用于快速登录
    savePhoneNumber(phoneNumber) {
      console.log('💾 [保存手机号] ========== 开始保存 ==========')
      console.log('📱 [保存手机号] 要保存的手机号:', phoneNumber)
      
      this.savedPhoneNumber = phoneNumber
      this.enableQuickLogin = true
      
      // 双重保障：同时使用uni.setStorageSync直接存储
      uni.setStorageSync('savedPhoneNumber', phoneNumber)
      uni.setStorageSync('enableQuickLogin', true)
      
      // 验证存储结果
      const verifyUniPhone = uni.getStorageSync('savedPhoneNumber')
      const verifyUniQuickLogin = uni.getStorageSync('enableQuickLogin')
      
      console.log('✅ [保存手机号] Store中的手机号:', this.savedPhoneNumber)
      console.log('✅ [保存手机号] Store中的快速登录状态:', this.enableQuickLogin)
      console.log('💾 [保存手机号] uni.storage中的手机号:', verifyUniPhone)
      console.log('💾 [保存手机号] uni.storage中的快速登录状态:', verifyUniQuickLogin)
      console.log('🎯 [保存手机号] hasQuickLoginPhone:', Boolean(this.savedPhoneNumber && this.enableQuickLogin))
      
      if (verifyUniPhone === phoneNumber && verifyUniQuickLogin === true) {
        console.log('✅ [保存手机号] 双重存储成功验证通过!')
      } else {
        console.error('❌ [保存手机号] 存储验证失败!')
      }
      
      console.log('💾 [保存手机号] ========== 保存完成 ==========\n')
    },

    // 清除保存的手机号
    clearSavedPhoneNumber() {
      this.savedPhoneNumber = ''
      this.enableQuickLogin = false
      
      // 同时清除uni.storage中的数据
      uni.removeStorageSync('savedPhoneNumber')
      uni.removeStorageSync('enableQuickLogin')
      
      console.log('已清除保存的手机号')
    },

    // 设置快速登录状态
    setQuickLoginEnabled(enabled) {
      this.enableQuickLogin = enabled
      if (!enabled) {
        this.savedPhoneNumber = ''
      }
      console.log('快速登录状态已更新:', enabled ? '启用' : '禁用')
    },
    
    // 登录成功后存储用户数据
    loginSuccess(data) {
      // 存储用户信息
      if (data) {
        // 存储token
        if (data.access_token) {
          this.setToken(data.access_token)
        } else if (data.token) {
          this.setToken(data.token)
        }
        
        // 存储用户信息
        this.setUserInfo(data)
        
        // 存储openId
        if (data.openId || data.openid) {
          this.setOpenId(data.openId || data.openid)
        }
        
        // 更新登录状态
        this.isLoggedIn = true
        
        console.log('用户信息已保存到store:', this.userInfo)
      }
    },
    
    // 退出登录 (可选择是否保留手机号)
    logout(clearPhoneNumber = false) {
      // 清除状态
      this.userInfo = null
      this.accessToken = ''
      this.isLoggedIn = false
      this.openId = ''

      // 根据参数决定是否清除手机号
      if (clearPhoneNumber) {
        this.clearSavedPhoneNumber()
      }

      // 清除本地存储
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      // 如果要清除手机号，则完全清除store
      if (clearPhoneNumber) {
        uni.removeStorageSync('user-store')
        uni.removeStorageSync('savedPhoneNumber')
        uni.removeStorageSync('enableQuickLogin')
      }

      console.log('用户已退出登录', clearPhoneNumber ? '(已清除手机号)' : '(保留手机号)')
    },

    // 快速登录成功后存储用户数据 (专用于4位数密码登录)
    quickLoginSuccess(data, phoneNumber) {
      // 存储用户信息
      if (data) {
        // 存储token
        if (data.access_token) {
          this.setToken(data.access_token)
        } else if (data.token) {
          this.setToken(data.token)
        }
        
        // 存储用户信息
        this.setUserInfo(data)
        
        // 存储openId
        if (data.openId || data.openid) {
          this.setOpenId(data.openId || data.openid)
        }
        
        // 保存手机号用于下次快速登录
        if (phoneNumber) {
          this.savePhoneNumber(phoneNumber)
        }
        
        // 更新登录状态
        this.isLoggedIn = true
        
        console.log('快速登录成功，用户信息已保存到store:', this.userInfo)
        console.log('手机号已保存用于下次快速登录:', phoneNumber)
      }
    }
  },
  
  // 持久化配置
  persist: {
    // 启用持久化
    enabled: true,
    // 存储方式
    strategies: [
      {
        // 存储的键名
        key: 'user-store',
        // 存储的位置，默认是 localStorage
        storage: {
          getItem: (key) => {
            return uni.getStorageSync(key);
          },
          setItem: (key, value) => {
            uni.setStorageSync(key, value);
          },
          removeItem: (key) => {
            uni.removeStorageSync(key);
          }
        },
        // 需要持久化的状态
        paths: ['userInfo', 'accessToken', 'isLoggedIn', 'openId', 'savedPhoneNumber', 'enableQuickLogin', 'userSettings']
      }
    ]
  }
}) 