<template>
	<view class="content">
		<image class="logo" src="/static/logo.png></image>
		<view class="text-area">
			<text class="title">{[object Object] title }}</text>
		</view>
	</view>
</template>

<script setup>
import[object Object]ref } from vue'

const title = ref('Hello')
</script>

<style>
	.content[object Object]
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.logo [object Object]		height:200rpx;
		width: 200rpx;
		margin-top: 200rpx;
		margin-left: auto;
		margin-right: auto;
		margin-bottom:50x;
	}

	.text-area[object Object]
		display: flex;
		justify-content: center;
	}

	.title [object Object]		font-size: 36rpx;
		color: #88
</style>