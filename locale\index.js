/**
 * 国际化配置
 * 支持法语(fr)和英语(en)切换
 */
import { createI18n } from 'vue-i18n'
import fr from './fr.js'
import en from './en.js'

// 获取当前语言设置
const getLocale = () => {
  // 优先从本地存储获取
  const savedLocale = uni.getStorageSync('locale')
  if (savedLocale) {
    // 仅允许 fr / en，其他值（如历史残留 zh）一律清理并走系统语言
    if (savedLocale === 'fr' || savedLocale === 'en') {
      return savedLocale
    } else {
      uni.removeStorageSync('locale')
    }
  }
  
  // 获取系统语言
  const systemInfo = uni.getSystemInfoSync()
  const systemLanguage = systemInfo.language || systemInfo.locale
  
  // 根据系统语言设置默认语言
  if (systemLanguage.startsWith('fr')) {
    return 'fr'
  } else {
    return 'en'
  }
}

// 创建i18n实例
const i18n = createI18n({
  locale: getLocale(),
  fallbackLocale: 'en',
  messages: {
    fr,
    en
  },
  legacy: false, // 使用Composition API模式
  globalInjection: true // 全局注入
})

// 为uni-app添加全局$t方法
// @ts-ignore
uni.$t = (key) => {
  return i18n.global.t(key)
}

// 切换语言的方法
export const switchLanguage = (locale) => {
  i18n.global.locale.value = locale
  uni.setStorageSync('locale', locale)
  
  // 可以在这里添加其他语言切换后的处理逻辑
  console.log('Language switched to:', locale)
}

// 获取当前语言
export const getCurrentLanguage = () => {
  return i18n.global.locale.value
}

// 获取支持的语言列表
export const getSupportedLanguages = () => {
  return [
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'en', name: 'English', flag: '🇺🇸' }
  ]
}

export default i18n
