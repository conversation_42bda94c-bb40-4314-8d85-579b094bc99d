<template>
  <view class="filter-page">
    <!-- 顶部导航栏 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <view class="header">
      <view class="header-content">
        <view class="back-btn" @click="handleBack">
          <text class="iconfont icon-back"></text>
        </view>
        <text class="title">{{ t('filter.filter') || 'Filtres' }}</text>
      </view>
    </view>

    <!-- 筛选内容区域 -->
    <view class="filter-content">
      <!-- 充电方式 -->
      <view class="filter-section">
        <text class="section-title">{{ t('filter.chargingMethod') }}</text>
        <view class="option-group charging-method-group">
          <view 
            class="option-btn" 
            :class="{ 'active': selectedChargingMethod === 'fast' }"
            @tap="selectChargingMethod('fast')"
          >
            <text :class="{ 'active': selectedChargingMethod === 'fast' }">{{ t('filter.fastCharging') }}</text>
          </view>
          <view
            class="option-btn"
            :class="{ 'active': selectedChargingMethod === 'slow' }"
            @tap="selectChargingMethod('slow')"
          >
            <text :class="{ 'active': selectedChargingMethod === 'slow' }">{{ t('filter.slowCharging') }}</text>
          </view>
        </view>
      </view>

      <!-- 停车费用 -->
      <view class="filter-section">
        <text class="section-title">{{ t('filter.parkingFee') }}</text>
        <view class="option-group parking-fee-group">
          <view 
            class="option-btn" 
            :class="{ 'active': selectedParkingFee === 'hour1' }"
            @tap="selectParkingFee('hour1')"
          >
            <text :class="{ 'active': selectedParkingFee === 'hour1' }">{{ t('filter.free1Hour') }}</text>
          </view>
          <view
            class="option-btn"
            :class="{ 'active': selectedParkingFee === 'halfHour' }"
            @tap="selectParkingFee('halfHour')"
          >
            <text :class="{ 'active': selectedParkingFee === 'halfHour' }">{{ t('filter.freeHalfHour') }}</text>
          </view>
          <view
            class="option-btn full-width"
            :class="{ 'active': selectedParkingFee === 'untilEnd' }"
            @tap="selectParkingFee('untilEnd')"
          >
            <text :class="{ 'active': selectedParkingFee === 'untilEnd' }">{{ t('filter.freeUntilEnd') }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="footer-btn default-btn" @tap="handleDefault">
        <text>{{ t('filter.default') || 'par défaut' }}</text>
      </view>
      <view class="footer-btn confirm-btn" @tap="handleConfirm">
        <text>{{ t('common.confirm') }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'

// 国际化
const { t } = useI18n()

// 状态栏高度
const statusBarHeight = ref(0)

// 选中的充电方式
const selectedChargingMethod = ref('fast')

// 选中的停车费用
const selectedParkingFee = ref('hour1')

// 页面加载时获取状态栏高度
onMounted(() => {
  const sysInfo = uni.getSystemInfoSync()
  statusBarHeight.value = sysInfo.statusBarHeight
})

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 选择充电方式
const selectChargingMethod = (method) => {
  selectedChargingMethod.value = method
}

// 选择停车费用
const selectParkingFee = (fee) => {
  selectedParkingFee.value = fee
}

// 恢复默认设置
const handleDefault = () => {
  selectedChargingMethod.value = 'fast'
  selectedParkingFee.value = 'hour1'
}

// 确认筛选
const handleConfirm = () => {
  // 这里可以添加筛选逻辑
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.filter-page {
  min-height: 100vh;
  background: #F7FBFF;
  display: flex;
  flex-direction: column;
}

.status-bar {
  background-color: #fff;
}

.header {
  padding: 20rpx 32rpx 0;
  background-color: #fff;

  .header-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 88rpx;

    .back-btn {
      position: absolute;
      left: 0;
      font-size: 32rpx;
      color: #000;
      cursor: pointer;
    }

    .title {
      font-size: 36rpx;
      font-weight: 600;
      color: #000;
    }
  }
}

.filter-content {
  flex: 1;
  padding: 32rpx;
}

.filter-section {
  margin-bottom: 48rpx;

  .section-title {
    font-size: 30rpx;
    color: #444;
    margin-bottom: 40rpx;
    font-weight: 600;
  }
}

.option-group {
	margin-top: 20rpx;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

/* 充电方式选项 - 两列布局 */
.charging-method-group {
  .option-btn {
    height: 76rpx;
    border-radius: 20rpx;
  }
}

/* 停车费用选项 - 两列布局，最后一项占满 */
.parking-fee-group {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;

  .option-btn {
    height: 76rpx;
    border-radius: 20rpx;
  }
  
  .full-width {
    grid-column: 1 / 2;
    width: 100%;
    margin: 0;
  }
}

.option-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F5F5F5;

  text {
    font-size: 26rpx;
    color: #333;
    text-align: center;
  }

  &.active {
    background: #E5F3FF;
  }

  text.active {
    color: #0088FF;
  }
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  display: flex;
  gap: 24rpx;
  background: #FFFFFF;
  border-top: 1rpx solid #F0F0F0;
}

.footer-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 44rpx;

  text {
    font-size: 30rpx;
    font-weight: 500;
  }
}

.default-btn {
  background: #F5F5F5;
  
  text {
    color: #999;
  }
}

.confirm-btn {
  background: #FF4141;
  
  text {
    color: #FFFFFF;
  }
}
</style> 