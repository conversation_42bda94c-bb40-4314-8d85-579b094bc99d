<template>
    <view class="payment-settings-container" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
        <!-- 头部导航栏 -->
        <view class="fixed-header">
            <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
            <view class="header">
                <view class="header-content">
                    <view class="back-btn" @click="goBack">
                        <text class="iconfont icon-back"></text>
                    </view>
                    <text class="title">{{ t('settings.paymentSettings') || 'Paramètres de paiement' }}</text>
                </view>
            </view>
        </view>
        
        <!-- 支付密码设置 -->
        <view class="settings-card">
            <view class="settings-item" @click="navigateTo('/pages/personal-center/payment-password-reset')">
                <text class="settings-label">{{ t('settings.paymentPassword') || 'Code de paiement' }}</text>
                <view class="arrow-container">
                    <text class="iconfont icon-jinrujiantou"></text>
                </view>
            </view>
        </view>
        
        <!-- 暂时隐藏：优惠券自动抵扣设置区域 -->
        <template v-if="false">
            <!-- 优惠券自动抵扣设置标题 -->
            <view class="section-title">
                <text>Coupon Automatic Deduction Settings</text>
            </view>
            
            <!-- 优惠券自动抵扣设置 -->
            <view class="settings-card">
                <!-- 优先使用即将过期的优惠券 -->
                <view class="settings-item" @click="toggleExpiringSetting">
                    <text class="settings-label">Give priority to using coupons that are about to expire.</text>
                    <view class="checkbox-container">
                        <image 
                            :src="useExpiringFirst ? '/static/images/checked.png' : '/static/images/unchecked.png'" 
                            class="checkbox-icon"
                            mode="aspectFit"
                        ></image>
                    </view>
                </view>
                
                <!-- 分隔线 -->
                <view class="divider"></view>
                
                <!-- 优先使用抵扣金额较大的优惠券 -->
                <view class="settings-item" @click="toggleLargerAmountSetting">
                    <text class="settings-label">Give priority to using the ones with a larger deduction amount</text>
                    <view class="checkbox-container">
                        <image 
                            :src="useLargerAmountFirst ? '/static/images/checked.png' : '/static/images/unchecked.png'" 
                            class="checkbox-icon"
                            mode="aspectFit"
                        ></image>
                    </view>
                </view>
            </view>
        </template>
    </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from '@/composables/useI18n.js'

// 国际化
const { t } = useI18n()

const statusBarHeight = ref(0)
const useExpiringFirst = ref(true)
const useLargerAmountFirst = ref(false)

onMounted(() => {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight
})

const goBack = () => {
    uni.navigateBack()
}

const navigateTo = (url) => {
    uni.navigateTo({ url })
}

const toggleExpiringSetting = () => {
    useExpiringFirst.value = !useExpiringFirst.value
    if (useExpiringFirst.value) {
        useLargerAmountFirst.value = false
    }
    saveSettings()
}

const toggleLargerAmountSetting = () => {
    useLargerAmountFirst.value = !useLargerAmountFirst.value
    if (useLargerAmountFirst.value) {
        useExpiringFirst.value = false
    }
    saveSettings()
}

const saveSettings = () => {
    // 保存设置到本地存储或发送到服务器
    uni.showToast({
        title: t('settings.settingsSaved') || 'Paramètres enregistrés',
        icon: 'success',
        duration: 2000
    })
}
</script>

<style lang="less">
@import '@/static/iconfont/iconfont.css';

.payment-settings-container {
    min-height: 100vh;
    background-color: #f8f8f8;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: #fff;
    box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.04);
}

.status-bar {
    background-color: #fff;
    width: 100%;
}

.header {
    background: #fff;
    width: 100%;
    
    .header-content {
        height: 44px;
        display: flex;
        align-items: center;
        position: relative;
        padding: 0 32rpx;
        border-bottom: 1rpx solid #f0f0f0;
        
        .back-btn {
            width: 88rpx;
            height: 44px;
            display: flex;
            align-items: center;
            
            .iconfont {
                font-size: 40rpx;
                color: #333;
            }
        }
        
        .title {
            position: absolute;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
    }
}

.section-title {
    padding: 32rpx;
    padding-bottom: 16rpx;
    
    text {
        font-size: 28rpx;
        color: #999;
    }
}

.settings-card {
    margin: 32rpx;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .settings-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 32rpx;
        
        .settings-label {
            font-size: 32rpx;
            color: #333;
            flex: 1;
            padding-right: 16rpx;
        }
        
        .arrow-container {
            .iconfont {
                color: #ccc;
                font-size: 32rpx;
            }
        }
        
        .checkbox-container {
            .checkbox-icon {
                width: 40rpx;
                height: 40rpx;
            }
        }
    }
    
    .divider {
        height: 1rpx;
        background-color: #f0f0f0;
        margin-left: 32rpx;
        margin-right: 32rpx;
    }
}
</style> 