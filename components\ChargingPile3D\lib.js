/********** 小程序端 *************/
// #ifdef MP
export * from 'three-platformize'
export { OrbitControls } from 'three-platformize/examples/jsm/controls/OrbitControls.js'
export { FBXLoader } from 'three-platformize/examples/jsm/loaders/FBXLoader.js'
export { GLTFLoader } from 'three-platformize/examples/jsm/loaders/GLTFLoader.js'
// #endif

/********** APP、H5 *************/
// #ifndef MP
export * from 'three'
export { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
export { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js'
export { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
// #endif

// 设备信息
const deviceInfo = uni.getDeviceInfo()
const devicePixelRatio = Math.max(deviceInfo.devicePixelRatio || 1, 2)

export { devicePixelRatio }