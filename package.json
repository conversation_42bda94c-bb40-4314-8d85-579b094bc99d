{"dependencies": {"@dcloudio/uni-app": "3.0.0-4070520250711001", "@dcloudio/uni-app-plus": "3.0.0-4070520250711001", "@dcloudio/uni-h5": "3.0.0-4070520250711001", "@dcloudio/uni-mp-weixin": "3.0.0-4070520250711001", "@dcloudio/vite-plugin-uni": "3.0.0-4070520250711001", "@googlemaps/js-api-loader": "^1.16.10", "@vitejs/plugin-vue": "3.2.0", "axios": "^1.10.0", "deep-pick-omit": "^1.0.1", "destr": "^2.0.3", "esbuild": "^0.25.8", "less": "^4.4.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "sass": "^1.72.0", "three": "^0.164.1", "three-platformize": "^1.133.3", "vite": "3.2.5", "vue": "^3.5.17", "vue-i18n": "^9.14.5"}, "devDependencies": {"sharp": "^0.33.0"}, "scripts": {"dev:h5": "uni", "dev:app": "uni -p app", "build:h5": "uni build", "build:app": "uni build -p app", "generate-icons": "node scripts/generate-icons.js", "update-manifest": "node scripts/generate-icons.js --update-manifest", "setup-icons": "npm run generate-icons && npm run update-manifest"}}