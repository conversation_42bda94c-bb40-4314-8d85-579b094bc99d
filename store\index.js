import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { destr, deepPickUnsafe, deepOmitUnsafe } from '../utils/polyfill'

// 修补pinia-plugin-persistedstate
const originalPlugin = piniaPluginPersistedstate;
if (originalPlugin && typeof originalPlugin === 'function') {
  // 检查是否有destr依赖问题
  try {
    // #ifdef H5
    // 尝试访问destr
    if (typeof window !== 'undefined' && !window.destr) {
      window.destr = destr;
    }

    if (typeof window !== 'undefined' && !window.deepPickUnsafe) {
      window.deepPickUnsafe = deepPickUnsafe;
    }

    if (typeof window !== 'undefined' && !window.deepOmitUnsafe) {
      window.deepOmitUnsafe = deepOmitUnsafe;
    }
    // #endif

    // #ifndef H5
    // 非H5环境不需要window对象的修补
    console.log('非H5环境，跳过window对象修补');
    // #endif
  } catch (e) {
    console.error('修补pinia-plugin-persistedstate失败:', e);
  }
}

// 创建 pinia 实例
const pinia = createPinia()

// 使用持久化插件
pinia.use(piniaPluginPersistedstate)

export default pinia