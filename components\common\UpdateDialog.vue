<template>
  <view v-if="visible" class="update-dialog-overlay" @click="handleOverlayClick">
    <view class="update-dialog" @click.stop>
      <view class="dialog-header">
        <text class="dialog-title">{{ $t('update.title') }}</text>
        <view class="close-btn" @click="handleClose" v-if="!isMandatoryUpdate">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <view class="dialog-content">
        <view class="version-info">
          <text class="version-label">{{ $t('update.newVersion') }}</text>
          <text class="version-number">{{ versionNum }}</text>
        </view>
        
        <view class="update-description">
          <text class="description-label">{{ $t('update.description') }}</text>
          <rich-text class="description-content" :nodes="versionDescription"></rich-text>
        </view>
      </view>
      
      <view class="dialog-footer">
        <button 
          class="update-btn" 
          @click="handleUpdate"
          :disabled="updating"
        >
          <text v-if="updating" class="loading-text">{{ $t('update.updating') }}</text>
          <text v-else>{{ $t('update.updateNow') }}</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  versionNum: {
    type: String,
    default: ''
  },
  versionDescription: {
    type: String,
    default: ''
  },
  downloadUrl: {
    type: String,
    default: ''
  },
  isMandatoryUpdate: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'update'])

const updating = ref(false)

const handleClose = () => {
  if (!props.isMandatoryUpdate) {
    emit('close')
  }
}

const handleOverlayClick = () => {
  // 点击遮罩层不关闭弹窗，只能通过点击叉号关闭
  // 移除原有的关闭逻辑
}

const handleUpdate = () => {
  updating.value = true
  emit('update', props.downloadUrl)
}

watch(() => props.visible, (newVal) => {
  if (!newVal) {
    updating.value = false
  }
})
</script>

<style lang="less" scoped>
.update-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.update-dialog {
  width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  margin: 40rpx;
}

.dialog-header {
  padding: 40rpx 40rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1rpx solid #f0f0f0;
  
  .dialog-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  
  .close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .close-icon {
      font-size: 48rpx;
      color: #999;
      line-height: 1;
    }
  }
}

.dialog-content {
  padding: 40rpx;
  
  .version-info {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    
    .version-label {
      font-size: 28rpx;
      color: #666;
      margin-right: 20rpx;
    }
    
    .version-number {
      font-size: 32rpx;
      font-weight: bold;
      color: #ff4757;
    }
  }
  
  .update-description {
    .description-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 20rpx;
      display: block;
    }
    
    .description-content {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
    }
  }
}

.dialog-footer {
  padding: 0 40rpx 40rpx;
  
  .update-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #ff4757, #ff3742);
    border: none;
    border-radius: 44rpx;
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.7;
    }
    
    .loading-text {
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        width: 32rpx;
        height: 32rpx;
        border: 3rpx solid transparent;
        border-top: 3rpx solid #fff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-right: 16rpx;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
