<template>
    <view class="photo-action-sheet" v-if="visible" @click="handleMaskClick">
        <view class="action-sheet-content" @click.stop>
            <!-- 标题 -->
            <view class="action-sheet-header">
                <text class="action-sheet-title">{{ title || '选择照片' }}</text>
            </view>
            
            <!-- 选项列表 -->
            <view class="action-sheet-options">
                <view class="action-option" @click="handleTakePhoto">
                    <text class="option-text">{{ takePhotoText || '拍照' }}</text>
                </view>

                <view class="action-option" @click="handleChooseFromAlbum">
                    <text class="option-text">{{ chooseFromAlbumText || '从相册选择' }}</text>
                </view>
            </view>
            
            <!-- 取消按钮 -->
            <view class="action-sheet-cancel" @click="handleCancel">
                <text class="cancel-text">{{ cancelText || '取消' }}</text>
            </view>
        </view>
    </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: '选择照片'
    },
    takePhotoText: {
        type: String,
        default: '拍照'
    },
    chooseFromAlbumText: {
        type: String,
        default: '从相册选择'
    },
    cancelText: {
        type: String,
        default: '取消'
    },
    maskClose: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['update:visible', 'takePhoto', 'chooseFromAlbum', 'cancel'])

const handleMaskClick = () => {
    if (props.maskClose) {
        emit('update:visible', false)
        emit('cancel')
    }
}

const handleTakePhoto = () => {
    emit('takePhoto')
    emit('update:visible', false)
}

const handleChooseFromAlbum = () => {
    emit('chooseFromAlbum')
    emit('update:visible', false)
}

const handleCancel = () => {
    emit('cancel')
    emit('update:visible', false)
}
</script>

<style lang="less" scoped>
@import '@/static/iconfont/iconfont.css';

.photo-action-sheet {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: flex-end;
    animation: fadeIn 0.3s ease-out;
}

.action-sheet-content {
    width: 100%;
    background: #fff;
    border-radius: 32rpx 32rpx 0 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    animation: slideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.action-sheet-header {
    padding: 40rpx 32rpx 24rpx;
    text-align: center;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 16rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 80rpx;
        height: 8rpx;
        background: #e0e0e0;
        border-radius: 4rpx;
    }

    .action-sheet-title {
        font-size: 34rpx;
        font-weight: 600;
        color: #333;
        margin-top: 8rpx;
    }
}

.action-sheet-options {
    padding: 24rpx 0 16rpx;
}

.action-option {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx 48rpx;
    margin: 0 32rpx 20rpx;
    background: #fff;
    transition: all 0.2s ease;
    border-bottom:1px solid #ece4e4 ;

    &:active {
        background-color: #f8f8f8;
        border-color: #e0e0e0;
        transform: scale(0.98);
    }

    &:first-child {
        margin-top: 8rpx;
    }

    .option-text {
        font-size: 36rpx;
        color: #333;
        font-weight: 500;
        letter-spacing: 1rpx;
    }
}

.action-sheet-cancel {
    margin: 12rpx 32rpx 0;
    padding: 30rpx;
    text-align: center;
    transition: all 0.2s ease;
    background: #fff;
    .cancel-text {
        font-size: 36rpx;
        color: #666;
        font-weight: 500;
        letter-spacing: 1rpx;
    }

    &:active {
        background-color: #f0f0f0;
        transform: scale(0.98);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(4px);
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0.8;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}


</style>
