<template>
  <view class="welcome-container">
    <!-- Logo -->
    <view class="logo-container">
      <image class="logo" src="/static/images/login_logo.png" mode="aspectFit"></image>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue';
import { useUserStore } from '@/store/user';

// 获取用户store
const userStore = useUserStore()

// 检查用户登录状态和快速登录配置
function checkUserLoginStatus() {
  console.log('🎬 [欢迎页面] ========== 开始检查用户登录状态 ==========')
  
  // 检查是否已经登录
  if (userStore.loggedIn && userStore.accessToken) {
    console.log('✅ [欢迎页面] 用户已登录，跳转到首页')
    uni.reLaunch({
      url: '/pages/home/<USER>'
    })
    return
  }

  // 双重检查手机号和快速登录状态
  const storePhone = userStore.getSavedPhoneNumber
  const storeQuickLogin = userStore.isQuickLoginEnabled
  const uniPhone = uni.getStorageSync('savedPhoneNumber')
  const uniQuickLogin = uni.getStorageSync('enableQuickLogin')
  
  console.log('🏪 [欢迎页面] Store中的手机号:', storePhone)
  console.log('✅ [欢迎页面] Store中的快速登录状态:', storeQuickLogin)
  console.log('📱 [欢迎页面] UniStorage中的手机号:', uniPhone)
  console.log('⚡ [欢迎页面] UniStorage中的快速登录状态:', uniQuickLogin)
  
  // 优先使用 uni.storage 中的数据，因为它更可靠
  const hasPhone = uniPhone || storePhone
  const quickLoginEnabled = uniQuickLogin || storeQuickLogin
  
  console.log('🎯 [欢迎页面] 最终判断 - 手机号:', hasPhone, '快速登录启用:', quickLoginEnabled)

  // 检查是否有保存的手机号用于快速登录
  if (hasPhone && quickLoginEnabled) {
    console.log('📱 [欢迎页面] 检测到保存的手机号，跳转到PIN登录页面')
    console.log('📱 [欢迎页面] 保存的手机号:', hasPhone)
    
    // 如果 store 中的数据不完整，先更新 store
    if (!storePhone && uniPhone) {
      console.log('🔄 [欢迎页面] 更新 store 中的手机号数据')
      userStore.savedPhoneNumber = uniPhone
      userStore.enableQuickLogin = uniQuickLogin
    }
    
    uni.reLaunch({
      url: '/pages/auth/pin-login/index',
      success: () => {
        console.log('✅ [欢迎页面] 成功跳转到PIN登录页面')
      },
      fail: (err) => {
        console.error('❌ [欢迎页面] PIN登录页面跳转失败:', err)
        // 如果PIN登录页面跳转失败，回退到正常登录页面
        fallbackToNormalLogin()
      }
    })
  } else {
    console.log('🔐 [欢迎页面] 未检测到快速登录配置，跳转到正常登录页面')
    fallbackToNormalLogin()
  }
  
  console.log('🎬 [欢迎页面] ========== 检查完成 ==========\n')
}

// 回退到正常登录页面的方法
function fallbackToNormalLogin() {
  console.log('🔄 [欢迎页面] 跳转到正常登录页面')
  uni.reLaunch({
    url: '/pages/auth/login/index',
    success: () => {
      console.log('✅ [欢迎页面] 成功跳转到登录页面')
    },
    fail: (err) => {
      console.error('❌ [欢迎页面] 登录页面跳转失败:', err)
    }
  })
}

// 在页面加载完成后，初始化用户状态并检查登录状态
onMounted(() => {
  console.log('🎬 [欢迎页面] 页面加载完成，开始初始化')
  
  // 初始化用户状态
  userStore.initUserFromStorage()
  
  // 延迟一段时间后执行检测逻辑，确保页面完全加载
  setTimeout(() => {
    checkUserLoginStatus()
  }, 1500) // 1.5秒后执行检测
});
</script>

<style lang="less">
.welcome-container {
  min-height: 100vh;
  background-color: #000000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  
  .logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .logo {
      width: 240rpx;
      height: 120rpx;
    }
  }
}
</style> 