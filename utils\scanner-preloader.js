/**
 * 扫码功能预加载器
 * 预加载扫码相关资源，减少首次使用延迟
 */

import { getPerformanceConfig, isLowEndDevice } from './device-performance.js';

/**
 * 扫码预加载器类
 */
class ScannerPreloader {
  constructor() {
    this.isPreloaded = false;
    this.preloadPromise = null;
    this.resources = {
      permissions: false,
      camera: false,
      scanner: false
    };
  }

  /**
   * 开始预加载
   * @returns {Promise<void>}
   */
  async startPreload() {
    if (this.preloadPromise) {
      return this.preloadPromise;
    }

    this.preloadPromise = this.performPreload();
    return this.preloadPromise;
  }

  /**
   * 执行预加载
   * @returns {Promise<void>}
   */
  async performPreload() {
    if (this.isPreloaded) {
      return;
    }

    try {
      console.log('🚀 开始扫码功能预加载');
      
      // 检查设备性能
      const isLowEnd = await isLowEndDevice();
      
      if (isLowEnd) {
        // 低端设备使用轻量级预加载
        await this.lightweightPreload();
      } else {
        // 高端设备使用完整预加载
        await this.fullPreload();
      }
      
      this.isPreloaded = true;
      console.log('✅ 扫码功能预加载完成');
    } catch (error) {
      console.warn('⚠️ 扫码功能预加载失败:', error);
      // 预加载失败不影响正常功能
    }
  }

  /**
   * 轻量级预加载（低端设备）
   * @returns {Promise<void>}
   */
  async lightweightPreload() {
    // 只预加载最关键的资源
    await Promise.all([
      this.preloadCameraPermission(),
      this.preloadScannerModule()
    ]);
  }

  /**
   * 完整预加载（高端设备）
   * @returns {Promise<void>}
   */
  async fullPreload() {
    // 预加载所有相关资源
    await Promise.all([
      this.preloadCameraPermission(),
      this.preloadCameraAccess(),
      this.preloadScannerModule(),
      this.preloadImages()
    ]);
  }

  /**
   * 预加载相机权限检查
   * @returns {Promise<void>}
   */
  async preloadCameraPermission() {
    try {
      // #ifdef H5
      if (typeof navigator !== 'undefined' && navigator.mediaDevices) {
        // 检查媒体设备权限
        const devices = await navigator.mediaDevices.enumerateDevices();
        const hasCamera = devices.some(device => device.kind === 'videoinput');
        this.resources.permissions = hasCamera;
        console.log('📷 相机权限检查完成:', hasCamera);
      }
      // #endif

      // #ifdef APP-PLUS
      if (typeof plus !== 'undefined' && plus.navigator) {
        plus.navigator.checkPermission('CAMERA', (result) => {
          this.resources.permissions = result === 'authorized';
          console.log('📷 相机权限状态:', result);
        });
      }
      // #endif

      // #ifdef MP-WEIXIN
      // 小程序环境下，权限由uni.scanCode自动处理
      this.resources.permissions = true;
      // #endif
    } catch (error) {
      console.warn('相机权限检查失败:', error);
      this.resources.permissions = false;
    }
  }

  /**
   * 预加载相机访问
   * @returns {Promise<void>}
   */
  async preloadCameraAccess() {
    try {
      // #ifdef H5
      if (typeof navigator !== 'undefined' && navigator.mediaDevices && this.resources.permissions) {
        // 预热相机访问（不实际启动相机）
        const constraints = {
          video: {
            facingMode: 'environment', // 后置摄像头
            width: { ideal: 640 },
            height: { ideal: 480 }
          }
        };
        
        // 快速获取并立即释放媒体流
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        stream.getTracks().forEach(track => track.stop());
        this.resources.camera = true;
        console.log('📹 相机访问预热完成');
      }
      // #endif
    } catch (error) {
      console.warn('相机访问预热失败:', error);
      this.resources.camera = false;
    }
  }

  /**
   * 预加载扫码模块
   * @returns {Promise<void>}
   */
  async preloadScannerModule() {
    try {
      // 预加载扫码相关的模块和配置
      const config = await getPerformanceConfig();
      
      // 预初始化扫码配置
      const scanConfig = {
        scanType: ['qrCode'],
        onlyFromCamera: true,
        autoDecodeCharset: true
      };
      
      // 根据性能配置调整
      if (config && config.scanner) {
        if (config.scanner.scanQuality === 'low') {
          scanConfig.compressed = true;
        }
      }
      
      // 缓存配置
      uni.setStorageSync('preloadedScanConfig', scanConfig);
      this.resources.scanner = true;
      console.log('🔍 扫码模块预加载完成');
    } catch (error) {
      console.warn('扫码模块预加载失败:', error);
      this.resources.scanner = false;
    }
  }

  /**
   * 预加载图片资源
   * @returns {Promise<void>}
   */
  async preloadImages() {
    try {
      const images = [
        '/static/images/export.png',
        '/static/images/charging-pile-placeholder.png'
      ];
      
      const preloadPromises = images.map(src => {
        return new Promise((resolve) => {
          // #ifdef H5
          if (typeof Image !== 'undefined') {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = src;
          } else {
            resolve(true);
          }
          // #endif
          
          // #ifndef H5
          resolve(true);
          // #endif
        });
      });
      
      await Promise.all(preloadPromises);
      console.log('🖼️ 图片资源预加载完成');
    } catch (error) {
      console.warn('图片资源预加载失败:', error);
    }
  }

  /**
   * 获取预加载的扫码配置
   * @returns {Object|null} 扫码配置
   */
  getPreloadedScanConfig() {
    try {
      return uni.getStorageSync('preloadedScanConfig');
    } catch (error) {
      console.warn('获取预加载配置失败:', error);
      return null;
    }
  }

  /**
   * 检查资源是否已预加载
   * @param {string} resourceType 资源类型
   * @returns {boolean} 是否已预加载
   */
  isResourcePreloaded(resourceType) {
    return this.resources[resourceType] || false;
  }

  /**
   * 获取预加载状态
   * @returns {Object} 预加载状态
   */
  getPreloadStatus() {
    return {
      isPreloaded: this.isPreloaded,
      resources: { ...this.resources },
      readyForScan: this.isReadyForScan()
    };
  }

  /**
   * 检查是否准备好扫码
   * @returns {boolean} 是否准备好
   */
  isReadyForScan() {
    return this.resources.permissions && this.resources.scanner;
  }

  /**
   * 清理预加载资源
   */
  cleanup() {
    try {
      uni.removeStorageSync('preloadedScanConfig');
    } catch (error) {
      console.warn('清理预加载资源失败:', error);
    }
    
    this.isPreloaded = false;
    this.preloadPromise = null;
    this.resources = {
      permissions: false,
      camera: false,
      scanner: false
    };
    
    console.log('🧹 扫码预加载器已清理');
  }

  /**
   * 强制重新预加载
   * @returns {Promise<void>}
   */
  async forceReload() {
    this.cleanup();
    return this.startPreload();
  }
}

// 创建全局实例
const scannerPreloader = new ScannerPreloader();

/**
 * 启动扫码预加载
 * @returns {Promise<void>}
 */
export async function startScannerPreload() {
  return scannerPreloader.startPreload();
}

/**
 * 获取预加载的扫码配置
 * @returns {Object|null} 扫码配置
 */
export function getPreloadedScanConfig() {
  return scannerPreloader.getPreloadedScanConfig();
}

/**
 * 检查是否准备好扫码
 * @returns {boolean} 是否准备好
 */
export function isReadyForScan() {
  return scannerPreloader.isReadyForScan();
}

/**
 * 获取预加载状态
 * @returns {Object} 预加载状态
 */
export function getPreloadStatus() {
  return scannerPreloader.getPreloadStatus();
}

/**
 * 清理预加载资源
 */
export function cleanupPreloader() {
  scannerPreloader.cleanup();
}

export default scannerPreloader;
