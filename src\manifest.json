{"name": "French_charging_station", "appid": "__UNI__0060761", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Maps": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {"privacyDescription": {"NSLocationWhenInUseUsageDescription": "此应用需要访问您的位置信息以显示附近的充电站", "NSLocationAlwaysUsageDescription": "此应用需要访问您的位置信息以显示附近的充电站"}}, "sdkConfigs": {"maps": {"google": {"APIKey_ios": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc", "APIKey_android": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc"}}, "geolocation": {"system": {"__platform__": ["ios", "android"]}, "amap": {"__platform__": ["ios", "android"], "appkey_ios": "", "appkey_android": ""}, "google": {"__platform__": ["ios", "android"], "APIKey_ios": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc", "APIKey_android": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc"}}}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "您的位置信息将用于显示附近的充电站"}}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"devServer": {"port": 8080, "disableHostCheck": true}, "title": "French Charging Station", "router": {"mode": "hash"}, "sdkConfigs": {"maps": {"qqmap": {"key": ""}, "google": {"key": "AIzaSyA5oNIFP-QuXdDReAtkyIsdZVwt0PRHojc"}}}}}