import { ref } from 'vue'
import { checkVersion, saveLocalVersion, type VersionResponse } from '../api/modules/version'
import { getAppVersion } from '../utils/version.js'
import { useVersionStore } from '@/store/version.js'

// 声明uni-app和plus全局对象
declare const uni: {
  showModal: (options: any) => void;
  [key: string]: any;
};

declare const plus: {
  downloader: {
    createDownload: (url: string, options: any, callback: (download: any, status: number) => void) => any;
  };
  runtime: {
    install: (filename: string, options: any, success: () => void, fail: (error: any) => void) => void;
  };
  [key: string]: any;
};

declare const getCurrentPages: () => Array<{
  route: string;
  [key: string]: any;
}>;

export const useVersionUpdate = () => {
  // 获取国际化函数
  const t = (key: string) => {
    try {
      // @ts-ignore
      if (uni.$t) {
        return uni.$t(key)
      }
      // 如果uni.$t不存在，尝试从全局获取
      if (typeof getCurrentPages === 'function') {
        const pages = getCurrentPages()
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1]
          if (currentPage.$vm && currentPage.$vm.$t) {
            return currentPage.$vm.$t(key)
          }
        }
      }
      return key
    } catch (error) {
      console.warn('国际化函数调用失败:', error)
      return key
    }
  }
  // 使用版本管理工具获取当前版本号（通过 Pinia 统一）
  const versionStore = useVersionStore()
  const currentVersion = ref(versionStore.currentVersion)
  const updateDialogVisible = ref(false)
  const updateInfo = ref<VersionResponse['data'] | null>(null)
  const checking = ref(false)

  // 检测版本更新
  const checkVersionUpdate = async (showToast = true, handlers?: { onLatest?: () => void; onError?: (msg: string) => void }) => {
    try {
      checking.value = true
      const response = await checkVersion()
      
      if (response.code === 200) {
        const versionData = response.data
        
        if (versionData.status === '0') {
          // 有新版本
          updateInfo.value = versionData
          updateDialogVisible.value = true
          console.log('检测到新版本:', versionData.versionNum)
        } else if (versionData.status === '1') {
          // 已是最新版本
          console.log('已是最新版本')
          if (showToast) {
            uni.showModal({
              title: t('update.title') || 'Update',
              content: t('update.latestVersion'),
              showCancel: false
            })
          }
          handlers && handlers.onLatest && handlers.onLatest()
        }
      } else {
        const msg = response.msg || t('update.checkFailed')
        if (showToast) {
          uni.showModal({
            title: t('update.title') || 'Update',
            content: msg,
            showCancel: false
          })
        }
        handlers && handlers.onError && handlers.onError(msg)
      }
    } catch (error) {
      console.error('版本检测失败:', error)
      if (showToast) {
        uni.showModal({
          title: t('update.title') || 'Version',
          content: t('update.checkFailed'),
          showCancel: false
        })
      }
    } finally {
      checking.value = false
    }
  }

  // 关闭更新弹窗
  const closeUpdateDialog = () => {
    updateDialogVisible.value = false
    updateInfo.value = null
  }

  // 执行更新
  const performUpdate = (downloadUrl: string) => {
    console.log('开始下载APK:', downloadUrl)

    // #ifdef APP-PLUS
    // 在APP环境下，使用plus.downloader下载APK
    const downloadTask = plus.downloader.createDownload(downloadUrl, {
      filename: '_downloads/update.apk',
      timeout: 60000, // 60秒超时
      retry: 3 // 重试3次
    }, (download, status) => {
      console.log('下载完成，状态码:', status)

      if (status === 200) {
        console.log('下载成功，开始安装APK')

        // 下载完成，安装APK（仅在安装成功后保存版本号）
        plus.runtime.install(download.filename, {
          force: false
        }, () => {
          console.log('APK安装成功')
          if (updateInfo.value?.versionNum) {
            saveLocalVersion(updateInfo.value.versionNum)
            // 通过全局版本仓库更新，可联动 About 等页面
            versionStore.updateVersion(updateInfo.value.versionNum)
            currentVersion.value = versionStore.currentVersion
            console.log('安装成功后已保存并刷新显示版本号为:', updateInfo.value.versionNum)
          }
        }, (error) => {
          console.error('APK安装失败:', error)
        })
      } else {
        console.error('下载失败，状态码:', status)
      }
    })

    // 监听下载进度
    downloadTask.addEventListener('statechanged', (download: any, status: any) => {
      console.log('下载状态变化:', status)
      switch (download.state) {
        case 1: // 开始下载
          console.log('开始下载')
          break
        case 2: // 下载中
          const progress = Math.round((download.downloadedSize / download.totalSize) * 100)
          console.log('下载进度:', progress + '%')
          break
        case 3: // 下载完成
          console.log('下载完成')
          break
        case 4: // 下载失败
          console.error('下载失败')
          break
      }
    })

    downloadTask.start()
    // #endif

    // #ifdef H5
    // 在H5环境下，直接打开下载链接
    console.log('H5环境，打开下载链接')

    // H5环境下，假设用户会下载并安装，所以立即保存版本号
    if (updateInfo.value?.versionNum) {
      saveLocalVersion(updateInfo.value.versionNum)
      console.log('H5环境：已保存最新版本号到本地:', updateInfo.value.versionNum)
    }

    window.open(downloadUrl, '_blank')
    // #endif

    // #ifdef MP
    // 在小程序环境下，提示用户手动更新
    console.log('小程序环境，提示手动更新')
    uni.showModal({
      title: t('update.title') || '版本更新',
      content: t('update.manualUpdate') || '请手动更新应用',
      showCancel: false
    })
    // #endif
  }

  // 手动标记为已更新（用于测试或特殊情况）
  const markAsUpdated = () => {
    if (updateInfo.value?.versionNum) {
      saveLocalVersion(updateInfo.value.versionNum)
      console.log('手动标记为已更新，版本号:', updateInfo.value.versionNum)
      closeUpdateDialog()
    }
  }

  return {
    currentVersion,
    updateDialogVisible,
    updateInfo,
    checking,
    checkVersionUpdate,
    closeUpdateDialog,
    performUpdate,
    markAsUpdated
  }
}
