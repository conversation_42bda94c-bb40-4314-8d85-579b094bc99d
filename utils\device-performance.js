/**
 * 设备性能检测和优化工具
 * 用于识别低端设备并提供相应的性能优化配置
 */

// 设备性能等级枚举
export const DEVICE_PERFORMANCE_LEVEL = {
  HIGH: 'high',      // 高性能设备 (4GB+ RAM, 高端处理器)
  MEDIUM: 'medium',  // 中等性能设备 (3-4GB RAM, 中端处理器)
  LOW: 'low'         // 低性能设备 (2GB- RAM, 低端处理器)
};

// 低端设备特征配置
const LOW_END_DEVICE_PATTERNS = {
  // 内存阈值 (MB)
  RAM_THRESHOLD: 2048,
  
  // CPU核心数阈值
  CPU_CORES_THRESHOLD: 4,
  
  // 已知低端设备型号模式
  LOW_END_MODELS: [
    /OPPO A1/i,
    /OPPO A3/i,
    /OPPO A5/i,
    /OPPO A7/i,
    /vivo Y\d+/i,
    /Redmi \d+A/i,
    /Redmi Note \d+/i,
    /Samsung Galaxy A0/i,
    /Samsung Galaxy J/i,
    /Huawei Y\d+/i,
    /Honor \d+A/i
  ],
  
  // 低端处理器模式
  LOW_END_PROCESSORS: [
    /Snapdragon 4\d+/i,
    /Snapdragon 6\d+/i,
    /MediaTek MT67\d+/i,
    /MediaTek Helio A/i,
    /Unisoc Tiger/i,
    /Kirin 6\d+/i
  ]
};

// 性能优化配置
export const PERFORMANCE_CONFIGS = {
  [DEVICE_PERFORMANCE_LEVEL.HIGH]: {
    // 高性能设备配置
    scanner: {
      enableAnimations: true,
      scanQuality: 'high',
      previewFrameRate: 30,
      enableHoverEffects: true,
      enable3DRendering: true
    },
    ui: {
      enableComplexAnimations: true,
      enableParticleEffects: true,
      maxConcurrentAnimations: 10
    }
  },
  
  [DEVICE_PERFORMANCE_LEVEL.MEDIUM]: {
    // 中等性能设备配置
    scanner: {
      enableAnimations: true,
      scanQuality: 'medium',
      previewFrameRate: 24,
      enableHoverEffects: true,
      enable3DRendering: true
    },
    ui: {
      enableComplexAnimations: true,
      enableParticleEffects: false,
      maxConcurrentAnimations: 5
    }
  },
  
  [DEVICE_PERFORMANCE_LEVEL.LOW]: {
    // 低性能设备配置
    scanner: {
      enableAnimations: false,
      scanQuality: 'low',
      previewFrameRate: 15,
      enableHoverEffects: false,
      enable3DRendering: false
    },
    ui: {
      enableComplexAnimations: false,
      enableParticleEffects: false,
      maxConcurrentAnimations: 2
    }
  }
};

/**
 * 设备性能检测类
 */
class DevicePerformanceDetector {
  constructor() {
    this.performanceLevel = null;
    this.deviceInfo = null;
    this.memoryInfo = null;
  }

  /**
   * 检测设备性能等级
   * @returns {Promise<string>} 性能等级
   */
  async detectPerformanceLevel() {
    if (this.performanceLevel) {
      return this.performanceLevel;
    }

    try {
      // 获取设备信息
      this.deviceInfo = await this.getDeviceInfo();
      
      // 获取内存信息
      this.memoryInfo = await this.getMemoryInfo();
      
      // 综合评估性能等级
      this.performanceLevel = this.evaluatePerformanceLevel();
      
      console.log('设备性能检测结果:', {
        level: this.performanceLevel,
        deviceInfo: this.deviceInfo,
        memoryInfo: this.memoryInfo
      });
      
      return this.performanceLevel;
    } catch (error) {
      console.warn('设备性能检测失败，使用默认中等性能配置:', error);
      this.performanceLevel = DEVICE_PERFORMANCE_LEVEL.MEDIUM;
      return this.performanceLevel;
    }
  }

  /**
   * 获取设备信息
   * @returns {Promise<Object>} 设备信息
   */
  getDeviceInfo() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      plus.device.getInfo({
        success: (info) => {
          resolve({
            model: info.model || '',
            vendor: info.vendor || '',
            uuid: info.uuid || '',
            platform: info.platform || '',
            osVersion: info.osVersion || '',
            cpuType: info.cpuType || '',
            cpuCount: info.cpuCount || 0
          });
        },
        fail: () => {
          resolve(this.getFallbackDeviceInfo());
        }
      });
      // #endif
      
      // #ifndef APP-PLUS
      uni.getSystemInfo({
        success: (info) => {
          resolve({
            model: info.model || '',
            brand: info.brand || '',
            platform: info.platform || '',
            system: info.system || '',
            version: info.version || '',
            screenWidth: info.screenWidth || 0,
            screenHeight: info.screenHeight || 0
          });
        },
        fail: () => {
          resolve(this.getFallbackDeviceInfo());
        }
      });
      // #endif
    });
  }

  /**
   * 获取内存信息
   * @returns {Promise<Object>} 内存信息
   */
  getMemoryInfo() {
    return new Promise((resolve) => {
      // #ifdef APP-PLUS
      if (plus.device && plus.device.getMemoryInfo) {
        plus.device.getMemoryInfo({
          success: (info) => {
            resolve({
              total: info.total || 0,
              available: info.available || 0,
              used: info.used || 0
            });
          },
          fail: () => {
            resolve(this.estimateMemoryInfo());
          }
        });
      } else {
        resolve(this.estimateMemoryInfo());
      }
      // #endif
      
      // #ifndef APP-PLUS
      resolve(this.estimateMemoryInfo());
      // #endif
    });
  }

  /**
   * 评估设备性能等级
   * @returns {string} 性能等级
   */
  evaluatePerformanceLevel() {
    let score = 0;
    
    // 基于设备型号评估
    if (this.deviceInfo) {
      const modelString = `${this.deviceInfo.model || ''} ${this.deviceInfo.brand || ''}`;
      
      // 检查是否为已知低端设备
      const isLowEndModel = LOW_END_DEVICE_PATTERNS.LOW_END_MODELS.some(pattern => 
        pattern.test(modelString)
      );
      
      if (isLowEndModel) {
        score -= 30;
      }
      
      // 检查CPU信息
      if (this.deviceInfo.cpuCount && this.deviceInfo.cpuCount < LOW_END_DEVICE_PATTERNS.CPU_CORES_THRESHOLD) {
        score -= 20;
      }
      
      // 检查处理器型号
      const cpuString = this.deviceInfo.cpuType || '';
      const isLowEndProcessor = LOW_END_DEVICE_PATTERNS.LOW_END_PROCESSORS.some(pattern => 
        pattern.test(cpuString)
      );
      
      if (isLowEndProcessor) {
        score -= 25;
      }
    }
    
    // 基于内存评估
    if (this.memoryInfo && this.memoryInfo.total) {
      if (this.memoryInfo.total < LOW_END_DEVICE_PATTERNS.RAM_THRESHOLD) {
        score -= 40;
      } else if (this.memoryInfo.total < 3072) { // 3GB
        score -= 20;
      } else if (this.memoryInfo.total >= 4096) { // 4GB+
        score += 20;
      }
    }
    
    // 基于屏幕分辨率评估（低分辨率通常对应低端设备）
    if (this.deviceInfo && this.deviceInfo.screenWidth && this.deviceInfo.screenHeight) {
      const totalPixels = this.deviceInfo.screenWidth * this.deviceInfo.screenHeight;
      if (totalPixels < 1280 * 720) { // 低于720p
        score -= 15;
      }
    }
    
    // 根据得分确定性能等级
    if (score <= -50) {
      return DEVICE_PERFORMANCE_LEVEL.LOW;
    } else if (score <= -10) {
      return DEVICE_PERFORMANCE_LEVEL.MEDIUM;
    } else {
      return DEVICE_PERFORMANCE_LEVEL.HIGH;
    }
  }

  /**
   * 获取备用设备信息
   * @returns {Object} 备用设备信息
   */
  getFallbackDeviceInfo() {
    return {
      model: 'Unknown',
      brand: 'Unknown',
      platform: 'Unknown'
    };
  }

  /**
   * 估算内存信息
   * @returns {Object} 估算的内存信息
   */
  estimateMemoryInfo() {
    // 基于设备型号估算内存
    if (this.deviceInfo) {
      const modelString = `${this.deviceInfo.model || ''} ${this.deviceInfo.brand || ''}`;
      
      // 检查是否为已知低端设备
      const isLowEndModel = LOW_END_DEVICE_PATTERNS.LOW_END_MODELS.some(pattern => 
        pattern.test(modelString)
      );
      
      if (isLowEndModel) {
        return { total: 2048, available: 1024, used: 1024 }; // 2GB设备估算
      }
    }
    
    return { total: 3072, available: 1536, used: 1536 }; // 3GB设备估算
  }

  /**
   * 获取当前性能配置
   * @returns {Object} 性能配置
   */
  getPerformanceConfig() {
    const level = this.performanceLevel || DEVICE_PERFORMANCE_LEVEL.MEDIUM;
    return PERFORMANCE_CONFIGS[level];
  }

  /**
   * 是否为低端设备
   * @returns {boolean} 是否为低端设备
   */
  isLowEndDevice() {
    return this.performanceLevel === DEVICE_PERFORMANCE_LEVEL.LOW;
  }

  /**
   * 获取设备信息摘要
   * @returns {Object} 设备信息摘要
   */
  getDeviceSummary() {
    return {
      performanceLevel: this.performanceLevel,
      isLowEnd: this.isLowEndDevice(),
      deviceModel: this.deviceInfo?.model || 'Unknown',
      totalMemory: this.memoryInfo?.total || 0,
      config: this.getPerformanceConfig()
    };
  }
}

// 创建全局实例
const devicePerformanceDetector = new DevicePerformanceDetector();

/**
 * 获取设备性能等级
 * @returns {Promise<string>} 性能等级
 */
export async function getDevicePerformanceLevel() {
  return await devicePerformanceDetector.detectPerformanceLevel();
}

/**
 * 获取性能配置
 * @returns {Promise<Object>} 性能配置
 */
export async function getPerformanceConfig() {
  await devicePerformanceDetector.detectPerformanceLevel();
  return devicePerformanceDetector.getPerformanceConfig();
}

/**
 * 检查是否为低端设备
 * @returns {Promise<boolean>} 是否为低端设备
 */
export async function isLowEndDevice() {
  await devicePerformanceDetector.detectPerformanceLevel();
  return devicePerformanceDetector.isLowEndDevice();
}

/**
 * 获取设备摘要信息
 * @returns {Promise<Object>} 设备摘要
 */
export async function getDeviceSummary() {
  await devicePerformanceDetector.detectPerformanceLevel();
  return devicePerformanceDetector.getDeviceSummary();
}

export default devicePerformanceDetector;
