<template>
  <view class="station-detail">
    <!-- 固定头部区域 -->
    <view class="fixed-header">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="header">
        <view class="header-content">
          <view class="back-btn" @click="handleBack">
            <text class="iconfont icon-back"></text>
          </view>
          <text class="title">{{ $t('station.title') }}</text>
          <view class="favorite-btn" @click="toggleFavorite">
            <!-- <image :src="isFavorite ? '/static/images/favorite-selected.png' : '/static/images/favorite-unselected.png'"
              class="favorite-icon" /> -->
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content-scroll"
      :style="{ height: `calc(100vh - ${statusBarHeight}px - 88rpx)`, marginTop: `${statusBarHeight + 88}rpx` }">
      <!-- 站点轮播图 -->
      <view class="station-image-container">
        <!-- 有图片时显示轮播图 -->
        <swiper v-if="hasValidImages" class="station-swiper" :indicator-dots="validImages.length > 1"
          :autoplay="validImages.length > 1" :interval="3000" :duration="500"
          indicator-color="rgba(255, 255, 255, 0.5)" indicator-active-color="#fff">
          <swiper-item v-for="(imageUrl, index) in validImages" :key="index">
            <image :src="imageUrl" class="station-image" mode="aspectFill" @error="handleImageError(index)"
              @click="openImagePreview(index)" />
          </swiper-item>
        </swiper>

        <!-- 无图片时显示占位符 -->
        <view v-else class="no-image-placeholder">
          <view class="placeholder-content">
            <view class="placeholder-icon">
              <text class="iconfont icon-image">📷</text>
            </view>
            <text class="placeholder-text">{{ $t('station.noImageUploaded') }}</text>
            <text class="placeholder-subtitle">{{ $t('station.imageNotAvailable') }}</text>
          </view>
        </view>
      </view>

      <!-- 站点基本信息 -->
      <view class="station-info-card">
        <view class="station-name">{{ stationDetail.name }}</view>
        <view class="time-charge-container">
          <view class="station-hours">
            <image src="/static/images/time.png" class="time-icon" />
            <text>{{ stationDetail.hours }}</text>
          </view>
        </view>

        <!-- 标签区域 -->
        <view class="tags-container">
          <view class="tag" v-for="tag in stationDetail.tags" :key="tag">{{ tag }}</view>
        </view>

        <!-- 地址信息 -->
        <view class="address-container">
          <view class="address-text">
            {{ stationDetail.address }}
          </view>
          <view class="distance-container" @click="openNavigationDialog">
            <image src="/static/images/map-icon.png" class="map-icon" />
            <text class="distance">{{ stationDetail.distance }}km</text>
          </view>
        </view>
      </view>

      <!-- 充电桩状态 -->
      <view class="status-card">
        <view class="status-left">
          <text class="status-text available">{{ $t('station.available') }}</text>
        </view>
        <view class="status-right">
          <text class="status-count">{{ stationDetail.available }}/{{ stationDetail.total }}</text>
        </view>
      </view>

      <!-- 当前时段价格 -->
      <view class="price-card">
        <view class="period-info">
          <text class="period-label">{{ $t('charging.currentPeriod') || 'Current period' }}</text>
          <text class="period-time">{{ stationDetail.currentPeriod.time }}</text>
        </view>
        <view class="price-info">
          <view class="regular-price">
            <text class="price-amount">{{ formatPrice(stationDetail.currentPeriod.price) }}</text>
            <text class="price-unit">{{ stationDetail.currentPeriod.unit }}</text>
          </view>
        </view>

        <!-- 会员价格 -->
        <!-- <view class="membership-price-container">
          <view class="membership-price">
            <text class="membership-label">{{ $t('account.membershipPrice') || 'Membership price' }}</text>
            <text class="membership-amount">{{ formatPrice(stationDetail.membershipPrice.price) }}</text>
            <text class="membership-unit">{{ stationDetail.membershipPrice.unit }}</text>
          </view>
          <view class="activate-btn">
            <text>{{ $t('common.activate') || 'Activate' }}</text>
          </view>
        </view> -->

        <!-- 下一时段价格 -->
        <!-- <view class="next-period-container">
          <view class="next-period-info">
            <text class="next-period-label">{{ $t('charging.nextPeriod') || 'Next time slot' }}</text>
            <text class="next-period-time">{{ stationDetail.nextPeriod.time }}</text>
          </view>
          <view class="next-period-price">
            <text>{{ formatPrice(stationDetail.nextPeriod.price) }}F</text>
            <text>/kWh</text>
          </view> -->
        </view>

        <!-- 价格提示 -->
        <!-- <view class="price-note">
          <text class="note-icon">i</text>
          <text class="note-text">The actual payment amount is subject to the final order confirmation</text>
        </view> -->

        <!-- 价格详情链接 -->
        <!-- <view class="price-details-link" @click="openPriceDetails">
          <text>Price Details</text>
          <text class="arrow-icon">></text>
        </view>
      </view> -->

      <!-- 停车费用信息 -->
      <!-- <view class="parking-fee-card">
        <view class="parking-fee-header">
          <view class="parking-icon-container">
            <text class="parking-icon">P</text>
          </view>
          <text class="parking-fee-title">Parking Fee</text>
        </view>
        <view class="parking-fee-content">
          <text>{{ stationDetail.parkingFee }}</text>
        </view>
      </view> -->

      <!-- 充电桩列表 -->
      <view class="charging-pile-section">
        <view class="section-header">
          <text class="section-title">{{ $t('station.chargingPileList') }}</text>
        </view>

        <!-- 充电桩筛选标签 -->
        <view class="pile-filter-tabs">
          <view
            v-for="tab in availableTabs"
            :key="tab.state"
            :class="['filter-tab', activeTab === tab.state ? 'active' : '']"
            @click="switchTab(tab.state)"
          >
            <text>{{ tab.label }} ({{ getTabCount(tab.state) }})</text>
          </view>
        </view>

        <!-- 充电桩列表 -->
        <view class="pile-list">
          <!-- 骨架屏加载状态 -->
          <view v-if="isLoadingPiles" class="skeleton-container">
            <view class="skeleton-pile-item" v-for="n in 3" :key="n">
              <view class="skeleton-status">
                <view class="skeleton-circle"></view>
              </view>
              <view class="skeleton-info">
                <view class="skeleton-row">
                  <view class="skeleton-label"></view>
                  <view class="skeleton-value"></view>
                  <view class="skeleton-icon"></view>
                </view>
                <view class="skeleton-row">
                  <view class="skeleton-label"></view>
                  <view class="skeleton-value"></view>
                </view>
              </view>
            </view>
          </view>

          <!-- 充电桩项 -->
          <view v-else-if="filteredPiles.length > 0" class="pile-items">
            <view class="pile-item" v-for="(pile, index) in filteredPiles" :key="pile.id || index">
              <view :class="['pile-status', pile.status]">
                <image :src="getPileStatusIcon(pile.status)" class="pile-icon" />
              </view>
              <view class="pile-info">
                <view class="pile-code-row">
                  <text class="info-label">{{ $t('station.code') }}</text>
                  <text class="info-value">{{ pile.code }}</text>
                  <image src="/static/images/copy.png" class="copy-icon" @click="copyCode(pile.code)" />
                </view>
                <view class="pile-power-row">
                  <text class="info-label">{{ $t('station.power') }}</text>
                  <text class="info-value">{{ pile.power }}</text>
                </view>
                <view class="pile-status-row">
                  <text class="info-label">État</text>
                  <text class="info-value status-text" :class="'status-' + pile.spearChargingState"
                        :style="{ color: getStatusColor(pile.spearChargingState) }">
                    {{ getChargingStateText(pile.spearChargingState) }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 无充电桩提示 -->
          <view v-else class="no-piles-tip">
            <text class="no-piles-text">{{ $t('station.noChargingPiles') || 'No charging piles available' }}</text>
          </view>
        </view>
      </view>

      <!-- 站点信息 -->
      <view class="station-info-section">
        <view class="section-header">
          <text class="section-title">{{ $t('station.powerStationInformation') || 'Informations sur la station de recharge' }}</text>
        </view>

        <view class="info-list">
          <view class="info-item">
            <text class="info-label">{{ $t('station.businessHours') || 'Horaires d\'ouverture' }}</text>
            <text class="info-value">{{ stationDetail.stationInfo.businessHours }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">{{ $t('station.serviceProvider') || 'Fournisseur du service' }}</text>
            <text class="info-value">{{ stationDetail.stationInfo.serviceProvider }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">{{ $t('station.invoiceProvision') || 'Facturation' }}</text>
            <text class="info-value">{{ stationDetail.stationInfo.invoiceProvision }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">{{ $t('station.customerService') || 'Service client' }}</text>
            <text class="info-value">{{ stationDetail.stationInfo.customerService }}</text>
          </view>
        </view>
      </view>

      <!-- 底部空白，防止内容被底部按钮遮挡 -->
      <!-- <view class="bottom-space"></view> -->
    </scroll-view>

    <!-- 底部固定按钮 -->
    <!-- <view class="footer">
      <view class="customer-service-btn" @click="handleCustomerService">
        <image src="/static/images/customer-service-2.png" class="cs-icon" />
      </view>
      <view class="charge-btn" @click="triggerScan">
        <image src="/static/images/scan-to-charge.png" class="scan-icon" />
        <text>Scan the QR code to charge</text>
      </view>
    </view> -->

    <!-- 价格详情弹出层 -->
    <view class="price-details-popup" v-if="showPriceDetails">
      <view class="popup-mask" @click="closePriceDetails"></view>
      <view class="popup-content" :class="{ 'popup-show': showPriceDetails }">
        <view class="popup-header">
          <text class="popup-title">All-time Electricity Tariff</text>
          <view class="close-btn" @click="closePriceDetails">
            <image src="/static/images/close.png" class="close-icon" />
          </view>
        </view>

        <!-- 表格内容 -->
        <view class="tariff-table">
          <!-- 表格头部 -->
          <view class="table-header">
            <text class="header-period">Period</text>
            <text class="header-type">Type</text>
            <text class="header-tariff">Electricity Tariff</text>
            <text class="header-charge">Electricity Charge</text>
            <text class="header-fee">Service Fee</text>
          </view>

          <view class="table-content">
            <view v-for="(tariff, index) in tariffList" :key="index" class="table-row"
              :class="{ 'current': tariff.isPeriod }" @click="selectPeriod(tariff.timeRange)">
              <view class="current-tag" v-if="tariff.isPeriod">Current Period</view>

              <!-- 会员价格行 -->
              <view class="price-row">
                <text class="period">{{ tariff.timeRange.split(' - ')[0] }}</text>
                <text class="type">Member Price</text>
                <text class="price-value">{{ formatPrice(tariff.memberPrice.total) }}</text>
                <text class="price-detail">{{ formatPrice(tariff.memberPrice.electricCharge) }}</text>
                <text class="price-detail">{{ formatPrice(tariff.memberPrice.serviceFee) }}</text>
              </view>

              <!-- 站点价格行 -->
              <view class="price-row">
                <text class="period">{{ tariff.timeRange.split(' - ')[1] }}</text>
                <text class="type">Site Price</text>
                <text class="price-value">{{ formatPrice(tariff.sitePrice.total) }}</text>
                <text class="price-detail">{{ formatPrice(tariff.sitePrice.electricCharge) }}</text>
                <text class="price-detail">{{ formatPrice(tariff.sitePrice.serviceFee) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 充电桩列表弹窗 -->
    <view class="pile-list-popup" v-if="showPileListPopup">
      <view class="popup-mask" @click="closePileListPopup"></view>
      <view class="popup-content" :class="{ 'popup-show': showPileListPopup }">
        <view class="popup-header">
          <text class="popup-title">Charging Pile List</text>
          <view class="close-btn" @click="closePileListPopup">
            <image src="/static/images/close.png" class="close-icon" />
          </view>
        </view>

        <!-- 充电桩筛选标签 -->
        <view class="popup-filter-tabs">
          <view v-for="(tab, index) in [
            { id: 'all', label: 'All', count: pileTypeCounts.all },
            { id: 'idle', label: 'Idle', count: pileTypeCounts.idle },
            { id: '40KW', label: '40KW', count: pileTypeCounts['40KW'] }
          ]" :key="tab.id" :class="['filter-tab', selectedPileType === tab.id ? 'active' : '']"
            @click="switchPileType(tab.id)">
            <text>{{ tab.label }} ({{ tab.count }})</text>
          </view>
        </view>

        <!-- 充电桩列表 -->
        <scroll-view scroll-y class="popup-pile-list">
          <!-- 骨架屏加载状态 -->
          <view v-if="isLoadingPiles" class="skeleton-container">
            <view class="skeleton-pile-item" v-for="n in 5" :key="n">
              <view class="skeleton-status">
                <view class="skeleton-circle"></view>
              </view>
              <view class="skeleton-info">
                <view class="skeleton-row">
                  <view class="skeleton-label"></view>
                  <view class="skeleton-value"></view>
                  <view class="skeleton-icon"></view>
                </view>
                <view class="skeleton-row">
                  <view class="skeleton-label"></view>
                  <view class="skeleton-value"></view>
                </view>
              </view>
            </view>
          </view>

          <!-- 充电桩项 -->
          <view v-else-if="filteredPopupPiles.length > 0">
            <view class="pile-item" v-for="(pile, index) in filteredPopupPiles" :key="index">
              <view class="pile-status" :class="pile.status">
                <image :src="getStatusIcon(pile.status)" class="pile-icon" />
              </view>
              <view class="pile-info">
                <view class="pile-code-row">
                  <text class="info-label">{{ $t('station.code') }}</text>
                  <text class="info-value">{{ pile.code }}</text>
                  <view class="copy-button" @click="copyCode(pile.code)">
                    <image src="/static/images/copy.png" class="copy-icon" />
                  </view>
                </view>
                <view class="pile-power-row">
                  <text class="info-label">{{ $t('station.power') }}</text>
                  <text class="info-value">{{ pile.power }}</text>
                </view>
                <view class="pile-status-row">
                  <text class="info-label">État</text>
                  <text class="info-value status-text" :class="'status-' + pile.spearChargingState"
                        :style="{ color: getStatusColor(pile.spearChargingState) }">
                    {{ getChargingStateText(pile.spearChargingState) }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 无充电桩提示 -->
          <view v-else class="no-piles-tip">
            <text class="no-piles-text">{{ $t('station.noChargingPiles') || 'No charging piles available' }}</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 图片预览组件 -->
    <ImagePreview :visible="showImagePreview" :images="validImages" :initialIndex="previewImageIndex"
      @close="closeImagePreview" @change="onPreviewImageChange" />

    <!-- 充电桩扫码组件 -->
    <!-- <ChargingPileScanner ref="chargingPileScannerRef" :disabled="false" @scan-success="handleScannerSuccess"
      @charging-start="handleChargingStart" @charging-test="handleChargingTest"
      @scan-fail="handleScannerFail" @charging-error="handleChargingError"
      @loading-change="handleLoadingChange" @step-change="handleStepChange"
      @model-type-change="handleModelTypeChange" /> -->

    <!-- 导航弹窗 -->
    <view v-if="showNavigationDialog" class="navigation-modal-overlay" @click="closeNavigationDialog">
      <view class="navigation-modal" @click.stop>
        <view class="modal-header">
          <view class="modal-title">{{ $t('navigation.title') }}</view>
          <view class="modal-close" @click="closeNavigationDialog">
            <text class="icon-close">✕</text>
          </view>
        </view>

        <view class="modal-content">
          <view class="station-name">{{ stationDetail.name }}</view>
          <view class="description-text">{{ $t('navigation.description') }}</view>
        </view>

        <view class="modal-actions">
          <view class="action-button cancel-button" @click="closeNavigationDialog">
            <text class="button-text">{{ $t('navigation.cancel') }}</text>
          </view>
          <view class="action-button confirm-button" @click="confirmNavigation">
            <text class="button-text">{{ $t('navigation.navigate') }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 充电状态弹窗 - 高级版本 -->
    <view class="charging-popup-premium" v-if="chargingStep > 0">
      <view class="popup-backdrop-premium" @click="closeChargingPopup"></view>
      <view class="popup-content-premium">

        <!-- 状态图标区域 -->
        <view class="status-icon-area-premium">
          <view class="status-icon-container-premium" :class="{ 'error': chargeError, 'success': chargingStep >= 4 && !chargeError }">
            <!-- App Logo图标 -->
            <view class="app-logo-container">
              <image src="/static/images/ARNIO.png" mode="aspectFit" class="app-logo" v-if="!chargeError && chargingStep < 4"></image>
              <text class="status-icon-text" v-if="chargeError">✕</text>
              <text class="status-icon-text success" v-else-if="chargingStep >= 4">✓</text>
            </view>
            <!-- 加载动画环 -->
            <view class="loading-ring" v-if="!chargeError && chargingStep < 4"></view>
          </view>
          <view class="status-glow-premium" :class="{ 'error': chargeError, 'success': chargingStep >= 4 && !chargeError }"></view>
        </view>

        <!-- 标题区域 -->
        <view class="popup-header-premium">
          <text class="header-title-premium" :class="{ 'error': chargeError }">
            {{ getPopupTitle() }}
          </text>
          <text class="header-subtitle-premium" v-if="!chargeError">
            {{ getPopupSubtitle() }}
          </text>
          <text class="error-message-premium" v-if="chargeError">
            {{ errorMessage }}
          </text>
        </view>

        <!-- 充电步骤状态列表 -->
        <view class="charging-steps-premium" v-if="!chargeError">
          <!-- 进度条 -->
          <view class="progress-track-premium">
            <view class="progress-fill-premium" :style="{ width: getProgressWidth() }"></view>
          </view>

          <!-- 步骤列表 -->
          <view class="steps-container-premium">
            <!-- 步骤1: 扫码识别 -->
            <view class="step-item-premium" :class="getStepClass(1)">
              <view class="step-icon-premium">
                <text class="step-number" v-if="chargingStep < 1">1</text>
                <text class="step-check" v-else-if="chargingStep > 1">✓</text>
                <view class="step-loading" v-else></view>
              </view>
              <view class="step-content-premium">
                <text class="step-text-premium">{{ $t('popup.step1') || 'Scanning QR Code' }}</text>
                <text class="step-desc-premium" v-if="chargingStep === 1">{{ $t('popup.connectingToPile') || 'Connecting to charging pile' }}</text>
              </view>
            </view>

            <!-- 步骤2: 验证信息 -->
            <view class="step-item-premium" :class="getStepClass(2)">
              <view class="step-icon-premium">
                <text class="step-number" v-if="chargingStep < 2">2</text>
                <text class="step-check" v-else-if="chargingStep > 2">✓</text>
                <view class="step-loading" v-else></view>
              </view>
              <view class="step-content-premium">
                <text class="step-text-premium">{{ $t('popup.step2') || 'Verifying Information' }}</text>
                <text class="step-desc-premium" v-if="chargingStep === 2">{{ $t('popup.verifyingPile') || 'Verifying charging pile information' }}</text>
              </view>
            </view>

            <!-- 步骤3: 处理支付 -->
            <view class="step-item-premium" :class="getStepClass(3)">
              <view class="step-icon-premium">
                <text class="step-number" v-if="chargingStep < 3">3</text>
                <text class="step-check" v-else-if="chargingStep > 3">✓</text>
                <view class="step-loading" v-else></view>
              </view>
              <view class="step-content-premium">
                <text class="step-text-premium">{{ $t('popup.step3') || 'Processing Payment' }}</text>
                <text class="step-desc-premium" v-if="chargingStep === 3">{{ $t('popup.processingPayment') || 'Processing payment information' }}</text>
              </view>
            </view>

            <!-- 步骤4: 开始充电 -->
            <view class="step-item-premium" :class="getStepClass(4)">
              <view class="step-icon-premium">
                <text class="step-number" v-if="chargingStep < 4">4</text>
                <text class="step-check" v-else>✓</text>
              </view>
              <view class="step-content-premium">
                <text class="step-text-premium">{{ $t('popup.step4') || 'Starting Charging' }}</text>
                <text class="step-desc-premium" v-if="chargingStep === 4">{{ $t('popup.chargingStarted') || 'Charging started successfully' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 底部操作区域 -->
        <view class="popup-footer-premium">
          <!-- 错误时显示关闭按钮 -->
          <view class="error-actions-premium" v-if="chargeError">
            <view class="close-button-premium" @click="handleCancelCharging">
              <text class="button-text-premium">{{ $t('common.close') || 'Close' }}</text>
            </view>
          </view>

          <!-- 底部提示信息 -->
          <view class="bottom-tips-premium" v-if="!chargeError">
            <view class="tip-icon-premium">💡</view>
            <text class="tip-text-premium">{{ $t('popup.keepConnection') || 'Please keep your phone connected to the charging pile' }}</text>
          </view>

          <!-- 错误提示信息 -->
          <view class="error-tips-premium" v-if="chargeError">
            <view class="error-icon-tip-premium">⚠️</view>
            <text class="error-tip-text-premium">{{ errorMessage || $t('popup.chargingFailed') || 'Charging process failed' }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { getChargingStationDetail, getChargingPileList, getPriceDetails } from '@/api'
import ImagePreview from '@/components/common/ImagePreview.vue'
import ChargingPileScanner from '@/components/ChargingPileScanner.vue'
import { getRouteParams } from '@/utils/route'

// 使用国际化
const { t } = useI18n()

// 状态栏高度
const statusBarHeight = ref(0)
// 是否收藏
const isFavorite = ref(false)
// 站点ID
const stationId = ref('')
// 当前选中的tab
const activeTab = ref('0')
// 充电桩数据按状态分组
const pilesByState = ref([])
// 充电桩加载状态
const isLoadingPiles = ref(false)
// 扫码相关状态
const scanSuccess = ref(false)
const showChargingLoading = ref(false)
const chargingStep = ref(0)
const orderInfo = ref(null)
const orderNum = ref('')
const isCharging = ref(false)
const currentModelType = ref('default')
// 错误状态
const chargeError = ref(false)
const errorMessage = ref('')
// 组件引用
const chargingPileScannerRef = ref(null)
// 用户位置
const userLocation = reactive({
  lat: 48.8566, // 默认位置（巴黎）
  lng: 2.3522
})
// 站点详情数据
const stationDetail = ref({
  name: '',
  hours: '',
  lastCharge: '',
  tags: [],
  address: '',
  distance: '',
  status: '',
  available: 0,
  total: 0,
  latitude: 0,
  longitude: 0,
  plotImgUrls: '',
  currentPeriod: {
    time: '',
    price: '',
    unit: ''
  },
  membershipPrice: {
    price: '',
    unit: ''
  },
  nextPeriod: {
    time: '',
    price: '',
    unit: ''
  },
  parkingFee: '',
  piles: [],
  stationInfo: {
    businessHours: '',
    serviceProvider: '',
    invoiceProvision: '',
    customerService: ''
  }
})

// 页面加载时获取状态栏高度和站点ID
onMounted(async () => {
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight

  // 不再使用document.documentElement，因为在APP环境中不存在
  // document.documentElement.style.setProperty('--status-bar-height', `${statusBarHeight.value}px`)
  // 默认选中第一个时间段
  selectedPeriod.value = '07:30-19:30'

  // 获取页面参数（静态导入，避免动态导入触发代码分割）
  const { plotId, plotNum } = getRouteParams(['plotId','plotNum'])
  console.log('🎯 获取到的plotId参数:', plotId)

  // 先获取用户位置，然后再获取站点详情
  try {
    await getCurrentLocation()
    console.log('🌍 用户位置获取成功:', userLocation)
  } catch (error) {
    console.warn('⚠️ 获取用户位置失败，使用默认位置:', error)
  }

  if (plotId) {
    console.log('✅ 使用页面参数中的plotId:', plotId)
    stationId.value = plotId
    fetchStationDetail(plotNum)
    setTimeout(()=>{
      fetchChargingPileList(plotId)
    },1000)
    fetchPriceDetails(plotId)
  } else {
    console.warn('⚠️ 未获取到plotId参数，尝试获取其他参数')

    // 尝试从页面参数中获取
    const query = uni.getLaunchOptionsSync().query || {}
    const id = query.plotId || query.id
    console.log('🔍 从启动参数中查找ID:', query, 'ID:', id)

    if (id) {
      console.log('✅ 使用启动参数中的ID:', id)
      stationId.value = id
      fetchStationDetail(id)
      fetchChargingPileList(id)
      fetchPriceDetails(id)
    } else {
      console.error('❌ 未获取到站点ID')
      uni.showToast({
        title: $t('toast.stationInfoMissing') || 'Unable to get station information',
        icon: 'none'
      })
    }
  }
})
const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    console.log('开始获取用户位置...')

    // 策略1: 尝试使用WGS84坐标系（国际标准，适用于非洲地区）
    uni.getLocation({
      type: 'wgs84', // 使用国际标准坐标系
      isHighAccuracy: true, // 启用高精度定位
      timeout: 15000, // 增加超时时间
      maximumAge: 0, // 不使用缓存位置
      success: (res) => {
        console.log('WGS84定位成功:', res)

        // 验证定位是否合理（科特迪瓦大致范围：纬度4-11°N，经度-8到-3°W）
        const isInCoteDIvoire = res.latitude >= 4 && res.latitude <= 11 &&
                               res.longitude >= -8 && res.longitude <= -3

        if (isInCoteDIvoire || res.accuracy < 1000) {
          userLocation.lat = res.latitude
          userLocation.lng = res.longitude
          console.log('✅ 定位验证通过，精度:', res.accuracy + 'm')
          resolve(res)
        } else {
          console.warn('⚠️ WGS84定位结果异常，尝试备用方案')
          tryAlternativeLocation(resolve, reject)
        }
      },
      fail: (err) => {
        console.error('WGS84定位失败:', err)
        tryAlternativeLocation(resolve, reject)
      }
    })
  })
}

// 备用定位方案
const tryAlternativeLocation = (resolve, reject) => {
  console.log('尝试备用定位方案...')

  // 策略2: 使用浏览器原生Geolocation API（更准确的GPS定位）
  if (navigator && navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        console.log('浏览器GPS定位成功:', position)
        const lat = position.coords.latitude
        const lng = position.coords.longitude
        const accuracy = position.coords.accuracy

        // 验证定位合理性
        const isReasonable = lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180

        if (isReasonable && accuracy < 5000) {
          userLocation.lat = lat
          userLocation.lng = lng
          console.log('✅ 浏览器GPS定位验证通过，精度:', accuracy + 'm')
          resolve({ latitude: lat, longitude: lng, accuracy })
        } else {
          console.warn('⚠️ 浏览器定位结果异常，使用默认方案')
          useDefaultLocation(resolve)
        }
      },
      (error) => {
        console.error('浏览器定位失败:', error)
        useDefaultLocation(resolve)
      },
      {
        enableHighAccuracy: true, // 强制使用GPS
        timeout: 20000,
        maximumAge: 0
      }
    )
  } else {
    useDefaultLocation(resolve)
  }
}

// 默认定位方案（保持默认坐标）
const useDefaultLocation = (resolve) => {
  console.log('使用默认定位方案，保持默认坐标')
  // 保持默认位置（巴黎），不设置为null
  // userLocation.lat = 48.8566
  // userLocation.lng = 2.3522
  console.log('🌍 使用默认位置:', userLocation.lat, userLocation.lng)
  resolve({
    latitude: userLocation.lat,
    longitude: userLocation.lng,
    useDefaultLocation: true,
    message: 'Using default location (Paris)'
  })
}
// 计算两点间距离（公里）
const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371 // 地球半径（公里）
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return Math.round(R * c * 100) / 100 // 保留两位小数
}

// 验证坐标是否在科特迪瓦范围内
const isLocationInCoteDIvoire = (lat, lng) => {
  // 科特迪瓦大致边界：纬度4-11°N，经度-8到-3°W
  return lat >= 4 && lat <= 11 && lng >= -8 && lng <= -3
}

// 验证坐标是否在西非范围内
const isLocationInWestAfrica = (lat, lng) => {
  // 西非大致范围：纬度0-20°N，经度-20到10°E
  return lat >= 0 && lat <= 20 && lng >= -20 && lng <= 10
}
// 获取站点详情数据
const fetchStationDetail = async (id) => {
  try {
    console.log('🎯 开始获取站点详情，ID:', id)
    console.log('🌍 当前用户位置:', userLocation)

    // 先确保获取到用户位置
    if (!userLocation.lat || !userLocation.lng) {
      console.log('📍 用户位置为空，重新获取定位...')
      try {
        await getCurrentLocation()
        console.log('📍 重新获取定位成功:', userLocation)
      } catch (error) {
        console.warn('📍 重新获取定位失败:', error)
      }
    }

    uni.showLoading({
      title: 'Loading...'
    })

    // 构建请求参数，确保坐标有效
    const result = { id: id }

    // 验证坐标有效性并添加coordinate参数
    const isValidLat = userLocation.lat !== null && userLocation.lat !== undefined && !isNaN(userLocation.lat)
    const isValidLng = userLocation.lng !== null && userLocation.lng !== undefined && !isNaN(userLocation.lng)

    if (isValidLat && isValidLng) {
      result.coordinate = `${userLocation.lng},${userLocation.lat}`
      console.log('✅ 使用用户坐标:', result.coordinate)
    } else {
      console.log('⚠️ 用户坐标无效，详细信息:')
      console.log('  - userLocation.lat:', userLocation.lat, typeof userLocation.lat, 'isValid:', isValidLat)
      console.log('  - userLocation.lng:', userLocation.lng, typeof userLocation.lng, 'isValid:', isValidLng)
      console.log('⚠️ 不传递coordinate参数，让后端使用默认逻辑')
    }

    console.log('📤 请求参数:', result)

    // 调用API获取站点详情
    const response = await getChargingStationDetail(result)

    console.log('📥 站点详情响应:', response)

    if (response && response.code === 200 && response.data) {
      const data = response.data

      // 更新站点详情数据
      stationDetail.value = {
        id: data.plotId || '',
        name: data.plotName || 'Charging Station',
        hours: data.businessHours || '00:00-23:59',
        lastCharge: data.title || 'Someone charged recently',
        tags: parseStationTags(data),
        address: data.address || '',
        distance: formatDistance(data.distance),
        status: data.plotStatus === '0' ? 'Idle' : 'Busy',
        available: data.countMap?.fastCount || 0,
        total: data.countMap?.fastSumCount || 0,
        latitude: parseFloat(data.latitude) || 0,
        longitude: parseFloat(data.longitude) || 0,
        plotImgUrls: data.plotImgUrls || '',
        currentPeriod: {
          time: '12:00-14:00',
          price: data.price || '0',
          unit: 'F/kWh'
        },
        membershipPrice: {
          price: data.vipPrice || '0',
          unit: 'fafc/kWh'
        },
        nextPeriod: {
          time: '14:00-16:00',
          price: data.nextPrice || '0',
          unit: 'F/kWh'
        },
        parkingFee: getParkingFeeText(data),
        piles: [], // 这里需要另外调用接口获取充电桩列表
        stationInfo: {
          businessHours: data.businessHours || '24h/24, 7j/7',
          serviceProvider: data.invoiceParty || 'Arnio',
          invoiceProvision: data.invoiceParty || 'Arnio',
          customerService: data.tel || '+225 **********'
        }
      }

      console.log('更新后的站点详情:', stationDetail.value)


    } else {
      throw new Error('获取站点详情失败')
    }
  } catch (error) {
    console.error('❌ 获取站点详情失败:', error)
    console.error('❌ 错误详情:', error.message, error.stack)
    uni.showToast({
      title: $t('toast.requestFailed') || 'Failed to get station details',
      icon: 'none'
    })

    // 使用默认数据确保页面有内容显示
    stationDetail.value = {
      id: stationId.value,
      name: 'Charging Station',
      hours: '00:00-23:59',
      lastCharge: 'Someone charged recently',
      tags: ['Free1 hour', 'Open 24 hours'],
      address: 'No address available',
      distance: '0',
      status: 'Idle',
      available: 10,
      total: 24,
      latitude: 48.8566, // 默认巴黎坐标
      longitude: 2.3522,
      plotImgUrls: '',
      currentPeriod: {
        time: '12:00-14:00',
        price: '--',
        unit: 'F/kWh'
      },
      membershipPrice: {
        price: '239',
        unit: 'fafc/kWh'
      },
      nextPeriod: {
        time: '14:00-16:00',
        price: '200',
        unit: 'F/kWh'
      },
      parkingFee: 'Free parking for half an hour. For any time exceeding this period, the parking fee shall be charged in accordance with the regulations of the parking lot.',
      piles: [],
      stationInfo: {
        businessHours: '24h/24, 7j/7',
        serviceProvider: 'Arnio',
        invoiceProvision: 'Arnio',
        customerService: '+225 **********'
      }
    }
  } finally {
    uni.hideLoading()
  }
}

// 解析站点标签
const parseStationTags = (data) => {
  const tags = []

  // 解析支持设施
  if (data.supportingFacilities) {
    const facilities = data.supportingFacilities.split(',')

    if (facilities.includes('1')) tags.push('WiFi')
    if (facilities.includes('2')) tags.push('Bathroom')
    if (facilities.includes('3')) tags.push('Restaurant')
    if (facilities.includes('4')) tags.push('Convenience Store')
    if (facilities.includes('5')) tags.push('Rest Area')
    if (facilities.includes('6')) tags.push('Parking')
    if (facilities.includes('7')) tags.push('Open 24 hours')
  }

  // 添加免费停车信息
  if (data.freeTime) {
    tags.push(`Free${data.freeTime} hour`)
  }

  return tags.length > 0 ? tags : ['Free1 hour', 'Open 24 hours']
}

// 获取停车费用文本
const getParkingFeeText = (data) => {
  if (data.freeTime) {
    return `Free parking for ${data.freeTime} hour. For any time exceeding this period, the parking fee shall be charged in accordance with the regulations of the parking lot.`
  }
  return 'Free parking for half an hour. For any time exceeding this period, the parking fee shall be charged in accordance with the regulations of the parking lot.'
}

// 格式化距离
const formatDistance = (distance) => {
  if (distance === undefined || distance === null) return '0'

  // 如果已经是字符串且包含单位，直接返回
  if (typeof distance === 'string' && (distance.includes('km') || distance.includes('m'))) {
    return distance.replace('km', '')
  }

  // 转换为数字
  const distanceNum = parseFloat(distance)

  if (isNaN(distanceNum)) return '0'

  // 如果大于等于1公里，显示为公里
  if (distanceNum >= 1000) {
    return (distanceNum / 1000).toFixed(2)
  }

  // 否则显示为米，但转换为公里
  return (distanceNum / 1000).toFixed(2)
}

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 切换收藏状态
const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value
  // 这里可以添加收藏/取消收藏的逻辑
  uni.showToast({
    title: isFavorite.value ? ($t('toast.favorited') || 'Favorited') : ($t('toast.unfavorited') || 'Unfavorited'),
    icon: 'none'
  })
}

// 切换tab
const switchTab = (tab) => {
  activeTab.value = tab
}

// 复制充电桩编码
const copyCode = (code) => {
  uni.setClipboardData({
    data: code,
    success: () => {
      uni.showToast({
        title: t('toast.copied'),
        icon: 'success',
        duration: 1500,
        mask: true  // 添加遮罩层，确保提示显示在最上层
      })
    }
  })
}

// 获取充电桩状态图标
const getPileStatusIcon = (status) => {
  switch (status) {
    case 'disponible':
      return '/static/images/available.png'
    case 'charging':
      return '/static/images/charging.png'
    case 'occupied':
      return '/static/images/occupied.png'
    default:
      return '/static/images/charging.png'
  }
}

// 处理图片加载错误
const handleImageError = (index) => {
  console.log(`图片加载失败，索引: ${index}，使用默认图片`)
  // 如果图片加载失败，替换为默认图片
  if (stationDetail.value && stationDetail.value.plotImgUrls) {
    const imageList = stationDetail.value.plotImgUrls.split(',').filter(url => url.trim())
    if (imageList[index]) {
      imageList[index] = '/static/images/group-2341.png'
      stationDetail.value.plotImgUrls = imageList.join(',')
    }
  }
}

// 筛选充电桩
const filteredPiles = computed(() => {
  // 根据当前选中的tab，从pilesByState中获取对应的数据
  const currentStateData = pilesByState.value.find(item => item.state === activeTab.value)
  return currentStateData ? currentStateData.list : []
})

// 获取各个tab的数量
const getTabCount = (state) => {
  const stateData = pilesByState.value.find(item => item.state === state)
  return stateData ? stateData.total : 0
}

// 动态生成可用的tabs
const availableTabs = computed(() => {
  const tabs = []

  // 根据接口返回的数据动态生成tabs
  pilesByState.value.forEach(group => {
    let label = ''
    switch (group.state) {
      case '0':
        label = t('station.all')
        break
      case '1':
        label = t('station.idleStatus')
        break
      case '2':
        label = t('station.fastCharging')
        break
      case '3':
        label = t('station.occupiedStatus')
        break
      default:
        label = `状态${group.state}`
    }

    tabs.push({
      state: group.state,
      label: label
    })
  })

  // 如果当前activeTab不在可用tabs中，设置为第一个可用tab
  if (tabs.length > 0 && !tabs.find(tab => tab.state === activeTab.value)) {
    activeTab.value = tabs[0].state
  }

  return tabs
})

// 价格详情弹出层状态
const showPriceDetails = ref(false)
// 充电桩列表弹窗状态
const showPileListPopup = ref(false)
// 当前选中的充电桩类型
const selectedPileType = ref('all')

// 图片预览相关状态
const showImagePreview = ref(false)
const previewImageIndex = ref(0)

// 导航弹窗状态
const showNavigationDialog = ref(false)

// 打开价格详情
const openPriceDetails = () => {
  showPriceDetails.value = true

  // 防止背景滚动 - 使用uni-app API代替document.body
  // #ifdef H5
  document.body && (document.body.style.overflow = 'hidden')
  // #endif

  // 如果还没有获取价格详情，则获取
  if (tariffList.value.length === 0) {
    fetchPriceDetails(stationId.value)
  }
}

// 关闭价格详情
const closePriceDetails = () => {
  showPriceDetails.value = false

  // 恢复背景滚动 - 使用uni-app API代替document.body
  // #ifdef H5
  document.body && (document.body.style.overflow = '')
  // #endif
}

// 打开充电桩列表弹窗
const openPileListPopup = () => {
  showPileListPopup.value = true

  // 防止背景滚动 - 使用uni-app API代替document.body
  // #ifdef H5
  document.body && (document.body.style.overflow = 'hidden')
  // #endif

  // 如果还没有获取充电桩列表，则获取
  if (!stationDetail.value.piles || stationDetail.value.piles.length === 0) {
    fetchChargingPileList(stationId.value)
  }
}

// 关闭充电桩列表弹窗
const closePileListPopup = () => {
  showPileListPopup.value = false

  // 恢复背景滚动 - 使用uni-app API代替document.body
  // #ifdef H5
  document.body && (document.body.style.overflow = '')
  // #endif
}

// 切换充电桩类型
const switchPileType = (type) => {
  selectedPileType.value = type
}

// 价格详情数据
const tariffList = ref([])
const currentPeriodIndex = ref(0)

// 计算有效图片列表
const validImages = computed(() => {
  if (!stationDetail.value.plotImgUrls) return []

  return stationDetail.value.plotImgUrls
    .split(',')
    .map(url => url.trim())
    .filter(url => url && url !== '' && !url.includes('/static/images/group-2341.png'))
})

// 检查是否有有效图片
const hasValidImages = computed(() => {
  return validImages.value.length > 0
})

// 计算各类型充电桩数量
const pileTypeCounts = computed(() => {
  return {
    all: getTabCount('0'),
    idle: getTabCount('1'),
    '40KW': getTabCount('2')
  }
})

// 充电桩列表弹窗中筛选充电桩
const filteredPopupPiles = computed(() => {
  // 获取所有充电桩数据
  const allPiles = []
  pilesByState.value.forEach(group => {
    allPiles.push(...group.list)
  })

  switch (selectedPileType.value) {
    case 'idle':
      return allPiles.filter(p => p.status === 'disponible')
    case '40KW':
      return allPiles.filter(p => p.power.includes('40') || p.power.includes('50'))
    default:
      return allPiles
  }
})

// 获取充电桩状态图标
const getStatusIcon = (status) => {
  switch (status) {
    case 'disponible':
      return '/static/images/available.png'
    case 'charging':
      return '/static/images/charging.png'
    case 'occupied':
      return '/static/images/occupied.png'
    default:
      return '/static/images/charging.png'
  }
}

// 添加选中时段的状态和方法
const selectedPeriod = ref('')

const selectPeriod = (timeRange) => {
  selectedPeriod.value = timeRange
}

// 获取价格详情
const fetchPriceDetails = async (plotId) => {
  try {
    console.log('获取价格详情，站点ID:', plotId)

    // 调用API获取价格详情
    const response = await getPriceDetails(plotId)

    console.log('价格详情响应:', response)

    if (response && response.code === 200 && response.data && Array.isArray(response.data)) {
      // 处理价格详情数据
      const priceData = response.data

      // 转换为tariffList格式
      tariffList.value = priceData.map((item, index) => {
        // 检查是否为当前时段
        if (item.isPeriod) {
          currentPeriodIndex.value = index
        }

        // 获取时间范围
        const timeRange = item.timeSpan

        // 返回转换后的数据
        return {
          timeRange,
          memberPrice: {
            total: item.vipPrice.price,
            electricCharge: item.vipPrice.electricCharge,
            serviceFee: item.vipPrice.coverCharge
          },
          sitePrice: {
            total: item.normalPrice.price,
            electricCharge: item.normalPrice.electricCharge,
            serviceFee: item.normalPrice.coverCharge
          },
          activityPrice: {
            total: item.activityPrice.price,
            electricCharge: item.activityPrice.electricCharge,
            serviceFee: item.activityPrice.coverCharge
          },
          isPeriod: item.isPeriod
        }
      })

      console.log('处理后的价格详情:', tariffList.value)

      // 更新当前价格信息
      if (tariffList.value.length > 0) {
        const currentPeriod = tariffList.value.find(item => item.isPeriod) || tariffList.value[0]
        console.log('222222222222222',currentPeriod);
        
        // 更新站点详情中的价格信息
        stationDetail.value.currentPeriod = {
          time: currentPeriod.timeRange,
          price: currentPeriod.sitePrice.total,
          unit: 'F/kWh'
        }

        stationDetail.value.membershipPrice = {
          price: currentPeriod.memberPrice.total,
          unit: 'fafc/kWh'
        }

        // 找到下一个时段
        const currentIndex = tariffList.value.findIndex(item => item.isPeriod)
        const nextIndex = (currentIndex + 1) % tariffList.value.length
        const nextPeriod = tariffList.value[nextIndex]

        stationDetail.value.nextPeriod = {
          time: nextPeriod.timeRange,
          price: nextPeriod.sitePrice.total,
          unit: 'F/kWh'
        }
      }
    } else {
      throw new Error('获取价格详情失败')
    }
  } catch (error) {
    console.error('获取价格详情失败:', error)
    // 使用默认价格数据
    tariffList.value = [
      {
        timeRange: '00:00:00 - 07:59:59',
        memberPrice: { total: '0.56500', electricCharge: '0.5400', serviceFee: '0.02500' },
        sitePrice: { total: '0.5900', electricCharge: '0.5400', serviceFee: '0.0500' },
        activityPrice: { total: '0.5900', electricCharge: '0.5400', serviceFee: '0.0500' },
        isPeriod: false
      },
      {
        timeRange: '08:00:00 - 21:59:59',
        memberPrice: { total: '0.78500', electricCharge: '0.6800', serviceFee: '0.10500' },
        sitePrice: { total: '0.8900', electricCharge: '0.6800', serviceFee: '0.2100' },
        activityPrice: { total: '0.8900', electricCharge: '0.6800', serviceFee: '0.2100' },
        isPeriod: true
      },
      {
        timeRange: '22:00:00 - 23:59:59',
        memberPrice: { total: '0.66500', electricCharge: '0.6400', serviceFee: '0.02500' },
        sitePrice: { total: '0.6900', electricCharge: '0.6400', serviceFee: '0.0500' },
        activityPrice: { total: '0.6900', electricCharge: '0.6400', serviceFee: '0.0500' },
        isPeriod: false
      }
    ]


  }
}

// 获取充电桩列表 - 骨架屏版本
const fetchChargingPileList = async (plotId) => {
  try {
    console.log('获取充电桩列表，站点ID:', plotId)

    // 设置加载状态
    isLoadingPiles.value = true
    // 清空之前的数据
    pilesByState.value = []

    // 调用API获取充电桩列表
    const response = await getChargingPileList(plotId)

    if (response?.code === 200 && response?.data) {
      // 直接使用接口返回的数据结构
      pilesByState.value = response.data.map(group => ({
        state: group.state,
        total: group.total,
        list: group.list.map(pile => ({
          id: pile.chargingId || Math.random().toString(),
          code: pile.spearName || 'Unknown',
          power: `${pile.power || 40}kw GTB`,
          status: getStatusFromChargingState(pile.spearChargingState),
          chargingId: pile.chargingId,
          spearNum: pile.spearNum,
          spearChargingState: pile.spearChargingState,
          spearUseStatus: pile.spearUseStatus
        }))
      }))

      console.log('处理后的充电桩数据:', pilesByState.value)
    }
  } catch (error) {
    console.error('获取充电桩失败:', error)

    // 显示错误提示
    uni.showToast({
      title: $t('toast.requestFailed') || 'Failed to get charging piles',
      icon: 'none',
      duration: 2000
    })

    // 设置空数据
    pilesByState.value = []
  } finally {
    // 无论成功失败都要关闭加载状态
    isLoadingPiles.value = false
  }
}

// 根据充电状态码获取状态
const getStatusFromChargingState = (chargingState) => {
  switch (chargingState) {
    case '00': return 'disponible' // 空闲/可用
    case '01': return 'disponible' // 空闲
    case '02': return 'charging'   // 充电中
    case '03': return 'occupied'   // 占用/故障
    case '04': return 'occupied'   // 离线
    default:
      return 'disponible'
  }
}

// 根据充电状态码获取法语状态文本
const getChargingStateText = (spearChargingState) => {
  switch (spearChargingState) {
    case '00': return 'Hors ligne'      // 离线
    case '01': return 'Panne'           // 故障
    case '02': return 'Libre'           // 空闲
    case '03': return 'En charge'       // 充电中
    case '04': return 'Arrêté'          // 已停止
    case '05': return 'Pistolet inséré' // 已插枪
    default: return 'État inconnu'      // 未知状态
  }
}

// 根据充电状态码获取颜色
const getStatusColor = (spearChargingState) => {
  switch (spearChargingState) {
    case '00': return '#8A8A8A'  // 离线 - 灰色
    case '01': return '#E53E3E'  // 故障 - 红色
    case '02': return '#38A169'  // 空闲 - 绿色
    case '03': return '#3182CE'  // 充电中 - 蓝色
    case '04': return '#DD6B20'  // 已停止 - 橙色
    case '05': return '#805AD5'  // 已插枪 - 紫色
    default: return '#333333'    // 默认颜色
  }
}

// 根据状态码获取状态
const getStatusFromState = (state) => {
  console.log('转换充电桩状态:', state)
  switch (state) {
    case '00': return 'disponible' // 空闲/可用
    case '01': return 'disponible' // 空闲
    case '02': return 'charging'   // 充电中
    case '03': return 'occupied'   // 占用/故障
    case '04': return 'occupied'   // 离线
    default:
      console.warn('未知的充电桩状态码:', state)
      return 'disponible'
  }
}

// 格式化价格，只保留整数部分
const formatPrice = (price) => {
  if (!price) return '0';

  // 转换为数字并取整数部分
  const num = parseFloat(price);

  // 返回整数部分
  return Math.floor(num).toString();
}

// 触发扫码
const triggerScan = () => {
  console.log('点击扫码按钮，触发ChargingPileScanner组件扫码')
  if (chargingPileScannerRef.value) {
    chargingPileScannerRef.value.startChargingPileScan()
  }
}

// 处理扫码组件的扫描成功事件
const handleScannerSuccess = (data) => {
  console.log('扫码成功:', data)
  scanSuccess.value = true
}

// 处理充电测试事件
const handleChargingTest = (data) => {
  console.log('=== 收到charging-test事件 ===')
  console.log('测试数据:', data)
}

// 处理扫码组件的充电开始事件
const handleChargingStart = (data) => {
  console.log('=== 收到charging-start事件 ===')
  console.log('充电开始数据:', data)

  const { orderInfo: orderInfoData, orderNumber, pileInfo, response } = data

  // 更新状态
  orderInfo.value = orderInfoData
  orderNum.value = orderNumber
  isCharging.value = true

  console.log('更新状态完成，准备跳转')
  console.log('订单号:', orderNumber)
  console.log('充电桩信息:', pileInfo)

  // 延迟500毫秒再跳转，让用户看到成功提示
  console.log('设置500ms延迟跳转定时器')
  setTimeout(() => {
    console.log('=== 开始执行跳转逻辑 ===')

    // 在跳转前关闭弹窗
    console.log('跳转前关闭弹窗')
    chargingStep.value = 0 // 重置步骤，这会关闭弹窗

    // 跳转到充电状态页面，传递订单号和订单ID
    let url = '/pages/charging-status/index'
    const params = []

    if (orderNumber) {
      console.log('添加orderNumber参数:', orderNumber)
      params.push(`orderNumber=${encodeURIComponent(orderNumber)}`)
    }

    if (orderInfoData && orderInfoData.orderId) {
      console.log('添加orderId参数:', orderInfoData.orderId)
      params.push(`orderId=${encodeURIComponent(orderInfoData.orderId)}`)
    }

    if (pileInfo && pileInfo.id) {
      console.log('添加pileId参数:', pileInfo.id)
      params.push(`pileId=${encodeURIComponent(pileInfo.id)}`)
    }

    if (params.length > 0) {
      url += '?' + params.join('&')
    }

    console.log('跳转URL:', url)

    uni.navigateTo({
      url,
      success: () => {
        console.log('跳转充电状态页面成功')
      },
      fail: (error) => {
        console.error('跳转充电状态页面失败:', error)
      }
    })
  }, 500)
}

// 处理扫码组件的扫描失败事件
const handleScannerFail = (data) => {
  console.log('Scan failed:', data)
  scanSuccess.value = false
}

// 处理扫码组件的充电错误事件
const handleChargingError = (data) => {
  console.log('Charging error:', data)

  // 设置错误状态，保持弹窗打开，让用户手动关闭
  chargeError.value = true
  errorMessage.value = data.errorMessage || t('charging.failed')
  scanSuccess.value = false

  // 不重置chargingStep，保持弹窗打开
  console.log('Charging failed, keep popup open, waiting for user to close manually:', errorMessage.value)
}

// 处理扫码组件的loading-change事件
const handleLoadingChange = (loading) => {
  console.log('Scan component loading state change:', loading)
  showChargingLoading.value = loading
}

// 处理扫码组件的step-change事件
const handleStepChange = (step) => {
  console.log('=== 扫码组件步骤变化 ===')
  console.log('新步骤:', step)
  chargingStep.value = step
}

// 处理扫码组件的model-type-change事件
const handleModelTypeChange = (modelType) => {
  console.log('模型类型变化:', modelType)
  currentModelType.value = modelType
}

// 关闭充电弹窗
const closeChargingPopup = () => {
  console.log('手动关闭充电弹窗')
  chargingStep.value = 0
  chargeError.value = false
  errorMessage.value = ''
  scanSuccess.value = false
  showChargingLoading.value = false
}

// 获取弹窗标题
const getPopupTitle = () => {
  if (chargeError.value) {
    return t('popup.chargingFailed') || 'Charging Failed'
  }

  switch (chargingStep.value) {
    case 1:
      return t('popup.step1') || 'Scanning QR Code'
    case 2:
      return t('popup.step2') || 'Verifying Information'
    case 3:
      return t('popup.step3') || 'Processing Payment'
    case 4:
      return t('popup.chargingStarted') || 'Charging Started Successfully'
    default:
      return t('popup.startingCharging') || 'Starting Charging'
  }
}

// 获取弹窗副标题
const getPopupSubtitle = () => {
  switch (chargingStep.value) {
    case 1:
      return t('popup.connectingToPile') || 'Connecting to charging pile...'
    case 2:
      return t('popup.verifyingPile') || 'Verifying charging pile information...'
    case 3:
      return t('popup.processingPayment') || 'Processing payment information...'
    case 4:
      return t('popup.chargingStarted') || 'Charging started successfully!'
    default:
      return t('popup.preparingCharging') || 'Preparing to start charging...'
  }
}

// 获取进度条宽度
const getProgressWidth = () => {
  const progress = (chargingStep.value / 4) * 100
  return `${Math.min(progress, 100)}%`
}

// 处理取消充电
const handleCancelCharging = () => {
  console.log('用户取消充电')
  closeChargingPopup()
}

// 获取步骤样式类
const getStepClass = (stepNumber) => {
  if (chargeError.value) {
    return stepNumber <= chargingStep.value ? 'error' : 'pending'
  }

  if (stepNumber < chargingStep.value) {
    return 'completed'
  } else if (stepNumber === chargingStep.value) {
    return 'active'
  } else {
    return 'pending'
  }
}

// 客服按钮点击事件
const handleCustomerService = () => {
  console.log('点击客服按钮 - 直接跳转到WhatsApp')

  const whatsappUrl = 'https://api.whatsapp.com/send/?phone=225**********&text&type=phone_number&app_absent=0'

  // 根据平台打开WhatsApp
  // #ifdef APP-PLUS
  // APP环境：使用 plus.runtime.openURL 打开外部应用
  if (typeof plus !== 'undefined' && plus.runtime) {
    plus.runtime.openURL(whatsappUrl, (error) => {
      console.error('❌ 打开 WhatsApp 失败:', error)
      uni.showToast({
        title: $t('toast.requestFailed') || 'Unable to open WhatsApp',
        icon: 'none',
        duration: 2000
      })
    })
  } else {
    console.warn('⚠️ plus 对象不可用')
    uni.showToast({
      title: $t('toast.requestFailed') || 'Unable to open WhatsApp',
      icon: 'none'
    })
  }
  // #endif

  // #ifdef H5
  // H5环境：在新标签页打开
  window.open(whatsappUrl, '_blank')
  // #endif

  // #ifdef MP
  // 小程序环境：提示用户复制链接
  uni.setClipboardData({
    data: whatsappUrl,
    success: () => {
      uni.showToast({
        title: $t('toast.copied') || 'Copied',
        icon: 'success',
        duration: 2000
      })
    }
  })
  // #endif
}

// 打开图片预览
const openImagePreview = (index) => {
  console.log('点击图片预览，索引:', index)
  console.log('有效图片列表:', validImages.value)

  if (validImages.value.length === 0) {
    console.warn('没有可预览的图片')
    return
  }

  // 使用 uni-app 内置的图片预览
  uni.previewImage({
    current: index,
    urls: validImages.value,
    success: () => {
      console.log('图片预览打开成功')
    },
    fail: (err) => {
      console.error('图片预览打开失败:', err)
      // 如果内置预览失败，使用自定义组件
      previewImageIndex.value = index
      showImagePreview.value = true
    }
  })
}

// 关闭图片预览
const closeImagePreview = () => {
  showImagePreview.value = false
}

// 预览图片变化
const onPreviewImageChange = (index) => {
  previewImageIndex.value = index
}

// 打开导航对话框
const openNavigationDialog = async () => {
  console.log('🗺️ 点击导航按钮')

  // 检查是否有充电站坐标信息
  if (!stationDetail.value.latitude || !stationDetail.value.longitude) {
    uni.showToast({
      title: $t('toast.locationFailed') || 'Unable to get station location',
      icon: 'none',
      duration: 2000
    })
    return
  }

  // 重新获取当前位置以确保准确性
  try {
    uni.showLoading({ title: uni.$t('map.gettingLocation'), mask: true })
    await getCurrentLocation()
    uni.hideLoading()

    // 简单记录距离信息，不弹窗提示
    if (userLocation.lat && userLocation.lng) {
      const distance = calculateDistance(
        userLocation.lat, userLocation.lng,
        stationDetail.value.latitude, stationDetail.value.longitude
      )
      console.log('📏 用户到充电站距离:', distance + 'km')

      // 如果距离过远，在控制台记录但不弹窗
      if (distance > 1000) {
        console.warn('⚠️ 检测到距离异常，将使用自动定位:', distance + 'km')
      }
    }
  } catch (error) {
    uni.hideLoading()
    console.warn('重新定位失败:', error)
  }

  // 显示自定义导航弹窗
  showNavigationDialog.value = true
}

// 关闭导航弹窗
const closeNavigationDialog = () => {
  showNavigationDialog.value = false
}

// 确认导航
const confirmNavigation = () => {
  closeNavigationDialog()
  openGoogleMapsNavigation()
}

// 打开 Google Maps 导航
const openGoogleMapsNavigation = () => {
  try {
    console.log('🚗 开始 Google Maps 导航')

    const stationLat = stationDetail.value.latitude
    const stationLng = stationDetail.value.longitude
    const stationName = encodeURIComponent(stationDetail.value.name || '充电站')

    console.log('📍 目标坐标:', stationLat, stationLng)
    console.log('🌍 用户位置:', userLocation)

    // 构建 Google Maps 导航 URL
    let mapsUrl

    // 验证用户位置的准确性
    const hasValidUserLocation = userLocation.lat && userLocation.lng &&
                                 Math.abs(userLocation.lat) <= 90 &&
                                 Math.abs(userLocation.lng) <= 180

    if (hasValidUserLocation) {
      // 检查用户位置是否在合理范围内（避免使用错误的IP定位）
      const distanceToStation = calculateDistance(
        userLocation.lat, userLocation.lng,
        stationLat, stationLng
      )

      console.log('📏 用户到充电站距离:', distanceToStation + 'km')

      // 如果距离过远（>1000km），可能是定位错误，让Google Maps自动检测
      if (distanceToStation > 1000) {
        console.warn('⚠️ 检测到定位距离异常，使用Google Maps自动定位')
        mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${stationLat},${stationLng}&destination_place_id=${stationName}`
      } else {
        // 距离合理，使用检测到的位置
        mapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${userLocation.lat},${userLocation.lng}&destination=${stationLat},${stationLng}&destination_place_id=${stationName}`
      }
    } else {
      // 没有有效用户位置，让Google Maps自动检测
      console.log('🎯 使用Google Maps自动定位')
      mapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${stationLat},${stationLng}&destination_place_id=${stationName}`
    }

    console.log('🔗 导航URL:', mapsUrl)

    // 根据平台打开导航
    // #ifdef APP-PLUS
    // APP环境：使用 plus.runtime.openURL 打开外部应用
    if (typeof plus !== 'undefined' && plus.runtime) {
      plus.runtime.openURL(mapsUrl, (error) => {
        console.error('❌ 打开 Google Maps 失败:', error)
        uni.showToast({
          title: $t('toast.requestFailed') || 'Unable to open Google Maps',
          icon: 'none',
          duration: 2000
        })
      })
    } else {
      console.warn('⚠️ plus 对象不可用，尝试其他方式')
      fallbackOpenMaps(mapsUrl)
    }
    // #endif

    // #ifdef H5
    // H5环境：在新标签页打开
    window.open(mapsUrl, '_blank')
    // #endif

    // #ifdef MP
    // 小程序环境：提示用户复制链接
    uni.setClipboardData({
      data: mapsUrl,
      success: () => {
        uni.showToast({
          title: $t('toast.copied') || 'Copied',
          icon: 'success',
          duration: 2000
        })
      }
    })
    // #endif

  } catch (error) {
    console.error('❌ 导航功能出错:', error)
    uni.showToast({
      title: $t('toast.requestFailed') || 'Navigation is temporarily unavailable',
      icon: 'none',
      duration: 2000
    })
  }
}

// 备用打开地图方案
const fallbackOpenMaps = (url) => {
  try {
    // 尝试使用 uni.navigateTo 打开 webview
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(url)}`,
      fail: () => {
        // 如果失败，提示用户复制链接
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showToast({
              title: $t('toast.copied') || 'Copied',
              icon: 'success',
              duration: 3000
            })
          }
        })
      }
    })
  } catch (error) {
    console.error('❌ 备用方案也失败:', error)
  }
}
</script>

<style lang="scss" scoped>
.station-detail {
  min-height: 100vh;
  background: #F8F8F8;
  display: flex;
  flex-direction: column;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: #fff;
  box-shadow: 0 2rpx 12px rgba(0, 0, 0, 0.04);
}

.status-bar {
  background-color: #fff;
  width: 100%;
}

.header {
  background: #fff;
  width: 100%;

  .header-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    position: relative;
    padding: 0 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .back-btn {
      width: 88rpx;
      height: 88rpx;
      display: flex;
      align-items: center;

      .iconfont {
        font-size: 40rpx;
        color: #333;
      }
    }

    .title {
      position: absolute;
      left: 0;
      right: 0;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      pointer-events: none;
    }

    .favorite-btn {
      position: absolute;
      right: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .favorite-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }
}

.content-scroll {
  flex: 1;
  /* 不再使用CSS变量 */
  /* height: calc(100vh - var(--status-bar-height, 0px) - 88rpx); */
  /* margin-top: calc(var(--status-bar-height, 0px) + 88rpx); */
}

// 卡片圆角和阴影
.station-info-card,
.price-card,
.parking-fee-card,
.charging-pile-section,
.station-info-section {
  border-radius: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

/* 站点轮播图 */
.station-image-container {
  width: 100%;
  height: 400rpx;
  position: relative;
  overflow: hidden;

  .station-swiper {
    width: 100%;
    height: 100%;

    swiper-item {
      width: 100%;
      height: 100%;
    }
  }

  .station-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    /* 确保图片覆盖整个容器且不变形 */
    object-position: center;
    /* 居中显示图片 */
    cursor: pointer;
    /* 鼠标悬停时显示手型 */
    transition: transform 0.2s ease;
    /* 添加过渡动画 */

    &:active {
      transform: scale(0.98);
      /* 点击时轻微缩放效果 */
    }
  }

  /* 无图片占位符样式 */
  .no-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .placeholder-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      z-index: 1;
      padding: 40rpx;

      .placeholder-icon {
        width: 160rpx;
        height: 160rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
        font-size: 72rpx;
        color: #8f9bb3;
        .icon-image{
          font-size:90rpx;
        }
      }

      .placeholder-text {
        font-size: 32rpx;
        color: #2e3a59;
        font-weight: 600;
        margin-bottom: 8rpx;
        line-height: 1.4;
      }

      .placeholder-subtitle {
        font-size: 24rpx;
        color: #8f9bb3;
        line-height: 1.3;
      }
    }
  }
}

/* 站点基本信息卡片 */
.station-info-card {
  padding: 32rpx;
  background: #FFFFFF;
  margin-bottom: 16rpx;
  border-radius: 35rpx 35rpx 0 0;

  .station-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }

  .time-charge-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
  }

  .station-hours {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #666;

    .time-icon {
      width: 28rpx;
      height: 28rpx;
      margin-right: 8rpx;
    }
  }

  .last-charge-info {
    font-size: 28rpx;
    color: #FF5B57;
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-bottom: 24rpx;

    .tag {
      padding: 8rpx 16rpx;
      background: #F5F5F5;
      border-radius: 8rpx;
      font-size: 24rpx;
      color: #666;
    }
  }

  .address-container {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .address-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      line-height: 1.5;
    }

    .distance-container {
      display: flex;
      align-items: center;
      margin-left: 16rpx;

      .map-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }

      .distance {
        font-size: 28rpx;
        color: #666;
      }
    }
  }
}

/* 充电桩状态卡片 */
.status-card {
  display: flex;
  width: 100%;
  height: 150rpx;
  padding: 32rpx 30rpx;
  box-sizing: border-box;
  background: #FFFFFF;
  margin: 16rpx 0;
  border-radius: 20rpx;

  .status-left {
    flex: 3;
    background-color: #1ED69E;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10rpx;

    .status-text {
      font-size: 32rpx;
      font-weight: 500;
      color: #FFFFFF;

      &.available {
        color: #FFFFFF;
      }

      &.occupied {
        color: #FFFFFF;
      }

      &.offline {
        color: #FFFFFF;
      }
    }
  }

  .status-right {
    flex: 1;
    background-color: #F4F4F4;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .status-count {
      font-size: 32rpx;
      color: #333;
      font-weight: 600;
    }
  }
}

/* 价格卡片 */
.price-card {
  padding: 32rpx;
  background: #FFFFFF;
  margin-bottom: 16rpx;

  .period-info {
    display: flex;
    align-items: center;
    margin-bottom: 8rpx;

    .period-label {
      font-size: 32rpx;
      color: #333;
      font-weight: 600;
      margin-right: 16rpx;
    }

    .period-time {
      font-size: 28rpx;
      color: #666;
    }
  }

  .price-info {
    margin-bottom: 24rpx;

    .regular-price {
      display: flex;
      align-items: baseline;

      .price-amount {
        font-size: 48rpx;
        font-weight: 600;
        color: #FF0000;
      }

      .price-unit {
        font-size: 28rpx;
        color: #FF0000;
        margin-left: 8rpx;
      }
    }
  }

  /* 会员价格容器 */
  .membership-price-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16rpx 24rpx;
    background: linear-gradient(to right, #FEF4E5, #F1CC8B);
    margin-bottom: 24rpx;
    border-radius: 15rpx;

    .membership-price {
      display: flex;
      align-items: center;

      .vip-icon {
        width: 32rpx;
        height: 20rpx;
        margin-right: 8rpx;
      }

      .membership-label {
        font-size: 28rpx;
        color: #B8860B;
        margin-right: 16rpx;
      }

      .membership-amount {
        font-size: 28rpx;
        color: #B8860B;
        font-weight: 600;
        margin-right: 4rpx;
      }

      .membership-unit {
        font-size: 28rpx;
        color: #B8860B;
      }
    }

    .activate-btn {
      padding: 16rpx 24rpx;
      background: linear-gradient(to right, #FEEDD1, #FDDAB1);
      border-radius: 0 8rpx 8rpx 0;
      display: flex;
      align-items: center;
      margin: -16rpx -24rpx -16rpx 0;
      border-radius: 15rpx;
      cursor: pointer;

      text {
        font-size: 24rpx;
        color: #2F2F2F;
      }
    }
  }

  .next-period-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .next-period-info {
      display: flex;
      align-items: center;

      .next-period-label {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-right: 16rpx;
      }

      .next-period-time {
        font-size: 28rpx;
        color: #666;
      }
    }

    .next-period-price {
      font-size: 32rpx;
      color: #333;
      display: flex;
      align-items: baseline;

      text {
        &:first-child {
          font-weight: 600;
        }

        &:last-child {
          font-size: 24rpx;
          margin-left: 4rpx;
        }
      }
    }
  }

  .price-note {
    display: flex;
    align-items: flex-start;
    margin-bottom: 24rpx;

    .note-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32rpx;
      height: 32rpx;
      background: #FF5B57;
      color: #FFFFFF;
      border-radius: 50%;
      font-size: 24rpx;
      margin-right: 8rpx;
    }

    .note-text {
      flex: 1;
      font-size: 24rpx;
      color: #FF5B57;
      line-height: 1.4;
    }
  }

  .price-details-link {
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1rpx solid #F0F0F0;
    padding-top: 16rpx;

    text {
      font-size: 28rpx;
      color: #666;
    }

    .arrow-icon {
      font-size: 28rpx;
      color: #666;
      margin-left: 8rpx;
    }
  }
}

/* 停车费用卡片 */
.parking-fee-card {
  padding: 32rpx;
  background: #FFFFFF;
  margin-bottom: 16rpx;

  .parking-fee-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .parking-icon-container {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background: #333;
      border-radius: 8rpx;
      margin-right: 16rpx;

      .parking-icon {
        font-size: 32rpx;
        color: #FFFFFF;
        font-weight: 600;
      }
    }

    .parking-fee-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .parking-fee-content {
    padding-left: 64rpx;

    text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }
}

/* 充电桩列表部分 */
.charging-pile-section {
  background: #fff;
  padding: 32rpx;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
    }

    .more-link {
      display: flex;
      align-items: center;

      text {
        color: #999999;
        font-size: 28rpx;

        &.arrow-icon {
          margin-left: 8rpx;
        }
      }
    }
  }

  .pile-filter-tabs {
    display: flex;
    margin-bottom: 24rpx;
    gap: 16rpx;

    .filter-tab {
      padding: 12rpx 24rpx;
      background: #F4F4F4;
      border-radius: 32rpx;
      font-size: 28rpx;
      color: #666666;

      &.active {
        background: #E5F3FF;
        color: #0088FF;
      }
    }
  }

  .pile-list {
    display: flex;
    flex-direction: column;
    gap: 5rpx;

    .pile-item {
      display: flex;
      padding: 20rpx 32rpx;
      background: #FFFFFF;
      border-radius: 16rpx;

      .pile-status {
        width: 100rpx;
        height: 95rpx;
        padding: 0;
        margin-right: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .pile-icon {
          width: 95rpx;
          height: 95rpx;
        }


      }

      .pile-info {
        flex: 1;

        .pile-code-row,
        .pile-power-row,
        .pile-status-row {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .info-label {
            color: #999999;
            font-size: 28rpx;
            margin-right: 24rpx;
            min-width: 80rpx;
          }

          .info-value {
            color: #333333;
            font-size: 28rpx;
            flex: 1;
          }

          .copy-icon {
            width: 30rpx;
            height: 30rpx;
            margin-left: 16rpx;
          }
        }

        .pile-status-row {
          .status-text {
            font-weight: 600;
            font-size: 28rpx;

            // 根据状态设置不同颜色 - 只改变文字颜色
            &.status-00 { color: #8A8A8A; } // 离线 - 灰色
            &.status-01 { color: #E53E3E; } // 故障 - 红色
            &.status-02 { color: #38A169; } // 空闲 - 绿色
            &.status-03 { color: #3182CE; } // 充电中 - 蓝色
            &.status-04 { color: #DD6B20; } // 已停止 - 橙色
            &.status-05 { color: #805AD5; } // 已插枪 - 紫色
          }
        }
      }
    }
  }
}

/* 站点信息部分 */
.station-info-section {
  padding: 32rpx;
  background: #FFFFFF;
  margin-bottom: 16rpx;

  .section-header {
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .info-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .info-item {
      display: flex;

      .info-label {
        width: 240rpx;
        font-size: 28rpx;
        color: #999;
      }

      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }
    }
  }
}

/* 底部空白 */
.bottom-space {
  height: 120rpx;
}

/* 底部固定按钮 */
.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  background: #FFFFFF;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 200;

  .customer-service-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    border: 1rpx solid #F0F0F0;
    border-radius: 50%;
    margin-right: 24rpx;
    background: #fff;

    .cs-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .charge-btn {
    flex: 1;
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FF5B57;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(255, 91, 87, 0.12);

    .scan-icon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 12rpx;
    }

    text {
      font-size: 30rpx;
      color: #FFFFFF;
      font-weight: 600;
    }
  }
}

/* 价格详情弹出层 */
.price-details-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .popup-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    touch-action: none;
    /* 禁止触摸事件 */
  }

  .popup-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #FFFFFF;
    border-radius: 24rpx 24rpx 0 0;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
    max-height: 85vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    /* 增加iOS流畅滚动 */
    overscroll-behavior: contain;
    /* 防止滚动穿透 */
    touch-action: pan-y;
    /* 只允许垂直滚动 */

    &.popup-show {
      transform: translateY(0);
    }

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx;
      border-bottom: 1rpx solid #EEEEEE;
      position: sticky;
      top: 0;
      background: #FFFFFF;
      z-index: 2;

      .popup-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        padding: 8rpx;

        .close-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    .tariff-table {
      padding: 0 32rpx;

      .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16rpx 32rpx;
        margin-bottom: 16rpx;

        .header-period {
          width: 20%;
          font-size: 24rpx;
          color: #666;
          font-weight: 600;
          text-align: center;
        }

        .header-type {
          width: 20%;
          font-size: 24rpx;
          color: #666;
          font-weight: 600;
          text-align: center;
        }

        .header-tariff {
          width: 20%;
          font-size: 24rpx;
          color: #666;
          font-weight: 600;
          text-align: center;
        }

        .header-charge {
          width: 20%;
          font-size: 24rpx;
          color: #666;
          font-weight: 600;
          text-align: center;
        }

        .header-fee {
          width: 20%;
          font-size: 24rpx;
          color: #666;
          font-weight: 600;
          text-align: center;
        }
      }

      .table-content {
        .table-row {
          position: relative;
          margin: 16rpx 0;
          padding: 16rpx 32rpx;
          background: #FFFFFF;
          border-radius: 12rpx;
          border: 2rpx solid #E5E5E5;
          cursor: pointer;

          &.current {
            background: #E5F3FF;
            border: 2rpx solid #0088FF;
          }

          .current-tag {
            position: absolute;
            top: -12rpx;
            left: 16rpx;
            background: #0088FF;
            color: white;
            font-size: 24rpx;
            padding: 4rpx 12rpx;
            border-radius: 8rpx;
            display: none;

            .current & {
              display: block;
            }
          }

          .price-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .period {
              width: 20%;
              font-size: 28rpx;
              color: #333;
              text-align: center;
            }

            .type {
              width: 20%;
              font-size: 28rpx;
              color: #333;
              text-align: center;
            }

            .price-value {
              width: 20%;
              font-size: 28rpx;
              color: #333;
              text-align: center;
            }

            .price-detail {
              width: 20%;
              font-size: 28rpx;
              color: #999;
              text-align: center;
            }
          }
        }
      }
    }
  }
}

/* 充电桩列表弹窗 */
.pile-list-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;

  .popup-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    touch-action: none;
    /* 禁止触摸事件 */
  }

  .popup-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background: #FFFFFF;
    border-radius: 24rpx 24rpx 0 0;
    max-height: 85vh;
    transform: translateY(100%);
    transition: transform 0.3s ease-out;
    -webkit-overflow-scrolling: touch;
    /* 增加iOS流畅滚动 */
    overscroll-behavior: contain;
    /* 防止滚动穿透 */

    &.popup-show {
      transform: translateY(0);
    }

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx;
      position: sticky;
      top: 0;
      background: #FFFFFF;
      z-index: 2;

      .popup-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .close-icon {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    .popup-filter-tabs {
      display: flex;
      padding: 16rpx 32rpx;
      overflow-x: auto;
      gap: 16rpx;

      .filter-tab {
        padding: 12rpx 24rpx;
        background: #F5F5F5;
        border-radius: 40rpx;
        margin-right: 0;
        white-space: nowrap;
        flex-shrink: 0;

        text {
          font-size: 28rpx;
          color: #666;
          white-space: nowrap;
          display: inline-block;
        }

        &.active {
          background: #E5F3FF;

          text {
            color: #0088FF;
          }
        }
      }
    }

    .popup-pile-list {
      padding: 0 32rpx;
      max-height: 65vh;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      /* 增加iOS流畅滚动 */
      overscroll-behavior: contain;
      /* 防止滚动穿透 */
      touch-action: pan-y;
      /* 只允许垂直滚动 */

      .pile-item {
        display: flex;
        padding: 5rpx 24rpx 15rpx 0rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        margin-bottom: 15rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

        &:last-child {
          margin-bottom: 0;
        }

        .pile-status {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 120rpx;
          height: 130rpx;
          padding: 15rpx 24rpx;
          border-radius: 16rpx;
          margin-right: 10rpx;

          .pile-icon {
            width: 100%;
            height: 130rpx;
          }

          text {
            display: none;
          }


        }

        .pile-info {
          flex: 1;
          padding: 15rpx 0;
          box-sizing: border-box;

          .pile-code-row,
          .pile-power-row,
          .pile-status-row {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;
            padding: 12rpx;
            box-sizing: border-box;

            &:last-child {
              margin-bottom: 0;
            }

            .info-label {
              width: 90rpx;
              font-size: 26rpx;
              color: #999999;
            }

            .info-value {
              flex: 1;
              font-size: 26rpx;
              color: #333333;
              font-weight: 500;
            }

            .copy-icon {
              width: 32rpx;
              height: 32rpx;
              cursor: pointer;
              margin-right: 30rpx;
            }
          }

          .pile-status-row {
            .status-text {
              font-weight: 600;
              font-size: 26rpx;

              // 根据状态设置不同颜色 - 只改变文字颜色
              &.status-00 { color: #8A8A8A; } // 离线 - 灰色
              &.status-01 { color: #E53E3E; } // 故障 - 红色
              &.status-02 { color: #38A169; } // 空闲 - 绿色
              &.status-03 { color: #3182CE; } // 充电中 - 蓝色
              &.status-04 { color: #DD6B20; } // 已停止 - 橙色
              &.status-05 { color: #805AD5; } // 已插枪 - 紫色
            }
          }
        }
      }
    }
  }
}

/* 导航弹窗样式 */
.navigation-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  animation: fadeIn 0.2s ease-out;
}

.navigation-modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #ffffff;
  border-radius: 16rpx;
  width: calc(100vw - 80rpx);
  max-width: 560rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: zoomBounceIn 0.25s cubic-bezier(0.2, 0.8, 0.2, 1);
  will-change: transform, opacity;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f8f8;
}

.icon-close {
  font-size: 24rpx;
  color: #666666;
}

.modal-content {
  padding: 32rpx;
}

.station-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.description-text {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.modal-actions {
  display: flex;
  padding: 0 32rpx 32rpx;
  gap: 16rpx;
}

.action-button {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-button {
  background: #f8f8f8;
  color: #666666;
}

.confirm-button {
  background: #007AFF;
  color: #ffffff;
}

.button-text {
  font-size: 28rpx;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 无充电桩提示样式 */
.no-piles-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;

  .no-piles-text {
    font-size: 28rpx;
    color: #8f9bb3;
    line-height: 1.4;
  }
}

/* 高级充电弹窗样式 - 优化动画性能 */
.charging-popup-premium {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1500;
  display: flex;
  align-items: flex-end; /* 底部弹出 */
  justify-content: center;
  padding-bottom: env(safe-area-inset-bottom);
  animation: fadeInFast 0.15s ease-out;
  will-change: opacity;
}

.popup-backdrop-premium {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  will-change: opacity;
}

.popup-content-premium {
  position: relative;
  width: 100%;
  max-width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafb 100%);
  border-radius: 24rpx 24rpx 0 0; /* 顶部圆角，底部直角 */
  padding: 0;
  box-shadow: 0 -8rpx 24rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.2s ease-out; /* 使用已有向上动画 */
  overflow: hidden;
  will-change: transform, opacity;
  transform: translateZ(0);
}



.status-icon-area-premium {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0 20rpx;
}

.status-icon-container-premium {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;

  &.error {
    background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%);
    box-shadow: 0 8rpx 32rpx rgba(231, 76, 60, 0.3);
  }

  &.success {
    background: linear-gradient(135deg, #27AE60 0%, #229954 100%);
    box-shadow: 0 8rpx 32rpx rgba(39, 174, 96, 0.3);
  }
}

.app-logo-container {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-logo {
  width: 70rpx;
  height: 70rpx;
}

.status-icon-text {
  font-size: 60rpx;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);

  &.success {
    font-size: 64rpx;
  }
}

.loading-ring {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 160rpx;
  height: 160rpx;
  border: 4rpx solid transparent;
  border-top: 4rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spinFast 1s linear infinite;
  will-change: transform;
  transform: translateZ(0);
}

.status-glow-premium {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(74, 144, 226, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%) translateZ(0);
  animation: pulseSimple 2s ease-in-out infinite;
  z-index: 1;
  will-change: transform, opacity;

  &.error {
    background: radial-gradient(circle, rgba(231, 76, 60, 0.2) 0%, transparent 70%);
  }

  &.success {
    background: radial-gradient(circle, rgba(39, 174, 96, 0.2) 0%, transparent 70%);
  }
}

.popup-header-premium {
  text-align: center;
  padding: 20rpx 40rpx 30rpx;
}

.header-title-premium {
  font-size: 40rpx;
  font-weight: 700;
  color: #2C3E50;
  line-height: 1.3;
  margin-bottom: 12rpx;
  display: block;

  &.error {
    color: #E74C3C;
  }
}

.header-subtitle-premium {
  font-size: 28rpx;
  color: #7F8C8D;
  line-height: 1.4;
  display: block;
}

.error-message-premium {
  font-size: 28rpx;
  color: #E74C3C;
  line-height: 1.4;
  display: block;
  margin-top: 8rpx;
}

.charging-steps-premium {
  padding: 0 40rpx 30rpx;
}

.progress-track-premium {
  width: 100%;
  height: 8rpx;
  background: linear-gradient(90deg, #F8F9FA 0%, #E9ECEF 100%);
  border-radius: 4rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
}

.progress-fill-premium {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 4rpx;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 20rpx rgba(74, 144, 226, 0.4);
}

.steps-container-premium {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.step-item-premium {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 16rpx 0;
  transition: all 0.3s ease;

  &.completed .step-icon-premium {
    background: linear-gradient(135deg, #27AE60 0%, #229954 100%);
    border-color: #27AE60;
    box-shadow: 0 4rpx 16rpx rgba(39, 174, 96, 0.3);
  }

  &.active .step-icon-premium {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    border-color: #4A90E2;
    animation: pulseStep 1.5s ease-in-out infinite;
    box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.4);
    will-change: transform;
  }

  &.error .step-icon-premium {
    background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%);
    border-color: #E74C3C;
    box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
  }

  &.pending .step-icon-premium {
    background: #F8F9FA;
    border-color: #DEE2E6;
    box-shadow: none;
  }
}

.step-icon-premium {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #DEE2E6;
  background: #F8F9FA;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.step-number {
  font-size: 22rpx;
  font-weight: 700;
  color: #6C757D;
}

.step-item-premium.completed .step-number,
.step-item-premium.active .step-number,
.step-item-premium.error .step-number {
  color: #ffffff;
}

.step-check {
  font-size: 26rpx;
  color: #ffffff;
  font-weight: 700;
}

.step-loading {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.step-content-premium {
  flex: 1;
  margin-left: 4rpx;
}

.step-text-premium {
  font-size: 30rpx;
  font-weight: 600;
  color: #2C3E50;
  line-height: 1.3;
  display: block;
  margin-bottom: 4rpx;
}

.step-desc-premium {
  font-size: 24rpx;
  color: #7F8C8D;
  line-height: 1.3;
  display: block;
}

.popup-footer-premium {
  padding: 20rpx 40rpx 40rpx;
}

.error-actions-premium {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
}

.close-button-premium {
  background: linear-gradient(135deg, #E74C3C 0%, #C0392B 100%);
  border-radius: 28rpx;
  padding: 16rpx 40rpx;
  box-shadow: 0 6rpx 20rpx rgba(231, 76, 60, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.4);
  }
}

.button-text-premium {
  font-size: 30rpx;
  font-weight: 600;
  color: #ffffff;
}

.bottom-tips-premium {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
  border-radius: 16rpx;
  border: 1rpx solid #DEE2E6;
}

.tip-icon-premium {
  font-size: 28rpx;
  flex-shrink: 0;
}

.tip-text-premium {
  font-size: 26rpx;
  color: #6C757D;
  line-height: 1.4;
  text-align: center;
}

.error-tips-premium {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #FDEDEC 0%, #F8D7DA 100%);
  border-radius: 16rpx;
  border-left: 4rpx solid #E74C3C;
}

.error-icon-tip-premium {
  font-size: 28rpx;
  flex-shrink: 0;
}

.error-tip-text-premium {
  font-size: 26rpx;
  color: #C0392B;
  line-height: 1.4;
  flex: 1;
}

@keyframes fadeInFast {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes popInCenter {
  from {
    opacity: 0;
    transform: scale(0.8) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

@keyframes pulseSimple {
  0%, 100% {
    transform: translate(-50%, -50%) translateZ(0) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translate(-50%, -50%) translateZ(0) scale(1.05);
    opacity: 0.2;
  }
}

@keyframes spinFast {
  from {
    transform: rotate(0deg) translateZ(0);
  }
  to {
    transform: rotate(360deg) translateZ(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg) translateZ(0);
  }
  to {
    transform: rotate(360deg) translateZ(0);
  }
}
@keyframes zoomBounceIn {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.85);
  }
  60% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.03);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes pulseStep {
  0%, 100% {
    transform: scale(1) translateZ(0);
  }
  50% {
    transform: scale(1.03) translateZ(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1) translateZ(0);
  }
  50% {
    transform: scale(1.05) translateZ(0);
  }
}

/* 骨架屏样式 */
.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.skeleton-pile-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.skeleton-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f5f5f5;
}

.skeleton-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

.skeleton-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.skeleton-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.skeleton-label {
  width: 80rpx;
  height: 28rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

.skeleton-value {
  flex: 1;
  height: 32rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

.skeleton-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s infinite;
}

@keyframes skeletonLoading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>